# Pattern Templates

This directory contains reusable pattern templates for both infrastructure (<PERSON><PERSON><PERSON>k, Docker Compose) and application-level (TypeScript) patterns.

## Structure

- `traefik/` — Traefik/Docker Compose label sets for common patterns (circuit breaker, rate limiting, retries, etc)
- `code/` — TypeScript implementations of common resilience and integration patterns (circuit breaker, retry, timeout, bulkhead, idempotency, outbox, etc)

## Usage

- **Traefik patterns:** Import or copy label sets into your Docker Compose files to apply middleware and routing patterns.
- **Code patterns:** Import TypeScript utilities into your services for in-process resilience and integration logic.

## Adding New Patterns

- Add new Traefik label sets to `traefik/` as `.ts` or `.yml` files.
- Add new code patterns to `code/` as `.ts` files.
- Update this README with a description of the new pattern and usage example. 

## Tasks

### Extracted Tasks

- [ ] `traefik/` — Traefik/Docker Compose label sets for common patterns (circuit breaker, rate limiting, retries, etc) - M1
- [ ] `code/` — TypeScript implementations of common resilience and integration patterns (circuit breaker, retry, timeout, bulkhead, idempotency, outbox, etc) - M2
- [ ] **Traefik patterns:** Import or copy label sets into your Docker Compose files to apply middleware and routing patterns. - M3
- [ ] **Code patterns:** Import TypeScript utilities into your services for in-process resilience and integration logic. - M4
- [ ] Add new Traefik label sets to `traefik/` as `.ts` or `.yml` files. - M5
- [ ] Add new code patterns to `code/` as `.ts` files. - M6
- [ ] Update this README with a description of the new pattern and usage example. - M7

### General Tasks

- [ ] Review and update content - M1
- [ ] Add missing information - M2
- [ ] Improve structure - M3
- [ ] Add examples - M4
- [ ] Validate accuracy - M5

