# Docker Container Testing Integration for Beauty CRM Backend Services

This directory contains comprehensive Docker testing infrastructure specifically designed for Beauty CRM backend services that use Bun, workspace configurations, and file path dependencies.

## Overview

The testing integration provides three levels of validation:

1. **Container Structure Tests** - Validate container structure, dependencies, and imports
2. **Runtime Behavior Tests** - Test actual service functionality in containers
3. **Transitive Dependency Tests** - Comprehensive validation of Bun workspace dependency resolution

## Key Features

- **Bun-specific dependency validation** - Tests file path dependencies and workspace configurations
- **Beauty CRM platform integration** - Validates @beauty-crm/* packages and platform components
- **Multi-platform support** - Works on macOS and Linux with automatic Docker host detection
- **Fallback testing** - Simplified validation when comprehensive tests fail
- **Automated tool installation** - Downloads and configures testing tools automatically

## Files Structure

```
shared-platform-engineering/docker-templates/
├── build-backend-service.sh              # Main build script with integrated testing
├── tests/
│   ├── container-structure-base.yaml     # Base container structure validation
│   ├── container-structure-service.template.yaml  # Service-specific template
│   ├── bun-deps-validation.yaml         # Simplified Bun dependency validation
│   ├── transitive-deps-test.yaml        # Comprehensive transitive dependency test
│   ├── runtime-tests-base.bats          # Base runtime behavior tests
│   └── README.md                         # Testing documentation
└── README.md                             # This file
```

## Usage Examples

### Basic Container Testing
```bash
# Build with container structure tests
./shared-platform-engineering/docker-templates/build-backend-service.sh \
  services/staff/staff-management-backend --container-tests
```

### Comprehensive Dependency Testing
```bash
# Run comprehensive transitive dependency validation
./shared-platform-engineering/docker-templates/build-backend-service.sh \
  services/staff/staff-management-backend --transitive-deps
```

### All Tests
```bash
# Run all available tests (dependency, container, runtime)
./shared-platform-engineering/docker-templates/build-backend-service.sh \
  services/staff/staff-management-backend --all-tests
```

### Development Testing
```bash
# Build development target with container tests
./shared-platform-engineering/docker-templates/build-backend-service.sh \
  services/staff/staff-management-backend --target development --container-tests
```

## Test Configurations

### Container Structure Tests

#### `container-structure-base.yaml`
- Validates basic container structure and dependencies
- Tests file existence for core platform components
- Validates command execution and import resolution
- Checks metadata like exposed ports and user configuration

#### `bun-deps-validation.yaml`
- Simplified dependency validation for Bun runtime
- Tests core framework imports (Hono, NATS, Zod, UUID)
- Validates Bun package manager functionality
- Fallback test when comprehensive validation fails

#### `transitive-deps-test.yaml`
- Comprehensive transitive dependency validation
- Tests file path dependencies and symlink resolution
- Validates workspace configuration and package.json files
- Tests platform-specific imports and functionality

### Runtime Behavior Tests

#### `runtime-tests-base.bats`
- Tests actual container runtime behavior
- Validates service startup and health checks
- Tests API endpoints and service functionality
- Verifies environment variable configuration

## Build Script Options

The `build-backend-service.sh` script supports the following testing options:

- `--container-tests` - Run container structure tests only
- `--runtime-tests` - Run runtime behavior tests only
- `--transitive-deps` - Run comprehensive transitive dependency validation
- `--all-tests` - Run all available tests (dependency, container, runtime)
- `--target <target>` - Specify build target (development/production)
- `--verbose` - Enable verbose output for debugging

## Testing Tools

### Container Structure Test
- **Automatically installed** on first run
- **Multi-platform support** (macOS/Linux)
- **Docker host detection** for macOS Docker Desktop
- **Version**: v1.16.0

### Bats (Bash Automated Testing System)
- **Runtime behavior testing** framework
- **Automatic installation** via package managers
- **Cross-platform support**

## Expected Test Results

### Successful Container Structure Test
```
=== RUN: Hono framework import test
--- PASS
duration: 293ms
stdout: Hono type: function

=== RUN: NATS client import test
--- PASS
duration: 156ms
stdout: NATS connect type: function
```

### Failed Test Example
```
=== RUN: Platform lifecycle import test
--- FAIL
duration: 89ms
Error: Cannot find module '@beauty-crm/platform-computing-lifecycle'
```

## Troubleshooting

### Common Issues

#### 1. Docker Socket Connection Errors
```
Error: dial unix /var/run/docker.sock: connect: no such file or directory
```
**Solution**: The script automatically detects macOS Docker Desktop and sets the correct socket path.

#### 2. Container Structure Test Installation Fails
```
Failed to install container-structure-test
```
**Solution**: Check internet connection and ensure curl is available. The tool will be downloaded to `.local/bin/`.

#### 3. Missing TypeScript Definitions
```
ERROR: dist/index.d.ts not found
```
**Solution**: This is now treated as a warning. The validation script has been updated to make TypeScript definitions optional for runtime.

#### 4. Bun Dependency Resolution Errors
```
error: @beauty-crm/platform-* failed to resolve
```
**Solution**: Ensure all shared libraries are built before running tests:
```bash
cd shared-platform-engineering/platform-computing-lifecycle && bun run build
cd shared-platform-engineering/platform-db-client && bun run build
```

### Debug Commands

```bash
# Test container structure manually
.local/bin/container-structure-test test \
  --image staff_staff-management-backend:production \
  --config shared-platform-engineering/docker-templates/tests/bun-deps-validation.yaml

# Check Docker images
docker images | grep staff

# Inspect container
docker run -it staff_staff-management-backend:production /bin/sh
```

## Creating Service-Specific Tests

### Container Structure Tests

To create a service-specific container structure test:

1. **Copy the template**:
   ```bash
   cp shared-platform-engineering/docker-templates/tests/container-structure-service.template.yaml \
      shared-platform-engineering/docker-templates/tests/container-structure-staff.yaml
   ```

2. **Customize for your service**:
   - Update service-specific file paths
   - Add service-specific dependencies
   - Modify command tests for your service's functionality

### Runtime Behavior Tests

To create service-specific runtime tests:

1. **Copy the base tests**:
   ```bash
   cp shared-platform-engineering/docker-templates/tests/runtime-tests-base.bats \
      shared-platform-engineering/docker-templates/tests/runtime-tests-staff.bats
   ```

2. **Add service-specific tests**:
   ```bash
   @test "Staff service health check" {
     run docker run --rm $TEST_IMAGE curl -f http://localhost:4000/health
     [ "$status" -eq 0 ]
   }
   ```

## Integration with CI/CD

### GitHub Actions Example

```yaml
name: Backend Service Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Build and Test Backend Service
        run: |
          ./shared-platform-engineering/docker-templates/build-backend-service.sh \
            services/staff/staff-management-backend \
            --all-tests
```

## Test Validation Criteria

### Container Structure Tests
- ✅ **File Existence**: Core dependencies and platform components exist
- ✅ **Command Execution**: Bun runtime and package manager functionality
- ✅ **Import Resolution**: All @beauty-crm/* and framework dependencies importable
- ✅ **Metadata Validation**: Correct user, ports, and environment variables

### Transitive Dependency Tests
- ✅ **Workspace Structure**: Proper file path dependency resolution
- ✅ **Symlink Validation**: File path dependencies create working symlinks
- ✅ **Package.json Integrity**: All dependency package.json files valid
- ✅ **Transitive Imports**: Dependencies of dependencies are accessible

### Runtime Behavior Tests
- ✅ **Service Startup**: Container starts successfully and stays running
- ✅ **Health Checks**: Service responds to health check endpoints
- ✅ **API Functionality**: Core service endpoints return expected responses
- ✅ **Environment Config**: Service reads configuration correctly

## Performance Considerations

### Test Execution Times
- **Container Structure Tests**: ~30-60 seconds
- **Runtime Behavior Tests**: ~60-120 seconds
- **Transitive Dependency Tests**: ~90-180 seconds

### Optimization Tips
- Use `--container-tests` for faster feedback during development
- Run `--all-tests` only in CI/CD or before releases
- Use `--transitive-deps` when debugging dependency issues

## Supported Architectures

- **macOS**: Intel (x86_64) and Apple Silicon (ARM64) via Docker Desktop
- **Linux**: x86_64 with Docker Engine
- **CI/CD**: GitHub Actions, GitLab CI, Jenkins with Docker support

## Version Compatibility

- **Container Structure Test**: v1.16.0+
- **Bats**: v1.8.0+
- **Docker**: v20.10.0+
- **Bun**: v1.2.15+

## Contributing

To add new test configurations:

1. Create test files in `tests/` directory
2. Follow existing naming conventions
3. Update build script integration if needed
4. Test with multiple services to ensure compatibility
5. Update documentation with new test descriptions

## Additional Resources

- [Container Structure Test Documentation](https://github.com/GoogleContainerTools/container-structure-test)
- [Bats Testing Framework](https://bats-core.readthedocs.io/)
- [Beauty CRM Platform Documentation](../platform-computing-lifecycle/README.md)


## Tasks

### Extracted Tasks

- [ ] **Bun-specific dependency validation** - Tests file path dependencies and workspace configurations - M1
- [ ] **Beauty CRM platform integration** - Validates @beauty-crm/* packages and platform components - M2
- [ ] **Multi-platform support** - Works on macOS and Linux with automatic Docker host detection - M3
- [ ] **Fallback testing** - Simplified validation when comprehensive tests fail - M4
- [ ] **Automated tool installation** - Downloads and configures testing tools automatically - M5
- [ ] Validates basic container structure and dependencies - M6
- [ ] Tests file existence for core platform components - M7
- [ ] Validates command execution and import resolution - M8
- [ ] Checks metadata like exposed ports and user configuration - M9
- [ ] Simplified dependency validation for Bun runtime - M10
- [ ] Tests core framework imports (Hono, NATS, Zod, UUID) - M11
- [ ] Validates Bun package manager functionality - M12
- [ ] Fallback test when comprehensive validation fails - M13
- [ ] Comprehensive transitive dependency validation - M14
- [ ] Tests file path dependencies and symlink resolution - M15
- [ ] Validates workspace configuration and package.json files - M16
- [ ] Tests platform-specific imports and functionality - M17
- [ ] Tests actual container runtime behavior - M18
- [ ] Validates service startup and health checks - M19
- [ ] Tests API endpoints and service functionality - M20
- [ ] Verifies environment variable configuration - M21
- [ ] `--container-tests` - Run container structure tests only - M22
- [ ] `--runtime-tests` - Run runtime behavior tests only - M23
- [ ] `--transitive-deps` - Run comprehensive transitive dependency validation - M24
- [ ] `--all-tests` - Run all available tests (dependency, container, runtime) - M25
- [ ] `--target <target>` - Specify build target (development/production) - M26
- [ ] `--verbose` - Enable verbose output for debugging - M27
- [ ] **Automatically installed** on first run - M28
- [ ] **Multi-platform support** (macOS/Linux) - M29
- [ ] **Docker host detection** for macOS Docker Desktop - M30
- [ ] **Version**: v1.16.0 - M31
- [ ] **Runtime behavior testing** framework - M32
- [ ] **Automatic installation** via package managers - M33
- [ ] **Cross-platform support** - M34
- [ ] Update service-specific file paths - M35
- [ ] Add service-specific dependencies - M36
- [ ] Modify command tests for your service's functionality - M37
- [ ] uses: actions/checkout@v3 - M38
- [ ] name: Build and Test Backend Service - M39
- [ ] ✅ **File Existence**: Core dependencies and platform components exist - M40
- [ ] ✅ **Command Execution**: Bun runtime and package manager functionality - M41
- [ ] ✅ **Import Resolution**: All @beauty-crm/* and framework dependencies importable - M42
- [ ] ✅ **Metadata Validation**: Correct user, ports, and environment variables - M43
- [ ] ✅ **Workspace Structure**: Proper file path dependency resolution - M44
- [ ] ✅ **Symlink Validation**: File path dependencies create working symlinks - M45
- [ ] ✅ **Package.json Integrity**: All dependency package.json files valid - M46
- [ ] ✅ **Transitive Imports**: Dependencies of dependencies are accessible - M47
- [ ] ✅ **Service Startup**: Container starts successfully and stays running - M48
- [ ] ✅ **Health Checks**: Service responds to health check endpoints - M49
- [ ] ✅ **API Functionality**: Core service endpoints return expected responses - M50
- [ ] ✅ **Environment Config**: Service reads configuration correctly - M51
- [ ] **Container Structure Tests**: ~30-60 seconds - M52
- [ ] **Runtime Behavior Tests**: ~60-120 seconds - M53
- [ ] **Transitive Dependency Tests**: ~90-180 seconds - M54
- [ ] Use `--container-tests` for faster feedback during development - M55
- [ ] Run `--all-tests` only in CI/CD or before releases - M56
- [ ] Use `--transitive-deps` when debugging dependency issues - M57
- [ ] **macOS**: Intel (x86_64) and Apple Silicon (ARM64) via Docker Desktop - M58
- [ ] **Linux**: x86_64 with Docker Engine - M59
- [ ] **CI/CD**: GitHub Actions, GitLab CI, Jenkins with Docker support - M60
- [ ] **Container Structure Test**: v1.16.0+ - M61
- [ ] **Bats**: v1.8.0+ - M62
- [ ] **Docker**: v20.10.0+ - M63
- [ ] **Bun**: v1.2.15+ - M64
- [ ] [Container Structure Test Documentation](https://github.com/GoogleContainerTools/container-structure-test) - M65
- [ ] [Bats Testing Framework](https://bats-core.readthedocs.io/) - M66
- [ ] [Beauty CRM Platform Documentation](../platform-computing-lifecycle/README.md) - M67

### General Tasks

- [ ] Review and update content - M1
- [ ] Add missing information - M2
- [ ] Improve structure - M3
- [ ] Add examples - M4
- [ ] Validate accuracy - M5

