# Docker Container Testing for Beauty CRM Backend Services

This directory contains comprehensive testing configurations and scripts for validating Docker containers built with the Beauty CRM backend service templates.

## 🧪 Testing Framework Overview

Our testing strategy uses a three-layer approach:

1. **Build-time Assertions** (Dockerfile) - Catch build failures early
2. **Container Structure Tests** - Validate container integrity and dependencies
3. **Runtime Behavior Tests** (Bats) - Validate service behavior and integration

## 📁 Test Files

### Container Structure Tests

- **`container-structure-base.yaml`** - Common tests for all backend services
- **`container-structure-staff.yaml`** - Staff service-specific tests
- **`container-structure-service.template.yaml`** - Template for creating new service tests

### Runtime Behavior Tests (Bats)

- **`runtime-tests-base.bats`** - Common runtime tests for all services
- **`runtime-tests-staff.bats`** - Staff-specific runtime tests

## 🚀 Quick Start

### Run All Tests for Staff Service

```bash
# Run all tests (dependency, container structure, and runtime)
./shared-platform-engineering/docker-templates/build-backend-service.sh \
  services/staff/staff-management-backend --all-tests

# Run only container structure tests
./shared-platform-engineering/docker-templates/build-backend-service.sh \
  services/staff/staff-management-backend --container-tests

# Run only runtime behavior tests
./shared-platform-engineering/docker-templates/build-backend-service.sh \
  services/staff/staff-management-backend --runtime-tests
```

### Manual Test Execution

```bash
# Container Structure Tests
container-structure-test test \
  --image staff_staff-management-backend:latest \
  --config shared-platform-engineering/docker-templates/tests/container-structure-base.yaml

# Runtime Behavior Tests
export TEST_IMAGE=staff_staff-management-backend:latest
bats shared-platform-engineering/docker-templates/tests/runtime-tests-base.bats
```

## 🔧 Test Configuration

### Container Structure Tests

Container Structure Tests validate:

- **File Existence**: Critical files and directories are present
- **File Content**: Configuration files contain expected values
- **Command Execution**: Runtime commands work correctly
- **Metadata**: Image metadata is properly configured

#### Base Tests (`container-structure-base.yaml`)

Common validations for all services:
- Platform dependencies are installed and built
- Node modules are properly resolved
- Bun runtime is functional
- System utilities (curl, jq) are available
- Non-root user is configured
- Environment variables are set

#### Service-Specific Tests

Extend base tests with service-specific validations:
- Service directory structure follows DDD patterns
- Service configuration files are valid
- Service-specific dependencies are available
- Lifecycle manifest is properly configured

### Runtime Behavior Tests (Bats)

Bats tests validate actual runtime behavior:

- **Container Startup**: Service starts successfully
- **Port Binding**: Service listens on correct ports
- **Health Checks**: Health endpoints respond appropriately
- **Graceful Shutdown**: Service handles SIGTERM correctly
- **Error Handling**: Service handles missing dependencies gracefully

## 📋 Creating Tests for New Services

### 1. Container Structure Tests

Copy and customize the template:

```bash
# Copy template
cp shared-platform-engineering/docker-templates/tests/container-structure-service.template.yaml \
   shared-platform-engineering/docker-templates/tests/container-structure-appointment.yaml

# Replace placeholders
sed -i 's/{{SERVICE_NAME}}/appointment/g' container-structure-appointment.yaml
sed -i 's/{{SERVICE_PATH}}/services\/appointment\/appointment-management-backend/g' container-structure-appointment.yaml
```

### 2. Runtime Behavior Tests

Create service-specific Bats tests:

```bash
# Copy base tests as starting point
cp shared-platform-engineering/docker-templates/tests/runtime-tests-staff.bats \
   shared-platform-engineering/docker-templates/tests/runtime-tests-appointment.bats

# Customize for your service
# - Update service names and paths
# - Add service-specific API endpoint tests
# - Add service-specific dependency validations
```

## 🔍 Test Categories

### Build-time Assertions (Dockerfile)

Already implemented in our Docker templates:
- Workspace structure validation
- Dependency installation verification
- Shared library build validation
- Prisma client generation
- Import resolution testing

### Container Structure Validation

**File Existence Tests:**
- Application directories (`/app`, `/app/services/[service]`)
- Platform dependencies (`shared-platform-engineering/`)
- Built artifacts (`dist/` directories)
- Node modules and symlinks
- Service configuration files

**File Content Tests:**
- Package.json contains correct dependencies
- Lifecycle manifest has proper configuration
- Service files import required modules

**Command Tests:**
- Bun runtime functionality
- Platform dependency imports
- Framework dependency imports
- File permissions and ownership

**Metadata Tests:**
- Environment variables
- Exposed ports
- Working directory
- User configuration

### Runtime Behavior Validation

**Container Lifecycle:**
- Successful startup
- Port binding and listening
- Graceful shutdown on SIGTERM

**Service Integration:**
- Health endpoint responses
- API endpoint accessibility
- Error handling for missing dependencies

**Environment Validation:**
- Environment variable handling
- File permissions at runtime
- Working directory correctness

## 🛠️ Troubleshooting

### Common Issues

**Container Structure Test Failures:**

1. **Missing files/directories**
   - Check if build completed successfully
   - Verify workspace structure in Dockerfile
   - Ensure shared libraries are built

2. **Import resolution failures**
   - Verify node_modules symlinks are correct
   - Check file path dependencies in package.json
   - Ensure all shared libraries have dist/ directories

3. **Permission issues**
   - Verify non-root user is created correctly
   - Check file ownership in Dockerfile
   - Ensure proper chown commands

**Runtime Test Failures:**

1. **Container won't start**
   - Check Docker image exists
   - Verify entry point configuration
   - Check for syntax errors in service code

2. **Port binding issues**
   - Ensure port is exposed in Dockerfile
   - Check if port is already in use
   - Verify service listens on 0.0.0.0

3. **Health check failures**
   - Service may need external dependencies (DB, NATS)
   - Check if health endpoint is implemented
   - Verify service startup time

### Debug Commands

```bash
# Check container structure test output
container-structure-test test --image [IMAGE] --config [CONFIG] --verbose

# Run single Bats test
bats --tap runtime-tests-base.bats --filter "container starts successfully"

# Debug container interactively
docker run -it --entrypoint /bin/sh [IMAGE]

# Check container logs
docker logs [CONTAINER_NAME]
```

## 📊 Test Results and Reporting

### Container Structure Test Output

```
====================================
=== Container Structure Test Results ===
====================================
✅ File Existence Tests: 15/15 passed
✅ File Content Tests: 8/8 passed  
✅ Command Tests: 12/12 passed
✅ Metadata Tests: 5/5 passed

Total: 40/40 tests passed
```

### Bats Test Output

```
✓ container starts successfully
✓ container uses non-root user
✓ bun runtime is available and functional
✓ platform dependencies are importable
✓ service starts and listens on correct port
✓ health endpoint responds correctly

6 tests, 0 failures
```

## 🔄 CI/CD Integration

### GitHub Actions Example

```yaml
- name: Build and Test Backend Service
  run: |
    ./shared-platform-engineering/docker-templates/build-backend-service.sh \
      services/staff/staff-management-backend \
      --all-tests --skip-deps
```

### Test Parallelization

```bash
# Run tests in parallel for faster CI
./build-backend-service.sh services/staff/staff-management-backend --container-tests &
./build-backend-service.sh services/appointment/appointment-management-backend --container-tests &
wait
```

## 📚 Additional Resources

- [Container Structure Test Documentation](https://github.com/GoogleContainerTools/container-structure-test)
- [Bats Testing Framework](https://github.com/bats-core/bats-core)
- [Beauty CRM Docker Templates](../README.md)
- [Platform Computing Lifecycle](../../platform-computing-lifecycle/README.md)
