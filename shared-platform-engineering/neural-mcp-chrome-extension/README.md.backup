# Beauty CRM Neural MCP Chrome Extension

This Chrome extension connects to the Neural Network Master Control Program (MCP) to monitor browser performance and track potential issues in Beauty CRM applications.

## Features

- **JavaScript Error Tracking**: Captures uncaught errors and exceptions
- **Network Request Monitoring**: Tracks API calls, response times, and failures
- **Performance Metrics**: Collects page load times and resource usage
- **Console Tracking**: Monitors console errors and warnings
- **Real-time Data**: Streams events to the MCP for neural network analysis

## Installation

### Development Mode

1. Clone the repository or navigate to the extension directory
2. Open Chrome and go to `chrome://extensions`
3. Enable "Developer mode" toggle in the top-right corner
4. Click "Load unpacked" and select the extension directory
5. The extension icon should appear in your Chrome toolbar

### Configuration

1. Click the extension icon in the toolbar to open the popup
2. Ensure the MCP endpoint is correctly set (default: `http://localhost:3100/api/events`)
3. Click "Test Connection" to verify connectivity
4. Enable or disable event reporting as needed

## Architecture

The extension consists of the following components:

- **Background Script**: Maintains connection with the MCP server and buffers events
- **Content Script**: Runs in the context of web pages to capture events and metrics
- **Popup Interface**: Provides configuration options and status indicators

## Data Collection

The extension collects the following data:

### JavaScript Errors
- Error message and stack trace
- URL where the error occurred
- Line and column numbers

### Performance Metrics
- Page load time
- DOM content loading time
- Resource loading statistics
- Memory usage (when available)

### Network Requests
- Request URL and method
- Response status code
- Request duration
- Success/failure status

## Privacy and Security

- Data is only sent to the configured MCP endpoint
- No personal user data is collected beyond what appears in error messages
- Only monitors Beauty CRM applications by default
- All data collection can be disabled through the popup interface

## DevTools Integration

The Neural MCP Chrome Extension integrates with Chrome DevTools to provide real-time monitoring and analysis of web requests and JavaScript errors.

### Features

#### Network Monitoring
- Track all web requests from Beauty CRM applications
- Monitor HTTP status codes and identify failed requests
- Filter and search request data

#### Error Tracking
- Capture and display JavaScript errors and unhandled promise rejections
- View detailed error information including stack traces
- Track error patterns across pages

#### DOM Analysis
- Analyze DOM elements for performance issues
- Receive accessibility recommendations
- Get insights on event handling

### Using the DevTools Panel

1. Install the extension
2. Open Chrome DevTools (F12 or right-click and select "Inspect")
3. Navigate to the "Neural MCP" panel tab
4. The panel provides two tabs:
   - **Requests**: Shows all captured network requests
   - **Errors**: Shows all captured JavaScript errors

### Using the Elements Sidebar

1. In Chrome DevTools, go to the "Elements" panel
2. Find the "Neural MCP Inspector" in the sidebar tabs (usually on the right side)
3. Select any DOM element to see analysis and recommendations

### Integration with Neural MCP Server

The extension communicates with the Neural MCP server to:
- Send captured data for analysis
- Retrieve AI-powered recommendations and insights
- Apply machine learning to detect patterns and anomalies

## Development

### Project Structure

```
neural-mcp-chrome-extension/
├── assets/            # Icons and images
├── pages/             # Popup and options pages
├── src/               # Extension source code
│   ├── background.js  # Background service worker
│   └── content.js     # Content script
└── manifest.json      # Extension manifest
```

### Building for Production

To build the extension for production:

1. Ensure all files are properly formatted and optimized
2. Zip the contents of the directory (excluding any development files)
3. The resulting ZIP file can be distributed or submitted to the Chrome Web Store

## Troubleshooting

If you encounter issues with the DevTools integration:

1. Check the Chrome DevTools console for errors
2. Verify that the extension has the required permissions
3. Ensure the Neural MCP server is running and accessible
4. Try reloading the extension or DevTools

## License

Proprietary - Beauty CRM Internal Use Only 