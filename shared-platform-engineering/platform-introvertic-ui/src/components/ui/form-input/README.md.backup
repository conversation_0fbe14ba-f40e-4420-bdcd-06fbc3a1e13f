# Enhanced Form Input Component

The `EnhancedFormInput` component is a comprehensive form input solution that combines our styled `Input` component with proper form handling and validation feedback.

## Features

- Seamless integration with react-hook-form
- Built-in label, description, and error message handling
- Support for all input variants (modern, filled, underlined, etc.)
- Icon support with positioning options
- Accessibility-friendly with proper labeling

## Usage

```tsx
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Form, FormField } from '@beauty-crm/platform-introvertic-ui';
import { EnhancedFormInput } from '@beauty-crm/platform-introvertic-ui';
import { User } from 'lucide-react';

// Define your form schema
const formSchema = z.object({
  firstName: z.string().min(2, 'First name must be at least 2 characters'),
  // other fields...
});

type FormValues = z.infer<typeof formSchema>;

function MyForm() {
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      firstName: '',
      // other defaults...
    },
  });

  const onSubmit = (values: FormValues) => {
    // Handle form submission
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <FormField
          control={form.control}
          name="firstName"
          render={({ field }) => (
            <EnhancedFormInput
              field={field}
              label="First Name"
              placeholder="Enter your first name"
              required
              icon={<User className="h-4 w-4" />}
              variant="modern"
              description="Your legal first name"
            />
          )}
        />
        
        {/* Other form fields */}
        
        <button type="submit">Submit</button>
      </form>
    </Form>
  );
}
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| field | ControllerRenderProps | required | The field object from react-hook-form |
| label | string | undefined | Label text for the input |
| placeholder | string | undefined | Placeholder text |
| description | string | undefined | Helper text displayed below the input |
| type | string | 'text' | HTML input type (text, email, password, etc.) |
| required | boolean | false | Whether the field is required |
| icon | ReactNode | undefined | Icon to display inside the input |
| iconPosition | 'left' \| 'right' | 'left' | Position of the icon |
| variant | 'default' \| 'modern' \| 'minimal' \| 'filled' \| 'underlined' | 'modern' | Visual style of the input |
| inputSize | 'sm' \| 'default' \| 'lg' | 'default' | Size of the input |
| className | string | undefined | Additional CSS classes |
| autoComplete | string | undefined | HTML autocomplete attribute |

## Variants

The component supports all the same variants as the base `Input` component:

- **modern**: Rounded with subtle shadows and hover effects
- **default**: Standard input style
- **minimal**: Clean, minimal styling
- **filled**: Background-filled style
- **underlined**: Bottom-border only style

## Accessibility

The component ensures proper accessibility by:
- Associating labels with inputs
- Providing clear error messages
- Supporting required field indicators
- Maintaining proper focus states 