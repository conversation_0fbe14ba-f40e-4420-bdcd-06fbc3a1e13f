# Enhanced Sidebar Components

A collection of beautiful, accessible sidebar components for building navigation in your application.

## Features

- Modern, clean design with subtle animations
- Collapsible sidebar with smooth transitions
- Active state indicators with accent colors
- Organized sections with optional titles
- Fully accessible and keyboard navigable
- Customizable with Tailwind CSS classes

## Components

### EnhancedSidebar

The main container component for the sidebar.

```tsx
import { EnhancedSidebar } from '@beauty-crm/platform-introvertic-ui';

<EnhancedSidebar collapsed={false} className="your-custom-class">
  {/* Sidebar content */}
</EnhancedSidebar>
```

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| children | ReactNode | required | The content of the sidebar |
| className | string | undefined | Additional CSS classes |
| collapsed | boolean | false | Whether the sidebar is collapsed |

### SidebarSection

A component for grouping sidebar items into sections with optional titles.

```tsx
import { SidebarSection } from '@beauty-crm/platform-introvertic-ui';

<SidebarSection title="Main" className="your-custom-class">
  {/* Sidebar items */}
</SidebarSection>
```

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| children | ReactNode | required | The sidebar items in this section |
| title | string | undefined | The title of the section |
| className | string | undefined | Additional CSS classes |

### SidebarItem

A component for individual navigation items in the sidebar.

```tsx
import { SidebarItem } from '@beauty-crm/platform-introvertic-ui';
import { Home } from 'lucide-react';

<SidebarItem
  icon={Home}
  label="Dashboard"
  href="/dashboard"
  isActive={true}
  onClick={() => console.log('Clicked')}
/>
```

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| icon | LucideIcon | required | The icon to display |
| label | string | required | The text label |
| href | string | required | The URL to navigate to |
| isActive | boolean | false | Whether this item is active |
| title | string | undefined | The title attribute for the link |
| onClick | function | undefined | Click handler function |
| className | string | undefined | Additional CSS classes |

## Example Usage

```tsx
import { 
  EnhancedSidebar, 
  SidebarSection, 
  SidebarItem 
} from '@beauty-crm/platform-introvertic-ui';
import { Home, Settings, Users } from 'lucide-react';

function AppSidebar() {
  const [activeItem, setActiveItem] = useState('dashboard');
  
  const handleItemClick = (itemId) => (e) => {
    e.preventDefault();
    setActiveItem(itemId);
  };
  
  return (
    <EnhancedSidebar>
      <div className="p-4">
        <h2 className="text-lg font-bold">My App</h2>
      </div>
      
      <SidebarSection title="Main">
        <SidebarItem
          icon={Home}
          label="Dashboard"
          href="/dashboard"
          isActive={activeItem === 'dashboard'}
          onClick={handleItemClick('dashboard')}
        />
        
        <SidebarItem
          icon={Users}
          label="Users"
          href="/users"
          isActive={activeItem === 'users'}
          onClick={handleItemClick('users')}
        />
      </SidebarSection>
      
      <SidebarSection title="Settings" className="mt-6">
        <SidebarItem
          icon={Settings}
          label="Settings"
          href="/settings"
          isActive={activeItem === 'settings'}
          onClick={handleItemClick('settings')}
        />
      </SidebarSection>
    </EnhancedSidebar>
  );
}
```

## Customization

The sidebar components are built with Tailwind CSS and can be customized by passing additional classes through the `className` prop. The components use CSS variables for colors, so you can also customize the appearance by overriding these variables in your CSS.

```css
:root {
  --sidebar-bg: #ffffff;
  --sidebar-border: #e5e7eb;
  --sidebar-text: #374151;
  --sidebar-text-muted: #6b7280;
  --sidebar-active-bg: rgba(59, 130, 246, 0.1);
  --sidebar-active-text: #3b82f6;
  --sidebar-hover-bg: #f3f4f6;
  --sidebar-hover-text: #111827;
}
```

## Accessibility

The sidebar components are built with accessibility in mind:

- All interactive elements are keyboard navigable
- Icons have proper aria labels
- Active states are clearly indicated visually
- Proper semantic HTML is used throughout
- Color contrast meets WCAG standards 