# 🌟 Beautiful Eventing Platform

> Elegant event-driven architecture with NATS JetStream for Beauty CRM

[![TypeScript](https://img.shields.io/badge/TypeScript-5.6+-blue.svg)](https://www.typescriptlang.org/)
[![NATS](https://img.shields.io/badge/NATS-2.28+-green.svg)](https://nats.io/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

## ✨ Features

- 🚀 **Beautiful API** - Fluent, type-safe event publishing and subscribing
- 🔄 **Auto-reconnection** - Graceful handling of connection failures
- 📡 **JetStream Support** - Persistent, reliable event streaming
- 🏗️ **Event Builder** - Fluent API for creating domain events
- 🎯 **Subject Patterns** - Consistent NATS subject naming
- 🏥 **Health Checks** - Built-in monitoring and diagnostics
- 📦 **Batch Publishing** - Efficient bulk event operations
- 🔍 **Type Safety** - Full TypeScript support with generics

## 🚀 Quick Start

### Installation

```bash
bun add @beauty-crm/platform-eventing
```

### Publishing Events

```typescript
import { EventPublisher, createEvent, EventTypes } from '@beauty-crm/platform-eventing';

// Create a beautiful publisher
const publisher = new EventPublisher({
  serviceName: 'appointment-service',
  stream: {
    name: 'APPOINTMENT_EVENTS',
    subjects: ['appointment.events.*']
  }
});

await publisher.connect();

// Publish a beautiful event
const event = createEvent()
  .type(EventTypes.created('appointment'))
  .aggregate('appointment-123', 'appointment')
  .data({ 
    customerName: 'Alice', 
    treatmentName: 'Facial',
    startTime: '2024-01-15T10:00:00Z'
  })
  .source('appointment-service')
  .correlationId('req-456')
  .userId('user-789')
  .build();

await publisher.publish(event);
```

### Subscribing to Events

```typescript
import { EventSubscriber, SubscriberConfigs } from '@beauty-crm/platform-eventing';

// Create a beautiful subscriber
const subscriber = new EventSubscriber(
  SubscriberConfigs.appointment('appointment-management')
);

await subscriber.connect();

// Subscribe to appointment events
await subscriber.subscribe(
  'appointment.events.created',
  async (event) => {
    console.log(`📅 New appointment: ${event.data.customerName}`);
    // Handle the event...
  }
);

// Error handling
subscriber.onError(async (error, event) => {
  console.error('💥 Event processing failed:', error);
  // Handle error...
});
```

## 🏗️ Event Builder

Create events with a delightful fluent API:

```typescript
import { createEvent, createEventFor, EventTypes } from '@beauty-crm/platform-eventing';

// Basic event creation
const event = createEvent()
  .type('appointment.created')
  .aggregate('appointment-123', 'appointment')
  .data({ customerName: 'Alice' })
  .source('appointment-service')
  .build();

// Create event for specific aggregate
const staffEvent = createEventFor('staff', 'staff-456')
  .type(EventTypes.updated('staff'))
  .data({ name: 'Bob', position: 'Stylist' })
  .source('staff-service')
  .correlationId('req-789')
  .build();

// Quick domain event creation
const quickEvent = createDomainEvent({
  eventType: EventTypes.completed('appointment'),
  aggregateId: 'appointment-789',
  aggregateType: 'appointment',
  data: { duration: 60, rating: 5 },
  source: 'appointment-service',
  correlationId: 'req-123'
});
```

## 📡 Subject Patterns

Build consistent NATS subjects:

```typescript
import { subject, Subjects } from '@beauty-crm/platform-eventing';

// Fluent subject building
const appointmentSubject = subject()
  .aggregate('appointment')
  .events()
  .action('created')
  .build(); // "appointment.events.created"

// Pre-built patterns
const allAppointments = Subjects.allEvents('appointment'); // "appointment.events.*"
const specificEvent = Subjects.eventType('staff', 'updated'); // "staff.events.updated"
```

## 🏭 Pre-configured Services

Use ready-made configurations for Beauty CRM services:

```typescript
import { 
  createPublisher, 
  createSubscriber,
  PublisherConfigs,
  SubscriberConfigs 
} from '@beauty-crm/platform-eventing';

// Pre-configured appointment publisher
const publisher = createPublisher(PublisherConfigs.appointment());

// Pre-configured staff subscriber
const subscriber = createSubscriber(SubscriberConfigs.staff('staff-management'));
```

## 🏥 Health Monitoring

Built-in health checks for monitoring:

```typescript
// Check publisher health
const publisherHealth = await publisher.healthCheck();
console.log('Publisher healthy:', publisherHealth.healthy);

// Check subscriber health
const subscriberHealth = await subscriber.healthCheck();
console.log('Active subscriptions:', subscriberHealth.details?.subscriptions);

// Connection status
console.log('Status:', publisher.getStatus()); // 'connected' | 'disconnected' | etc.
```

## 📦 Batch Operations

Efficient bulk event publishing:

```typescript
const events = [
  createEvent().type('appointment.created').aggregate('1', 'appointment').data({}).source('service').build(),
  createEvent().type('appointment.created').aggregate('2', 'appointment').data({}).source('service').build(),
  createEvent().type('appointment.created').aggregate('3', 'appointment').data({}).source('service').build(),
];

await publisher.publishBatch(events);
```

## 🔧 Configuration

### Publisher Configuration

```typescript
const publisherConfig = {
  serviceName: 'my-service',
  natsUrl: 'nats://localhost:4222',
  stream: {
    name: 'MY_EVENTS',
    subjects: ['my.events.*'],
    maxAge: 7 * 24 * 60 * 60 * 1000 * 1000 * 1000, // 7 days
    maxMessages: 10000,
    storage: 'file',
    retention: 'limits'
  },
  connection: {
    maxReconnectAttempts: 10,
    reconnectTimeWait: 2000
  },
  publishing: {
    waitForAck: true,
    ackTimeout: 5000
  }
};
```

### Subscriber Configuration

```typescript
const subscriberConfig = {
  serviceName: 'my-subscriber',
  natsUrl: 'nats://localhost:4222',
  consumer: {
    name: 'my-consumer',
    deliverPolicy: 'new',
    ackPolicy: 'explicit',
    maxDeliver: 3,
    ackWait: 30000
  }
};
```

## 🎯 Common Patterns

### Request-Response with Events

```typescript
// Service A publishes request event
const requestEvent = createEvent()
  .type('treatment.price.requested')
  .aggregate('treatment-123', 'treatment')
  .data({ treatmentId: 'treatment-123' })
  .source('appointment-service')
  .correlationId('req-456')
  .build();

await publisher.publish(requestEvent);

// Service B responds with event
const responseEvent = createEvent()
  .type('treatment.price.responded')
  .aggregate('treatment-123', 'treatment')
  .data({ price: 150, currency: 'USD' })
  .source('treatment-service')
  .correlationId('req-456') // Same correlation ID
  .causationId(requestEvent.eventId) // Link to causing event
  .build();
```

### Saga Pattern

```typescript
// Orchestrator publishes command events
const commands = [
  createEvent().type('appointment.reserve').aggregate('apt-1', 'appointment').data({}).source('orchestrator').build(),
  createEvent().type('payment.charge').aggregate('pay-1', 'payment').data({}).source('orchestrator').build(),
  createEvent().type('notification.send').aggregate('not-1', 'notification').data({}).source('orchestrator').build(),
];

await publisher.publishBatch(commands);
```

## 📚 API Reference

### EventPublisher

- `connect()` - Connect to NATS
- `disconnect()` - Graceful disconnect
- `publish(event, options?)` - Publish single event
- `publishBatch(events, options?)` - Publish multiple events
- `healthCheck()` - Get health status
- `getStatus()` - Get connection status
- `isConnected()` - Check if connected

### EventSubscriber

- `connect()` - Connect to NATS
- `disconnect()` - Graceful disconnect
- `subscribe(subject, handler, options?)` - Subscribe to events
- `subscribeToStream(stream, subject, handler, options?)` - JetStream subscription
- `unsubscribe(subject)` - Unsubscribe from subject
- `onError(handler)` - Set error handler
- `healthCheck()` - Get health status
- `getSubscriptions()` - Get active subscriptions

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

## 📄 License

MIT © Beauty CRM Platform Team
