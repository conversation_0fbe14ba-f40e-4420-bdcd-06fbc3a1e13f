# Neural Network MCP (Master Control Program)

A centralized neural network-based system for monitoring, controlling, and optimizing services across the Beauty CRM platform.

## Overview

The Neural Network MCP acts as a central intelligence system that:

1. Monitors events and patterns across microservices
2. Predicts potential issues before they occur
3. Optimizes resource allocation and scaling
4. Coordinates inter-service communication
5. Provides real-time insights into system health

## Architecture

The MCP uses a neural network architecture for event pattern recognition and anomaly detection:

- **Core Neural Network**: Multi-layered network for pattern detection
- **Service Registry**: Tracks all connected microservices
- **Event Processing Pipeline**: Processes and normalizes events for the neural network
- **Resource Optimizer**: Allocates resources based on neural network predictions
- **API Gateway**: Provides a unified interface for services to communicate with the MCP

## Getting Started

### Prerequisites

- Node.js 18+
- SQLite 3+
- Redis 6+

### Installation

```bash
# Clone the repository
git clone <repository-url>

# Navigate to the project
cd neural-mcp

# Install dependencies
npm install

# Generate Prisma client
npm run db:generate

# Run migrations
npm run db:migrate

# Start the development server
npm run dev
```

### Environment Variables

Create a `.env` file with the following variables:

```
DATABASE_URL="file:./dev.db"
REDIS_URL="redis://localhost:6379"
PORT=3100
LOG_LEVEL="debug"
```

## Usage

Services can register with the MCP and start sending event data:

```typescript
import { MCPClient } from '@beauty-crm/neural-network-mcp-client';

const mcpClient = new MCPClient({
  treatmentId: 'identity-service',
  serviceType: 'authentication',
  endpoint: 'http://localhost:3100'
});

// Register with MCP
await mcpClient.register();

// Send events
mcpClient.trackEvent({
  type: 'authentication',
  action: 'login_attempt',
  success: true,
  metadata: {
    userId: '123',
    ipAddress: '***********'
  }
});
```

## Features

### Error Pattern Recognition

Similar to the React error neural network, but expanded to handle:

- Database connection patterns
- API failure patterns
- Memory usage anomalies
- Request timeout patterns
- Authorization failures

### Resource Optimization

- Automatic scaling recommendations
- Load balancing suggestions
- Database query optimization
- Cache strategy recommendations

### Service Communication Management

- Coordinates service discovery
- Manages inter-service communication patterns
- Identifies potential race conditions or deadlocks

## Browser Extension

The Neural MCP Chrome extension allows monitoring browser performance and errors:

### Features

- Captures JavaScript errors and exceptions
- Monitors network requests
- Tracks page load and performance metrics
- Sends data to the MCP for analysis

### Installation

1. Navigate to the `../neural-mcp-chrome-extension` directory
2. Load the extension in Chrome:
   - Open Chrome and go to `chrome://extensions`
   - Enable "Developer mode"
   - Click "Load unpacked" and select the extension directory
3. Configure the extension to point to your MCP server

### Configuration

- The extension connects to the MCP server at `http://localhost:3100` by default
- You can change the endpoint in the extension popup settings
- Enable/disable data collection as needed

## Data Storage

The Neural MCP uses SQLite for data storage with the following schema:

- **Services**: Registered service information and health data
- **Events**: Event data from various services
- **NeuralNetworkState**: Stores the trained neural network weights
- **Predictions**: Issue predictions and suggested actions
- **ServiceHealth**: Health metrics for each service
- **ResourceRecommendations**: Resource allocation recommendations

## License

Proprietary - Beauty CRM Internal Use Only 

## Tasks

### Extracted Tasks

- [ ] **Core Neural Network**: Multi-layered network for pattern detection - M1
- [ ] **Service Registry**: Tracks all connected microservices - M2
- [ ] **Event Processing Pipeline**: Processes and normalizes events for the neural network - M3
- [ ] **Resource Optimizer**: Allocates resources based on neural network predictions - M4
- [ ] **API Gateway**: Provides a unified interface for services to communicate with the MCP - M5
- [ ] Node.js 18+ - M6
- [ ] 'login_attempt', - M7
- [ ] Database connection patterns - M8
- [ ] API failure patterns - M9
- [ ] Memory usage anomalies - M10
- [ ] Request timeout patterns - M11
- [ ] Authorization failures - M12
- [ ] Automatic scaling recommendations - M13
- [ ] Load balancing suggestions - M14
- [ ] Database query optimization - M15
- [ ] Cache strategy recommendations - M16
- [ ] Coordinates service discovery - M17
- [ ] Manages inter-service communication patterns - M18
- [ ] Identifies potential race conditions or deadlocks - M19
- [ ] Captures JavaScript errors and exceptions - M20
- [ ] Monitors network requests - M21
- [ ] Tracks page load and performance metrics - M22
- [ ] Sends data to the MCP for analysis - M23
- [ ] Open Chrome and go to `chrome://extensions` - M24
- [ ] Enable "Developer mode" - M25
- [ ] Click "Load unpacked" and select the extension directory - M26
- [ ] The extension connects to the MCP server at `http://localhost:3100` by default - M27
- [ ] You can change the endpoint in the extension popup settings - M28
- [ ] Enable/disable data collection as needed - M29
- [ ] **Services**: Registered service information and health data - M30
- [ ] **Events**: Event data from various services - M31
- [ ] **NeuralNetworkState**: Stores the trained neural network weights - M32
- [ ] **Predictions**: Issue predictions and suggested actions - M33
- [ ] **ServiceHealth**: Health metrics for each service - M34
- [ ] **ResourceRecommendations**: Resource allocation recommendations - M35

### General Tasks

- [ ] Review and update content - M1
- [ ] Add missing information - M2
- [ ] Improve structure - M3
- [ ] Add examples - M4
- [ ] Validate accuracy - M5

