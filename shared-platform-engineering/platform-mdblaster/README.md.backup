# MDBlaster

Lightning-fast API testing using markdown-like syntax.

## Installation

```bash
gem install mdblaster
```

## Usage

Create a test file with `.blast.md` extension:

```markdown
# Auth API Tests
> baseUrl: ${env.BASE_URL}

# Create a new user
POST /api/v1/auth/register {"email":"<EMAIL>","password":"Test123!@#","metadata":{"firstName":"Test","lastName":"User"}} -> 200 {"success":true}

# First login
POST /api/v1/auth/sign-in {"email":"<EMAIL>","password":"Test123!@#"} -> 200 {"success":true} => userToken: $.data.token

# Setup MFA
POST /api/v1/auth/mfa/setup [Authorization: Bearer ${vars.userToken}] -> 200 {"success":true} => secretKey: $.data.secretKey

# Verify MFA
POST /api/v1/auth/mfa/verify [Authorization: Bearer ${vars.userToken}] {"code": "123456", "secretKey": "${vars.secretKey}"} -> 200 {"success":true} 

# Logout
POST /api/v1/auth/sign-out [Authorization: Bearer ${vars.userToken}] -> 200 {"success":true}
```

Or use the smart syntax for automatic value generation auth.smart.blast.md:

```markdown
# Auth Smart API Tests
> baseUrl: ${env.BASE_URL}

# Create a new user
POST /api/v1/auth/register email,password,metadata.firstName,metadata.lastName -> 200 {"success":true}

# First login
POST /api/v1/auth/sign-in email,password -> 200 {"success":true} => userToken: $.data.token

# Logout
POST /api/v1/auth/sign-out [Authorization: Bearer ${vars.userToken}] -> 200 {"success":true}
```

Run the tests:

```bash
mdblaster tests/api/auth.blast.md
```

## Features

- Markdown-like syntax for API tests
- Smart value generation for test data
- Variable support for chaining requests
- JSON response validation
- Headers and authentication support
- Multiple test files support

## Test File Format

Each test file consists of:

1. Optional base URL configuration
2. Test cases in the format:
   ```
   METHOD /path [headers] {body} -> expected_status {expected_response}
   ```
   or
   ```
   METHOD /path field1,field2,nested.field -> expected_status {expected_response}
   ```

### Headers

Headers are specified in square brackets:
```
[Authorization: Bearer token, Content-Type: application/json]
```

### Body

Body can be specified in two ways:

1. JSON format:
```
{"key": "value"}
```

2. Field list format for automatic value generation:
```
email,password,metadata.firstName,metadata.lastName
```

### Variables

Variables can be stored from responses and used in subsequent requests:
```
POST /auth/login {"email": "<EMAIL>"} -> 200 => token: $.data.token
GET /profile [Authorization: Bearer ${vars.token}] -> 200
```

## License

MIT
