# Platform Shell Lifecycle

Smart development lifecycle tool for Module Federation microfrontends, supporting both Vite and Nx.

## Features

- Start Module Federation applications (shells or remotes)
- Support for both Vite and Nx Module Federation frameworks
- Automatic configuration generation for Nx projects
- Development and production modes
- Support for both Webpack and Vite bundlers
- Configuration via metadata file

## Installation

```bash
npm install
npm run build
```

The tool will be linked to your global npm binaries automatically.

## Usage

### Using the Metadata File

The recommended way to configure your microfrontend is using a metadata file named `platform-shell-lifecycle.metadata.json`. You can create this file in your project root:

```bash
# Initialize a new metadata file with default settings
platform-shell-lifecycle start --init
```

This will prompt you for basic information like application type and name, then create a metadata file with sensible defaults. You can then edit this file to customize the configuration.

### Basic Usage

Once you have a metadata file, you can start your application simply by:

```bash
# Start using configuration from platform-shell-lifecycle.metadata.json
platform-shell-lifecycle start
```

You can still override settings via command line:

```bash
# Start a shell application, overriding settings in metadata file
platform-shell-lifecycle start --app-type shell --app-name my-shell --port 5000

# Start a remote application, overriding settings in metadata file
platform-shell-lifecycle start --app-type remote --app-name my-remote --port 5001
```

### Specifying the Framework

You can choose between Vite and Nx Module Federation frameworks:

```bash
# Use Vite Module Federation (default)
platform-shell-lifecycle start --framework vite

# Use Nx Module Federation
platform-shell-lifecycle start --framework nx
```

If you don't specify a framework, the one defined in the metadata file will be used, or you'll be prompted to choose one.

### Environment

You can specify the environment (development or production):

```bash
# Development mode (default)
platform-shell-lifecycle start --app-env dev

# Production mode
platform-shell-lifecycle start --app-env prod
```

### Custom Configuration

You can specify a custom webpack or vite configuration file:

```bash
# Use a custom webpack config with Nx
platform-shell-lifecycle start --framework nx --config path/to/webpack.config.js

# Use a custom vite config
platform-shell-lifecycle start --framework vite --config path/to/vite.config.js
```

## Metadata File Format

The `platform-shell-lifecycle.metadata.json` file has the following structure:

```json
{
  "appType": "shell",                  // "shell" or "remote"
  "appName": "my-app",                 // Application name
  "appEnv": "dev",                     // "dev" or "prod"
  "port": "5174",                      // Port number
  "framework": "vite",                 // "vite" or "nx"
  "configPath": "./vite.config.ts",    // Optional: path to custom config
  
  "vite": {
    "name": "my-app",
    "remotes": {                        // For shell applications
      "remote1": "http://localhost:3001/remoteEntry.js"
    },
    "exposes": {                        // For remote applications
      "./Component": "./src/components/Component.tsx"
    },
    "shared": {
      "react": { "singleton": true, "eager": true },
      "react-dom": { "singleton": true, "eager": true }
    }
  },
  
  "nx": {
    "name": "my-app",
    "remotes": [                        // For shell applications
      ["remote1", "http://localhost:3001/remoteEntry.js"]
    ],
    "exposes": {                        // For remote applications
      "./Component": "./src/components/Component.tsx"
    },
    "shared": {
      "react": { "singleton": true, "eager": true },
      "react-dom": { "singleton": true, "eager": true }
    }
  }
}
```

## Framework-Specific Information

### Vite Module Federation

For Vite Module Federation, the tool uses the official `@module-federation/vite` plugin from module-federation.io. When you choose the Vite framework, the tool will create a default vite.config.ts if one doesn't exist:

```typescript
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import federation from '@module-federation/vite';

export default defineConfig({
  plugins: [
    react(),
    federation({
      name: 'my-app',
      filename: 'remoteEntry.js',
      // For shell applications
      remotes: {
        // Example: remote1: 'http://localhost:3001/remoteEntry.js',
      },
      // OR for remote applications
      exposes: {
        // Example: './Component': './src/components/Component.tsx',
      },
      shared: {
        react: { singleton: true, eager: true },
        'react-dom': { singleton: true, eager: true }
      }
    })
  ],
  build: {
    modulePreload: false,
    target: 'esnext',
    minify: false,
    cssCodeSplit: false
  },
  server: {
    port: 5174,
    open: true
  }
});
```

### Nx Module Federation

For Nx Module Federation, the tool will automatically generate the following configuration files if they don't exist:

- nx.json
- project.json
- module-federation.config.js
- webpack.config.ts

The webpack.config.ts file follows the Nx Module Federation pattern:

```typescript
import { composePlugins, withNx } from '@nx/webpack';
import { withReact } from '@nx/react';
import { withModuleFederation } from '@nx/react/module-federation';
import baseConfig from './module-federation.config';

const config = {
  ...baseConfig,
};

// Nx plugins for webpack to build config object from Nx options and context.
export default composePlugins(
  withNx(),
  withReact(),
  withModuleFederation(config, { dts: false })
);
```

The module-federation.config.js defines your federation setup:

```javascript
module.exports = {
  name: 'my-app',
  // For shell applications
  remotes: [
    // Example: ['remote1', 'http://localhost:4201/remoteEntry.js']
  ],
  // OR for remote applications
  exposes: {
    // Example: './Component': './src/components/Component.tsx'
  },
  shared: {
    react: { singleton: true, eager: true },
    'react-dom': { singleton: true, eager: true }
  }
};
```

You may need to customize these configuration files to properly configure your remotes and exposed modules.

## Development

To contribute to this tool:

```bash
# Watch for changes
npm run dev

# Lint code
npm run lint

# Format code
npm run format
```

## License

MIT 

## Tasks

### Extracted Tasks

- [ ] Start Module Federation applications (shells or remotes) - M1
- [ ] Support for both Vite and Nx Module Federation frameworks - M2
- [ ] Automatic configuration generation for Nx projects - M3
- [ ] Development and production modes - M4
- [ ] Support for both Webpack and Vite bundlers - M5
- [ ] Configuration via metadata file - M6
- [ ] project.json - M7
- [ ] module-federation.config.js - M8
- [ ] webpack.config.ts - M9

### General Tasks

- [ ] Review and update content - M1
- [ ] Add missing information - M2
- [ ] Improve structure - M3
- [ ] Add examples - M4
- [ ] Validate accuracy - M5

