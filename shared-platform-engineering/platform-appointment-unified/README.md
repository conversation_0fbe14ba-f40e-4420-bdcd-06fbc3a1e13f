# 🎯 Product Appointment Unified

A unified appointment library that consolidates all appointment-related functionality for the Beauty CRM system. This library follows Domain-Driven Design (DDD) principles and provides a single source of truth for appointment management.

## 🚀 Features

- **Single Source of Truth**: All appointment schemas, types, and validation in one place
- **Domain-Driven Design**: Proper separation of concerns with domain entities and business logic
- **Event-Driven Architecture**: Comprehensive event system for appointment lifecycle
- **Type Safety**: Full TypeScript support with Zod validation
- **Repository Pattern**: Clean abstraction for data persistence
- **Business Rules**: Encapsulated business logic within domain entities

## 📦 Installation

```bash
bun add @beauty-crm/product-appointment-unified
```

## 🏗️ Architecture

This library is organized into four main layers:

### 1. Schema Layer (`/schema`)
- **Single source of truth** for appointment structure
- Zod schemas for validation
- TypeScript type definitions
- Prisma schema generation

### 2. Domain Layer (`/domain`)
- `AppointmentEntity` with business logic
- Domain rules and validation
- Business methods (confirm, cancel, complete, etc.)

### 3. Eventing Layer (`/eventing`)
- Domain event definitions
- Event builders and factories
- Integration with platform eventing system

### 4. Infrastructure Layer (`/infrastructure`)
- Repository interfaces
- Adapter interfaces
- Application service interfaces
- Query builders and filters

## 🔧 Usage

### Basic Usage

```typescript
import {
  AppointmentEntity,
  CreateAppointmentRequest,
  validateCreateRequest,
  createAppointmentCreatedEvent
} from '@beauty-crm/product-appointment-unified';

// Create a new appointment
const createRequest: CreateAppointmentRequest = {
  salonId: 'salon_123',
  customerId: 'customer_456',
  treatmentId: 'treatment_789',
  customerName: 'John Doe',
  customerEmail: '<EMAIL>',
  treatmentName: 'Haircut',
  treatmentDuration: 60,
  treatmentPrice: 50.00,
  salonName: 'Beauty Salon',
  startTime: new Date('2024-01-15T10:00:00Z'),
  endTime: new Date('2024-01-15T11:00:00Z'),
  status: 'PENDING',
  locale: 'en-US',
  source: 'planner'
};

// Validate the request
const validatedRequest = validateCreateRequest(createRequest);

// Create domain entity
const appointment = new AppointmentEntity({
  ...validatedRequest,
  id: 'appointment_123',
  createdAt: new Date(),
  updatedAt: new Date()
});

// Business operations
appointment.confirm();
appointment.start();
appointment.complete();

// Create events
const createdEvent = createAppointmentCreatedEvent(
  appointment.toPlainObject(),
  { correlationId: 'correlation_123', userId: 'user_456' }
);
```

### Repository Implementation

```typescript
import {
  IAppointmentRepository,
  AppointmentEntity,
  AppointmentFilters
} from '@beauty-crm/product-appointment-unified';

class PrismaAppointmentRepository implements IAppointmentRepository {
  async findById(id: string): Promise<AppointmentEntity | null> {
    // Implementation
  }

  async save(appointment: AppointmentEntity): Promise<void> {
    // Implementation
  }

  async findMany(query: AppointmentFilters): Promise<AppointmentEntity[]> {
    // Implementation
  }

  // ... other methods
}
```

### Event Publishing

```typescript
import {
  IAppointmentEventPublisher,
  createAppointmentConfirmedEvent
} from '@beauty-crm/product-appointment-unified';

class AppointmentEventPublisher implements IAppointmentEventPublisher {
  async publishConfirmed(
    appointmentId: string,
    data: any,
    metadata?: any
  ): Promise<void> {
    const event = createAppointmentConfirmedEvent(appointmentId, data, metadata);
    await this.eventPublisher.publish(event);
  }

  // ... other methods
}
```

## 🎯 Domain Events

The library provides comprehensive event support:

- `appointment.created` - When an appointment is created
- `appointment.updated` - When appointment details are updated
- `appointment.confirmed` - When an appointment is confirmed
- `appointment.cancelled` - When an appointment is cancelled
- `appointment.completed` - When an appointment is completed
- `appointment.rescheduled` - When an appointment is rescheduled
- `appointment.no-show` - When a customer doesn't show up
- `appointment.started` - When an appointment begins

## 🔍 Business Rules

The `AppointmentEntity` enforces business rules:

- Cannot confirm a cancelled appointment
- Cannot cancel a completed appointment
- Can only complete confirmed or in-progress appointments
- Cannot reschedule completed, cancelled, or no-show appointments
- Cannot mark completed appointments as no-show
- Can only start confirmed appointments

## 🧪 Testing

```bash
bun test
```

## 🏗️ Building

```bash
bun run build
```

## 📝 License

MIT


## Tasks

### Extracted Tasks

- [ ] **Single Source of Truth**: All appointment schemas, types, and validation in one place - M1
- [ ] **Domain-Driven Design**: Proper separation of concerns with domain entities and business logic - M2
- [ ] **Event-Driven Architecture**: Comprehensive event system for appointment lifecycle - M3
- [ ] **Type Safety**: Full TypeScript support with Zod validation - M4
- [ ] **Repository Pattern**: Clean abstraction for data persistence - M5
- [ ] **Business Rules**: Encapsulated business logic within domain entities - M6
- [ ] **Single source of truth** for appointment structure - M7
- [ ] Zod schemas for validation - M8
- [ ] TypeScript type definitions - M9
- [ ] Prisma schema generation - M10
- [ ] `AppointmentEntity` with business logic - M11
- [ ] Domain rules and validation - M12
- [ ] Business methods (confirm, cancel, complete, etc.) - M13
- [ ] Domain event definitions - M14
- [ ] Event builders and factories - M15
- [ ] Integration with platform eventing system - M16
- [ ] Repository interfaces - M17
- [ ] Adapter interfaces - M18
- [ ] Application service interfaces - M19
- [ ] Query builders and filters - M20
- [ ] `appointment.created` - When an appointment is created - M21
- [ ] `appointment.updated` - When appointment details are updated - M22
- [ ] `appointment.confirmed` - When an appointment is confirmed - M23
- [ ] `appointment.cancelled` - When an appointment is cancelled - M24
- [ ] `appointment.completed` - When an appointment is completed - M25
- [ ] `appointment.rescheduled` - When an appointment is rescheduled - M26
- [ ] `appointment.no-show` - When a customer doesn't show up - M27
- [ ] `appointment.started` - When an appointment begins - M28
- [ ] Cannot confirm a cancelled appointment - M29
- [ ] Cannot cancel a completed appointment - M30
- [ ] Can only complete confirmed or in-progress appointments - M31
- [ ] Cannot reschedule completed, cancelled, or no-show appointments - M32
- [ ] Cannot mark completed appointments as no-show - M33
- [ ] Can only start confirmed appointments - M34

### General Tasks

- [ ] Review and update content - M1
- [ ] Add missing information - M2
- [ ] Improve structure - M3
- [ ] Add examples - M4
- [ ] Validate accuracy - M5

