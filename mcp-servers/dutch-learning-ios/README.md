# Dutch Learning iOS App - C<PERSON>zemaster Clone

A native iOS application for learning Dutch from English using cloze (fill-in-the-blank) exercises, similar to Clozemaster.com.

## 🎯 Features

- **Cloze Exercises**: Fill-in-the-blank Dutch sentences with English translations
- **Progress Tracking**: Track your learning progress and scores
- **Multiple Difficulty Levels**: Common, intermediate, and advanced word tiers
- **Audio Pronunciation**: Native iOS text-to-speech for Dutch words
- **Offline Support**: Works without internet connection
- **iPhone XS Optimized**: Specifically designed for iOS 18.2

## 🚀 Development Approaches

### Option 1: React Native + Expo (Recommended)
**Advantages**: True native iOS app, cloud building, no local Xcode required

**Setup Steps**:
1. Install Node.js and npm
2. Install Expo CLI: `npm install -g @expo/cli`
3. Navigate to `react-native-app` folder
4. Run `npm install`
5. Start development: `expo start`
6. Build for iOS: `eas build --platform ios`

**Cloud Building**: Uses Expo Application Services (EAS) to build iOS apps in the cloud without requiring local Xcode installation.

### Option 2: Progressive Web App (PWA)
**Advantages**: No app store required, works immediately on iOS Safari

**Setup Steps**:
1. Navigate to `pwa-app` folder
2. Open `index.html` in any web server
3. On iPhone: Open in Safari → Share → Add to Home Screen

## 📱 Installation on iPhone XS

### React Native App:
1. Build using EAS: `eas build --platform ios`
2. Install via TestFlight or direct IPA installation
3. Trust developer certificate in Settings → General → VPN & Device Management

### PWA App:
1. Open Safari on iPhone
2. Navigate to the hosted PWA URL
3. Tap Share button → "Add to Home Screen"
4. App will appear on home screen like a native app

## 🎮 How to Use

1. **Start Learning**: Tap "Start Exercise" to begin
2. **Fill the Blank**: Read the Dutch sentence and fill in the missing word
3. **Check Answer**: Tap "Check" to verify your answer
4. **Track Progress**: View your score and progress in the stats section
5. **Audio**: Tap the speaker icon to hear Dutch pronunciation

## 📊 Learning Data

The app includes 500+ Dutch-English sentence pairs organized by difficulty:
- **Common (A1-A2)**: 200 most frequent Dutch words
- **Intermediate (B1-B2)**: 200 intermediate frequency words  
- **Advanced (C1-C2)**: 100+ advanced vocabulary words

## 🛠 Technical Details

- **Target**: iOS 18.2, iPhone XS
- **Languages**: Dutch → English learning
- **Storage**: Local storage for progress tracking
- **Audio**: iOS native text-to-speech (AVSpeechSynthesizer)
- **Offline**: Full offline functionality after initial load

## 📁 Project Structure

```
dutch-learning-ios/
├── README.md                 # This file
├── react-native-app/         # React Native + Expo implementation
│   ├── App.js               # Main app component
│   ├── package.json         # Dependencies
│   ├── app.json            # Expo configuration
│   └── data/               # Dutch learning data
├── pwa-app/                 # Progressive Web App fallback
│   ├── index.html          # Single-file PWA
│   ├── manifest.json       # PWA manifest
│   └── sw.js              # Service worker
└── docs/                   # Additional documentation
```

## 🔧 Development Commands

### React Native + Expo:
```bash
# Install dependencies
cd react-native-app && npm install

# Start development server
expo start

# Build for iOS (cloud)
eas build --platform ios

# Submit to App Store
eas submit --platform ios
```

### PWA Development:
```bash
# Serve locally
cd pwa-app && python -m http.server 8000
# Or use any web server
```

## 📚 Learning Algorithm

The app uses spaced repetition principles:
1. **New words**: Appear more frequently
2. **Correct answers**: Reduce word frequency
3. **Incorrect answers**: Increase word frequency
4. **Difficulty progression**: Unlock higher tiers based on accuracy

## 🎨 UI/UX Design

- **Minimalist Interface**: Focus on learning content
- **Large Touch Targets**: Optimized for iPhone XS screen
- **Dark/Light Mode**: Automatic based on iOS system settings
- **Haptic Feedback**: Subtle feedback for interactions
- **Accessibility**: VoiceOver support for visually impaired users

## 🚀 Next Steps

1. Test the React Native app using Expo Go
2. Build production iOS app using EAS
3. Install on iPhone XS for testing
4. Iterate based on user feedback
5. Add more advanced features (streaks, achievements, etc.)


## Tasks

### Extracted Tasks

- [ ] **Cloze Exercises**: Fill-in-the-blank Dutch sentences with English translations - M1
- [ ] **Progress Tracking**: Track your learning progress and scores - M2
- [ ] **Multiple Difficulty Levels**: Common, intermediate, and advanced word tiers - M3
- [ ] **Audio Pronunciation**: Native iOS text-to-speech for Dutch words - M4
- [ ] **Offline Support**: Works without internet connection - M5
- [ ] **iPhone XS Optimized**: Specifically designed for iOS 18.2 - M6
- [ ] **Common (A1-A2)**: 200 most frequent Dutch words - M7
- [ ] **Intermediate (B1-B2)**: 200 intermediate frequency words - M8
- [ ] **Advanced (C1-C2)**: 100+ advanced vocabulary words - M9
- [ ] **Target**: iOS 18.2, iPhone XS - M10
- [ ] **Languages**: Dutch → English learning - M11
- [ ] **Storage**: Local storage for progress tracking - M12
- [ ] **Audio**: iOS native text-to-speech (AVSpeechSynthesizer) - M13
- [ ] **Offline**: Full offline functionality after initial load - M14
- [ ] **Minimalist Interface**: Focus on learning content - M15
- [ ] **Large Touch Targets**: Optimized for iPhone XS screen - M16
- [ ] **Dark/Light Mode**: Automatic based on iOS system settings - M17
- [ ] **Haptic Feedback**: Subtle feedback for interactions - M18
- [ ] **Accessibility**: VoiceOver support for visually impaired users - M19

### General Tasks

- [ ] Review and update content - M1
- [ ] Add missing information - M2
- [ ] Improve structure - M3
- [ ] Add examples - M4
- [ ] Validate accuracy - M5

