#!/bin/bash

# Dutch Learning iOS App - Test Script
# Quick verification that everything is set up correctly

set -e

echo "🧪 Dutch Learning iOS App - Test Script"
echo "======================================="

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_test() {
    echo -e "${YELLOW}[TEST]${NC} $1"
}

print_pass() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

print_fail() {
    echo -e "${RED}[FAIL]${NC} $1"
}

# Test 1: Check project structure
print_test "Checking project structure..."
if [ -f "README.md" ] && [ -d "react-native-app" ] && [ -d "pwa-app" ]; then
    print_pass "Project structure is correct"
else
    print_fail "Project structure is missing files"
    exit 1
fi

# Test 2: Check React Native files
print_test "Checking React Native app files..."
if [ -f "react-native-app/package.json" ] && [ -f "react-native-app/App.js" ] && [ -f "react-native-app/app.json" ]; then
    print_pass "React Native files are present"
else
    print_fail "React Native files are missing"
    exit 1
fi

# Test 3: Check PWA files
print_test "Checking PWA files..."
if [ -f "pwa-app/index.html" ] && [ -f "pwa-app/manifest.json" ] && [ -f "pwa-app/sw.js" ]; then
    print_pass "PWA files are present"
else
    print_fail "PWA files are missing"
    exit 1
fi

# Test 4: Check Dutch sentences data
print_test "Checking Dutch sentences data..."
if [ -f "react-native-app/data/sentences.json" ]; then
    SENTENCE_COUNT=$(grep -o '"id"' react-native-app/data/sentences.json | wc -l)
    if [ "$SENTENCE_COUNT" -gt 10 ]; then
        print_pass "Dutch sentences data is present ($SENTENCE_COUNT sentences)"
    else
        print_fail "Not enough sentences in data file"
        exit 1
    fi
else
    print_fail "Dutch sentences data file is missing"
    exit 1
fi

# Test 5: Check Node.js (if available)
print_test "Checking Node.js installation..."
if command -v node &> /dev/null; then
    NODE_VERSION=$(node --version)
    print_pass "Node.js is installed: $NODE_VERSION"
    
    # Test npm
    if command -v npm &> /dev/null; then
        NPM_VERSION=$(npm --version)
        print_pass "npm is installed: $NPM_VERSION"
    else
        print_fail "npm is not installed"
    fi
else
    print_fail "Node.js is not installed (required for React Native)"
fi

# Test 6: Check if dependencies are installed
print_test "Checking React Native dependencies..."
if [ -d "react-native-app/node_modules" ]; then
    print_pass "React Native dependencies are installed"
else
    print_fail "React Native dependencies not installed. Run: cd react-native-app && npm install"
fi

# Test 7: Validate JSON files
print_test "Validating JSON files..."
if command -v node &> /dev/null; then
    # Test package.json
    if node -e "JSON.parse(require('fs').readFileSync('react-native-app/package.json', 'utf8'))" 2>/dev/null; then
        print_pass "package.json is valid JSON"
    else
        print_fail "package.json is invalid JSON"
    fi
    
    # Test app.json
    if node -e "JSON.parse(require('fs').readFileSync('react-native-app/app.json', 'utf8'))" 2>/dev/null; then
        print_pass "app.json is valid JSON"
    else
        print_fail "app.json is invalid JSON"
    fi
    
    # Test sentences.json
    if node -e "JSON.parse(require('fs').readFileSync('react-native-app/data/sentences.json', 'utf8'))" 2>/dev/null; then
        print_pass "sentences.json is valid JSON"
    else
        print_fail "sentences.json is invalid JSON"
    fi
    
    # Test manifest.json
    if node -e "JSON.parse(require('fs').readFileSync('pwa-app/manifest.json', 'utf8'))" 2>/dev/null; then
        print_pass "manifest.json is valid JSON"
    else
        print_fail "manifest.json is invalid JSON"
    fi
else
    print_fail "Cannot validate JSON files without Node.js"
fi

# Test 8: Check PWA HTML structure
print_test "Checking PWA HTML structure..."
if grep -q "<!DOCTYPE html>" pwa-app/index.html && grep -q "<meta name=\"viewport\"" pwa-app/index.html; then
    print_pass "PWA HTML structure is correct"
else
    print_fail "PWA HTML structure is invalid"
fi

# Test 9: Check for required PWA features
print_test "Checking PWA features..."
if grep -q "serviceWorker" pwa-app/index.html && grep -q "manifest.json" pwa-app/index.html; then
    print_pass "PWA features are implemented"
else
    print_fail "PWA features are missing"
fi

# Test 10: Check executable permissions
print_test "Checking script permissions..."
if [ -x "deploy.sh" ] && [ -x "test.sh" ]; then
    print_pass "Scripts have executable permissions"
else
    print_fail "Scripts need executable permissions. Run: chmod +x *.sh"
fi

echo ""
echo "🎯 Test Summary"
echo "==============="
echo "✅ All tests completed!"
echo ""
echo "📱 Next Steps:"
echo "1. For PWA: Run './deploy.sh' and choose option 4"
echo "2. For React Native: Run './deploy.sh' and choose option 1, then 2"
echo "3. For iOS build: Run './deploy.sh' and choose option 3"
echo ""
echo "📚 Documentation:"
echo "- README.md - Overview and features"
echo "- SETUP.md - Detailed setup instructions"
echo "- CLOUD_DEVELOPMENT.md - Cloud-based development guide"
echo ""
echo "🇳🇱 Happy Dutch learning!"
