{"name": "Dutch Learning - Cloze<PERSON>", "short_name": "Dutch Learning", "description": "Learn Dutch through cloze exercises, similar to C<PERSON><PERSON>master", "start_url": "/", "display": "standalone", "background_color": "#ffffff", "theme_color": "#2196F3", "orientation": "portrait", "scope": "/", "lang": "en", "categories": ["education", "language"], "icons": [{"src": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNzIiIGhlaWdodD0iNzIiIHZpZXdCb3g9IjAgMCA3MiA3MiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjcyIiBoZWlnaHQ9IjcyIiByeD0iMTYiIGZpbGw9InVybCgjZ3JhZGllbnQwX2xpbmVhcl8xXzEpIi8+CjxwYXRoIGQ9Ik0yMCAyNEgyNFYyOEgyMFYyNFoiIGZpbGw9IndoaXRlIi8+CjxwYXRoIGQ9Ik0yOCAyNEg1MlYyOEgyOFYyNFoiIGZpbGw9IndoaXRlIi8+CjxwYXRoIGQ9Ik0yMCAzMkgyNFYzNkgyMFYzMloiIGZpbGw9IndoaXRlIi8+CjxwYXRoIGQ9Ik0yOCAzMkg0NFYzNkgyOFYzMloiIGZpbGw9IndoaXRlIi8+CjxwYXRoIGQ9Ik0yMCA0MEgyNFY0NEgyMFY0MFoiIGZpbGw9IndoaXRlIi8+CjxwYXRoIGQ9Ik0yOCA0MEg0OFY0NEgyOFY0MFoiIGZpbGw9IndoaXRlIi8+CjxwYXRoIGQ9Ik0yMCA0OEgyNFY1MkgyMFY0OFoiIGZpbGw9IndoaXRlIi8+CjxwYXRoIGQ9Ik0yOCA0OEg0MFY1MkgyOFY0OFoiIGZpbGw9IndoaXRlIi8+CjxkZWZzPgo8bGluZWFyR3JhZGllbnQgaWQ9ImdyYWRpZW50MF9saW5lYXJfMV8xIiB4MT0iMCIgeTE9IjAiIHgyPSI3MiIgeTI9IjcyIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSI+CjxzdG9wIHN0b3AtY29sb3I9IiMyMTk2RjMiLz4KPHN0b3Agb2Zmc2V0PSIxIiBzdG9wLWNvbG9yPSIjNjY3ZWVhIi8+CjwvbGluZWFyR3JhZGllbnQ+CjwvZGVmcz4KPHN2Zz4K", "sizes": "72x72", "type": "image/svg+xml", "purpose": "any maskable"}, {"src": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTkyIiBoZWlnaHQ9IjE5MiIgdmlld0JveD0iMCAwIDE5MiAxOTIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxOTIiIGhlaWdodD0iMTkyIiByeD0iNDIiIGZpbGw9InVybCgjZ3JhZGllbnQwX2xpbmVhcl8xXzEpIi8+CjxwYXRoIGQ9Ik01NCA2NEg2NFY3NEg1NFY2NFoiIGZpbGw9IndoaXRlIi8+CjxwYXRoIGQ9Ik03NCA2NEgxMzhWNzRINzRWNjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNNTQgODRINjRWOTRINTRWODRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNNzQgODRIMTE4Vjk0SDc0Vjg0WiIgZmlsbD0id2hpdGUiLz4KPHN0eWxlPgoudGV4dCB7IGZvbnQ6IDI0cHggc2Fucy1zZXJpZjsgZmlsbDogd2hpdGU7IH0KPC9zdHlsZT4KPHR0ZXh0IHg9IjU0IiB5PSIxMjAiIGNsYXNzPSJ0ZXh0Ij5OTDwvdGV4dD4KPHR0ZXh0IHg9IjU0IiB5PSIxNDgiIGNsYXNzPSJ0ZXh0Ij5FTjwvdHRleHQ+CjxkZWZzPgo8bGluZWFyR3JhZGllbnQgaWQ9ImdyYWRpZW50MF9saW5lYXJfMV8xIiB4MT0iMCIgeTE9IjAiIHgyPSIxOTIiIHkyPSIxOTIiIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIj4KPHN0b3Agc3RvcC1jb2xvcj0iIzIxOTZGMyIvPgo8c3RvcCBvZmZzZXQ9IjEiIHN0b3AtY29sb3I9IiM2NjdlZWEiLz4KPC9saW5lYXJHcmFkaWVudD4KPC9kZWZzPgo8L3N2Zz4K", "sizes": "192x192", "type": "image/svg+xml", "purpose": "any maskable"}, {"src": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTEyIiBoZWlnaHQ9IjUxMiIgdmlld0JveD0iMCAwIDUxMiA1MTIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI1MTIiIGhlaWdodD0iNTEyIiByeD0iMTEyIiBmaWxsPSJ1cmwoI2dyYWRpZW50MF9saW5lYXJfMV8xKSIvPgo8cGF0aCBkPSJNMTQ0IDE3MEgxNzRWMjAwSDE0NFYxNzBaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMTk0IDE3MEgzNjhWMjAwSDE5NFYxNzBaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMTQ0IDIyNEgxNzRWMjU0SDE0NFYyMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMTk0IDIyNEgzMTRWMjU0SDE5NFYyMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMTQ0IDI3OEgxNzRWMzA4SDE0NFYyNzhaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMTk0IDI3OEgzNDBWMzA4SDE5NFYyNzhaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMTQ0IDMzMkgxNzRWMzYySDE0NFYzMzJaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMTk0IDMzMkgyODBWMzYySDE5NFYzMzJaIiBmaWxsPSJ3aGl0ZSIvPgo8c3R5bGU+Ci50ZXh0IHsgZm9udDogNjBweCBzYW5zLXNlcmlmOyBmaWxsOiB3aGl0ZTsgZm9udC13ZWlnaHQ6IGJvbGQ7IH0KLnNtYWxsIHsgZm9udDogNDBweCBzYW5zLXNlcmlmOyBmaWxsOiB3aGl0ZTsgZm9udC13ZWlnaHQ6IGJvbGQ7IH0KPC9zdHlsZT4KPHR0ZXh0IHg9IjE0NCIgeT0iMTM1IiBjbGFzcz0idGV4dCI+RHV0Y2g8L3R0ZXh0Pgo8dHRleHQgeD0iMTQ0IiB5PSI0MjAiIGNsYXNzPSJzbWFsbCI+TGVhcm5pbmc8L3R0ZXh0Pgo8ZGVmcz4KPGxpbmVhckdyYWRpZW50IGlkPSJncmFkaWVudDBfbGluZWFyXzFfMSIgeDE9IjAiIHkxPSIwIiB4Mj0iNTEyIiB5Mj0iNTEyIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSI+CjxzdG9wIHN0b3AtY29sb3I9IiMyMTk2RjMiLz4KPHN0b3Agb2Zmc2V0PSIxIiBzdG9wLWNvbG9yPSIjNjY3ZWVhIi8+CjwvbGluZWFyR3JhZGllbnQ+CjwvZGVmcz4KPHN2Zz4K", "sizes": "512x512", "type": "image/svg+xml", "purpose": "any maskable"}], "screenshots": [{"src": "screenshot-mobile.png", "sizes": "375x812", "type": "image/png", "form_factor": "narrow"}], "features": ["Cross Platform", "fast", "simple"], "prefer_related_applications": false}