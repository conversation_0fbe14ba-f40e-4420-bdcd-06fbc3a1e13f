<!DOCTYPE html>
<html>
<head>
    <title>Icon Generator</title>
</head>
<body>
    <h1>Dutch Learning App Icon Generator</h1>
    <canvas id="canvas" width="512" height="512" style="border: 1px solid #ccc;"></canvas>
    <br><br>
    <button onclick="downloadIcon(192)">Download 192x192</button>
    <button onclick="downloadIcon(512)">Download 512x512</button>
    <button onclick="downloadIcon(180)">Download 180x180 (Apple)</button>
    
    <script>
        function createIcon(size) {
            const canvas = document.getElementById('canvas');
            const ctx = canvas.getContext('2d');
            
            // Set canvas size
            canvas.width = size;
            canvas.height = size;
            
            // Create gradient background
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#2196F3');
            gradient.addColorStop(1, '#667eea');
            
            // Draw background with rounded corners
            const radius = size * 0.15;
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.roundRect(0, 0, size, size, radius);
            ctx.fill();
            
            // Draw cloze exercise lines (fill-in-the-blank representation)
            ctx.fillStyle = 'white';
            const lineHeight = size * 0.06;
            const lineSpacing = size * 0.12;
            const startY = size * 0.25;
            const leftMargin = size * 0.15;
            const rightMargin = size * 0.85;
            
            // Draw 4 lines representing sentences with blanks
            for (let i = 0; i < 4; i++) {
                const y = startY + (i * lineSpacing);
                
                // Draw bullet point
                ctx.beginPath();
                ctx.arc(leftMargin, y + lineHeight/2, lineHeight/3, 0, 2 * Math.PI);
                ctx.fill();
                
                // Draw line segments with gaps (representing blanks)
                const lineStart = leftMargin + size * 0.08;
                const lineWidth = rightMargin - lineStart;
                const segmentWidth = lineWidth * 0.25;
                const gapWidth = lineWidth * 0.15;
                
                // First segment
                ctx.fillRect(lineStart, y, segmentWidth, lineHeight);
                
                // Gap (blank space)
                
                // Second segment (shorter for variety)
                const secondStart = lineStart + segmentWidth + gapWidth;
                const secondWidth = segmentWidth * (0.6 + i * 0.1); // Vary length
                ctx.fillRect(secondStart, y, secondWidth, lineHeight);
            }
            
            // Add text at bottom
            ctx.fillStyle = 'white';
            ctx.font = `bold ${size * 0.08}px -apple-system, BlinkMacSystemFont, sans-serif`;
            ctx.textAlign = 'center';
            ctx.fillText('Dutch Learning', size/2, size * 0.85);
            
            // Add smaller subtitle
            ctx.font = `${size * 0.05}px -apple-system, BlinkMacSystemFont, sans-serif`;
            ctx.fillText('NL → EN', size/2, size * 0.92);
        }
        
        function downloadIcon(size) {
            createIcon(size);
            const canvas = document.getElementById('canvas');
            const link = document.createElement('a');
            link.download = `icon-${size}.png`;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
        
        // Create initial icon
        createIcon(512);
    </script>
</body>
</html>
