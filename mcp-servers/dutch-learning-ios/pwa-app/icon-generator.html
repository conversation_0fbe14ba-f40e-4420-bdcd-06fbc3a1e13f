<!DOCTYPE html>
<html>
<head>
    <title>Icon Generator</title>
</head>
<body>
    <h1>Dutch Learning App Icon Generator</h1>
    <canvas id="canvas" width="180" height="180" style="border: 1px solid #ccc;"></canvas>
    <br><br>
    <button onclick="generateIcon()">Generate Icon</button>
    <button onclick="downloadIcon()">Download PNG</button>
    
    <script>
        function generateIcon() {
            const canvas = document.getElementById('canvas');
            const ctx = canvas.getContext('2d');
            const size = 180;
            
            // Clear canvas
            ctx.clearRect(0, 0, size, size);
            
            // Create gradient background
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#2196F3');
            gradient.addColorStop(1, '#667eea');
            
            // Draw rounded rectangle background
            const radius = 40;
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.roundRect(0, 0, size, size, radius);
            ctx.fill();
            
            // Draw Dutch flag emoji or text
            ctx.fillStyle = 'white';
            ctx.font = 'bold 80px system-ui, -apple-system, sans-serif';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('🇳🇱', size/2, size/2);
            
            // Fallback text if emoji doesn't work
            ctx.font = 'bold 32px system-ui, -apple-system, sans-serif';
            ctx.fillText('Dutch', size/2, size/2 + 50);
        }
        
        function downloadIcon() {
            const canvas = document.getElementById('canvas');
            const link = document.createElement('a');
            link.download = 'apple-touch-icon.png';
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
        
        // Generate icon on load
        generateIcon();
    </script>
</body>
</html>
