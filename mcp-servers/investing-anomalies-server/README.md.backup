# Investing Anomalies MCP Server

A quantitative investment analysis server inspired by <PERSON>' approach and ValueInvestorsClub.com methodology. Focuses on detecting market anomalies and trading patterns in European markets (Amsterdam Stock Exchange and Frankfurt Stock Exchange).

## Features

### 🔍 Core Functionality
- **Anomaly Detection**: Identifies unusual price movements, volume spikes, and fundamental disconnects
- **Seasonal Pattern Recognition**: Detects recurring patterns like January effect, earnings season patterns, holiday effects
- **Value Investment Opportunities**: Finds undervalued companies with strong fundamentals (ValueInvestorsClub style)
- **Technical Anomalies**: Identifies oversold/overbought conditions and breakout patterns

### 📊 Supported Markets
- **Amsterdam Stock Exchange (Euronext Amsterdam)**: ASML, Shell, Unilever, ING, Adyen
- **Frankfurt Stock Exchange (XETRA)**: SAP, Siemens, Allianz, BASF, Volkswagen

### 🛠 Available Tools

#### 1. `detect_market_anomalies`
Detects stock market anomalies using quantitative analysis.

**Parameters:**
- `start_date` (required): Analysis start date (YYYY-MM-DD)
- `end_date` (required): Analysis end date (YYYY-MM-DD)  
- `exchanges` (required): Array of exchanges ["amsterdam", "frankfurt"]
- `industries` (optional): Industry sector filters
- `market_cap_min` (optional): Minimum market cap in millions EUR (default: 100)
- `volume_threshold` (optional): Minimum daily volume (default: 100,000)

**Output:**
```json
{
  "analysis_period": "2025-01-01 to 2025-07-30",
  "exchanges_analyzed": ["amsterdam", "frankfurt"],
  "total_anomalies_found": 15,
  "high_confidence_count": 8,
  "anomalies": [
    {
      "company_name": "ASML Holding",
      "ticker_symbol": "ASML.AS",
      "exchange": "amsterdam",
      "industry": "technology",
      "anomaly_type": "technical_oversold",
      "confidence_score": 8.7,
      "optimal_buy_date": "2025-08-05",
      "expected_duration_days": 45,
      "historical_success_rate": 78.5,
      "risk_level": "Low",
      "current_price": 125.50,
      "target_price": 145.20,
      "description": "ASML technically oversold with RSI below 30, indicating potential reversal",
      "supporting_data": {
        "rsi_14": 28.5,
        "bollinger_position": -2.1,
        "volume_ratio": 2.3
      }
    }
  ]
}
```

#### 2. `analyze_seasonal_patterns`
Analyzes seasonal trading patterns and calendar effects.

**Parameters:**
- `ticker_symbols` (required): Array of ticker symbols to analyze
- `pattern_type` (required): Type of pattern ["monthly", "quarterly", "earnings", "holiday"]
- `years_back` (optional): Years of historical data (default: 5)

#### 3. `find_value_opportunities`
Finds undervalued companies with strong fundamentals.

**Parameters:**
- `exchanges` (required): Target exchanges
- `pe_ratio_max` (optional): Maximum P/E ratio (default: 15)
- `debt_equity_max` (optional): Maximum debt-to-equity ratio (default: 0.5)
- `roe_min` (optional): Minimum return on equity (default: 0.15)

## Installation

1. **Create the server directory:**
```bash
mkdir -p mcp-servers/investing-anomalies-server
cd mcp-servers/investing-anomalies-server
```

2. **Install dependencies:**
```bash
pip install -r requirements.txt
```

3. **Configure MCP client:**
Add to your MCP configuration file:
```json
{
  "mcpServers": {
    "investing-anomalies-server": {
      "command": "python",
      "args": ["mcp-servers/investing-anomalies-server/server.py"],
      "env": {}
    }
  }
}
```

## Usage Examples

### Detect Market Anomalies
```python
# Find anomalies in Amsterdam tech stocks
result = await detect_market_anomalies(
    start_date="2025-01-01",
    end_date="2025-07-30",
    exchanges=["amsterdam"],
    industries=["technology"],
    market_cap_min=1000
)
```

### Analyze Seasonal Patterns
```python
# Analyze monthly patterns for European blue chips
result = await analyze_seasonal_patterns(
    ticker_symbols=["ASML.AS", "SAP.DE", "RDSA.AS"],
    pattern_type="monthly",
    years_back=10
)
```

### Find Value Opportunities
```python
# Screen for undervalued stocks
result = await find_value_opportunities(
    exchanges=["amsterdam", "frankfurt"],
    pe_ratio_max=12,
    debt_equity_max=0.3,
    roe_min=0.20
)
```

## Integration with Existing Tools

This server is designed to work with existing GroupA MCP tools:

- **`get_ticker_data_GroupA-investor-agent`**: For fundamental data
- **`get_price_history_GroupA-investor-agent`**: For historical price analysis  
- **`perplexity_ask_GroupA-perplexity-ask-web-search`**: For news correlation
- **DEGIRO portfolio tools**: For position analysis

## Anomaly Types Detected

1. **Seasonal Pattern**: Recurring calendar-based patterns
2. **Technical Oversold**: RSI < 30 with reversal signals
3. **Fundamental Disconnect**: Price below intrinsic value
4. **Volume Spike**: Unusual volume with price momentum
5. **Earnings Surprise**: Likely earnings beats based on sector trends
6. **Sector Rotation**: Beneficiaries of sector rotation

## Risk Assessment

- **Low Risk**: Confidence score ≥ 8.5, strong historical success rate
- **Medium Risk**: Confidence score 7.0-8.4, moderate success rate  
- **High Risk**: Confidence score < 7.0, lower success rate

## Quantitative Approach

Inspired by Jim Simons' Renaissance Technologies methodology:
- Statistical pattern recognition
- Historical backtesting validation
- Risk-adjusted return optimization
- Systematic anomaly identification
- Data-driven decision making

## Limitations

- **Simulated Data**: Current implementation uses simulated data for demonstration
- **European Focus**: Limited to Amsterdam and Frankfurt exchanges
- **Pattern Recognition**: Focuses on statistical patterns rather than fundamental analysis
- **Historical Performance**: Past patterns may not predict future results

## Future Enhancements

- Real-time data integration with European exchanges
- Machine learning pattern recognition
- Options flow analysis
- Insider trading pattern detection
- ESG factor integration
- Cryptocurrency anomaly detection

## License

MIT License - See LICENSE file for details.

## Contributing

Contributions welcome! Please read CONTRIBUTING.md for guidelines.

## Disclaimer

This tool is for educational and research purposes only. Not financial advice. Always conduct your own research and consult with financial professionals before making investment decisions.
