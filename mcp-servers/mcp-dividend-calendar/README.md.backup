# DivvyDiary + DEGIRO MCP Server

A Model Context Protocol (MCP) server that finds dividend beasts available on DEGIRO by integrating DivvyDiary's dividend calendar with DEGIRO availability filtering.

## 🎯 Purpose

This MCP server helps European retail investors find legitimate, high-yield dividend opportunities that they can actually purchase through DEGIRO, filtering out the many attractive but inaccessible securities due to regulatory restrictions or unsupported exchanges.

## 🚀 Features

- **Dividend Calendar Scanning**: Scrapes DivvyDiary for dividend opportunities
- **DEGIRO Availability Filtering**: Filters out blocked securities (US ETFs, unsupported exchanges)
- **Yield Analysis**: Ranks opportunities by yield, absolute dividend amount, or pay date
- **European Focus**: Prioritizes European stocks available on DEGIRO
- **Real-time Verification**: Cross-references against DEGIRO's supported exchanges

## 🛠 Installation

```bash
cd mcp-dividend-calendar
npm install
```

## 📋 Available Tools

### 1. `scan_dividend_calendar`
Scan DivvyDiary dividend calendar for DEGIRO-available dividend beasts.

**Parameters:**
- `startDate` (required): Start date (YYYY-MM-DD)
- `endDate` (required): End date (YYYY-MM-DD)
- `minYield` (optional): Minimum yield percentage (default: 5)
- `minDividend` (optional): Minimum dividend amount in EUR (default: 0.5)
- `maxResults` (optional): Maximum results to return (default: 20)

### 2. `verify_degiro_availability`
Verify if specific securities are available on DEGIRO.

**Parameters:**
- `securities` (required): Array of securities with name, ticker, ISIN, exchange

### 3. `find_dividend_beasts`
Find the highest yield dividend opportunities available on DEGIRO.

**Parameters:**
- `dateRange` (optional): Date range: today, week, month, quarter
- `sortBy` (optional): Sort criteria: yield, amount, date
- `filterEuropean` (optional): Filter only European stocks

## 🎮 Usage Examples

### Basic Dividend Scan
```json
{
  "name": "scan_dividend_calendar",
  "arguments": {
    "startDate": "2025-07-28",
    "endDate": "2025-08-28",
    "minYield": 10,
    "minDividend": 1.0,
    "maxResults": 15
  }
}
```

### Find Monthly Dividend Beasts
```json
{
  "name": "find_dividend_beasts",
  "arguments": {
    "dateRange": "month",
    "sortBy": "yield",
    "filterEuropean": true
  }
}
```

### Verify Specific Securities
```json
{
  "name": "verify_degiro_availability",
  "arguments": {
    "securities": [
      {
        "name": "ASML Holding NV",
        "ticker": "ASML",
        "isin": "NL0010273215",
        "exchange": "Euronext"
      }
    ]
  }
}
```

## 🏦 DEGIRO Exchange Support

### ✅ Supported Exchanges
- **LSE** (London Stock Exchange) - UK stocks
- **XETRA** (Germany) - German stocks
- **Euronext** (Amsterdam, Paris, Brussels) - European stocks
- **Oslo Børs** (Norway) - Norwegian stocks
- **Borsa Italiana** (Italy) - Italian stocks
- **BME** (Spain) - Spanish stocks
- **WSE** (Warsaw Stock Exchange) - Polish stocks
- **SIX** (Switzerland) - Swiss stocks

### ❌ Blocked/Restricted
- **US ETFs** - Blocked by PRIIPs regulation (no KID documents)
- **JSE** (South Africa) - Not supported for EU retail
- **ASX** (Australia) - Limited access
- **TSX** (Canada) - Not supported
- **Asian exchanges** - Limited/no access

## 🔧 Running the Server

### As MCP Server
```bash
npm start
```

### Development Mode
```bash
npm run dev
```

### Testing
```bash
npm test
```

## 📊 Output Format

The server returns ranked dividend opportunities with:
- Company name and exchange
- Dividend yield percentage
- Dividend amount (original currency + EUR equivalent)
- Ex-dividend and pay dates
- DEGIRO availability status
- Trading instructions (ticker, ISIN, exchange)

## 🚨 Important Notes

### Regulatory Restrictions
- **PRIIPs Regulation**: Blocks most US ETFs for European retail investors
- **Exchange Access**: Not all global exchanges are available on DEGIRO
- **Currency Risk**: Non-EUR dividends subject to FX fluctuations

### Data Accuracy
- Dividend data scraped from DivvyDiary (third-party source)
- DEGIRO availability based on known exchange support
- Always verify securities in your DEGIRO account before investing

### Risk Warnings
- **High yields (>20%) often indicate distressed companies or unsustainable dividends**
- **Past dividends don't guarantee future payments**
- **Always research companies before investing**

## 🤝 Contributing

This is a simple, focused MCP server. Contributions welcome for:
- Additional exchange support
- Better currency conversion
- Enhanced error handling
- Real-time API integrations

## 📄 License

MIT License - Use at your own risk. Not financial advice.

---

**Disclaimer**: This tool is for informational purposes only. Always do your own research and consult with financial advisors before making investment decisions.
