# DivvyDiary + DEGIRO MCP Server

A Model Context Protocol (MCP) server that finds dividend beasts available on DEGIRO by integrating DivvyDiary's dividend calendar with DEGIRO availability filtering.

## 🎯 Purpose

This MCP server helps European retail investors find legitimate, high-yield dividend opportunities that they can actually purchase through DEGIRO, filtering out the many attractive but inaccessible securities due to regulatory restrictions or unsupported exchanges.

## 🚀 Features

- **Dividend Calendar Scanning**: Scrapes DivvyDiary for dividend opportunities
- **DEGIRO Availability Filtering**: Filters out blocked securities (US ETFs, unsupported exchanges)
- **Yield Analysis**: Ranks opportunities by yield, absolute dividend amount, or pay date
- **European Focus**: Prioritizes European stocks available on DEGIRO
- **Real-time Verification**: Cross-references against DEGIRO's supported exchanges

## 🛠 Installation

```bash
cd mcp-dividend-calendar
npm install
```

## 📋 Available Tools

### 1. `scan_dividend_calendar`
Scan DivvyDiary dividend calendar for DEGIRO-available dividend beasts.

**Parameters:**
- `startDate` (required): Start date (YYYY-MM-DD)
- `endDate` (required): End date (YYYY-MM-DD)
- `minYield` (optional): Minimum yield percentage (default: 5)
- `minDividend` (optional): Minimum dividend amount in EUR (default: 0.5)
- `maxResults` (optional): Maximum results to return (default: 20)

### 2. `verify_degiro_availability`
Verify if specific securities are available on DEGIRO.

**Parameters:**
- `securities` (required): Array of securities with name, ticker, ISIN, exchange

### 3. `find_dividend_beasts`
Find the highest yield dividend opportunities available on DEGIRO.

**Parameters:**
- `dateRange` (optional): Date range: today, week, month, quarter
- `sortBy` (optional): Sort criteria: yield, amount, date
- `filterEuropean` (optional): Filter only European stocks

## 🎮 Usage Examples

### Basic Dividend Scan
```json
{
  "name": "scan_dividend_calendar",
  "arguments": {
    "startDate": "2025-07-28",
    "endDate": "2025-08-28",
    "minYield": 10,
    "minDividend": 1.0,
    "maxResults": 15
  }
}
```

### Find Monthly Dividend Beasts
```json
{
  "name": "find_dividend_beasts",
  "arguments": {
    "dateRange": "month",
    "sortBy": "yield",
    "filterEuropean": true
  }
}
```

### Verify Specific Securities
```json
{
  "name": "verify_degiro_availability",
  "arguments": {
    "securities": [
      {
        "name": "ASML Holding NV",
        "ticker": "ASML",
        "isin": "NL0010273215",
        "exchange": "Euronext"
      }
    ]
  }
}
```

## 🏦 DEGIRO Exchange Support

### ✅ Supported Exchanges
- **LSE** (London Stock Exchange) - UK stocks
- **XETRA** (Germany) - German stocks
- **Euronext** (Amsterdam, Paris, Brussels) - European stocks
- **Oslo Børs** (Norway) - Norwegian stocks
- **Borsa Italiana** (Italy) - Italian stocks
- **BME** (Spain) - Spanish stocks
- **WSE** (Warsaw Stock Exchange) - Polish stocks
- **SIX** (Switzerland) - Swiss stocks

### ❌ Blocked/Restricted
- **US ETFs** - Blocked by PRIIPs regulation (no KID documents)
- **JSE** (South Africa) - Not supported for EU retail
- **ASX** (Australia) - Limited access
- **TSX** (Canada) - Not supported
- **Asian exchanges** - Limited/no access

## 🔧 Running the Server

### As MCP Server
```bash
npm start
```

### Development Mode
```bash
npm run dev
```

### Testing
```bash
npm test
```

## 📊 Output Format

The server returns ranked dividend opportunities with:
- Company name and exchange
- Dividend yield percentage
- Dividend amount (original currency + EUR equivalent)
- Ex-dividend and pay dates
- DEGIRO availability status
- Trading instructions (ticker, ISIN, exchange)

## 🚨 Important Notes

### Regulatory Restrictions
- **PRIIPs Regulation**: Blocks most US ETFs for European retail investors
- **Exchange Access**: Not all global exchanges are available on DEGIRO
- **Currency Risk**: Non-EUR dividends subject to FX fluctuations

### Data Accuracy
- Dividend data scraped from DivvyDiary (third-party source)
- DEGIRO availability based on known exchange support
- Always verify securities in your DEGIRO account before investing

### Risk Warnings
- **High yields (>20%) often indicate distressed companies or unsustainable dividends**
- **Past dividends don't guarantee future payments**
- **Always research companies before investing**

## 🤝 Contributing

This is a simple, focused MCP server. Contributions welcome for:
- Additional exchange support
- Better currency conversion
- Enhanced error handling
- Real-time API integrations

## 📄 License

MIT License - Use at your own risk. Not financial advice.

---

**Disclaimer**: This tool is for informational purposes only. Always do your own research and consult with financial advisors before making investment decisions.


## Tasks

### Extracted Tasks

- [ ] **Dividend Calendar Scanning**: Scrapes DivvyDiary for dividend opportunities - M1
- [ ] **DEGIRO Availability Filtering**: Filters out blocked securities (US ETFs, unsupported exchanges) - M2
- [ ] **Yield Analysis**: Ranks opportunities by yield, absolute dividend amount, or pay date - M3
- [ ] **European Focus**: Prioritizes European stocks available on DEGIRO - M4
- [ ] **Real-time Verification**: Cross-references against DEGIRO's supported exchanges - M5
- [ ] `startDate` (required): Start date (YYYY-MM-DD) - M6
- [ ] `endDate` (required): End date (YYYY-MM-DD) - M7
- [ ] `minYield` (optional): Minimum yield percentage (default: 5) - M8
- [ ] `minDividend` (optional): Minimum dividend amount in EUR (default: 0.5) - M9
- [ ] `maxResults` (optional): Maximum results to return (default: 20) - M10
- [ ] `securities` (required): Array of securities with name, ticker, ISIN, exchange - M11
- [ ] `dateRange` (optional): Date range: today, week, month, quarter - M12
- [ ] `sortBy` (optional): Sort criteria: yield, amount, date - M13
- [ ] `filterEuropean` (optional): Filter only European stocks - M14
- [ ] **LSE** (London Stock Exchange) - UK stocks - M15
- [ ] **XETRA** (Germany) - German stocks - M16
- [ ] **Euronext** (Amsterdam, Paris, Brussels) - European stocks - M17
- [ ] **Oslo Børs** (Norway) - Norwegian stocks - M18
- [ ] **Borsa Italiana** (Italy) - Italian stocks - M19
- [ ] **BME** (Spain) - Spanish stocks - M20
- [ ] **WSE** (Warsaw Stock Exchange) - Polish stocks - M21
- [ ] **SIX** (Switzerland) - Swiss stocks - M22
- [ ] **US ETFs** - Blocked by PRIIPs regulation (no KID documents) - M23
- [ ] **JSE** (South Africa) - Not supported for EU retail - M24
- [ ] **ASX** (Australia) - Limited access - M25
- [ ] **TSX** (Canada) - Not supported - M26
- [ ] **Asian exchanges** - Limited/no access - M27
- [ ] Company name and exchange - M28
- [ ] Dividend yield percentage - M29
- [ ] Dividend amount (original currency + EUR equivalent) - M30
- [ ] Ex-dividend and pay dates - M31
- [ ] DEGIRO availability status - M32
- [ ] Trading instructions (ticker, ISIN, exchange) - M33
- [ ] **PRIIPs Regulation**: Blocks most US ETFs for European retail investors - M34
- [ ] **Exchange Access**: Not all global exchanges are available on DEGIRO - M35
- [ ] **Currency Risk**: Non-EUR dividends subject to FX fluctuations - M36
- [ ] Dividend data scraped from DivvyDiary (third-party source) - M37
- [ ] DEGIRO availability based on known exchange support - M38
- [ ] Always verify securities in your DEGIRO account before investing - M39
- [ ] **High yields (>20%) often indicate distressed companies or unsustainable dividends** - M40
- [ ] **Past dividends don't guarantee future payments** - M41
- [ ] **Always research companies before investing** - M42
- [ ] Additional exchange support - M43
- [ ] Better currency conversion - M44
- [ ] Enhanced error handling - M45
- [ ] Real-time API integrations - M46

### General Tasks

- [ ] Review and update content - M1
- [ ] Add missing information - M2
- [ ] Improve structure - M3
- [ ] Add examples - M4
- [ ] Validate accuracy - M5

