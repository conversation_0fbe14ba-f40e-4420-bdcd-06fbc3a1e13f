
//
//  ClipboardHistory.swift
//  A complete clipboard history manager for macOS
//  Created with Swift and SwiftUI
//

import SwiftUI
import AppKit
import Foundation

// MARK: - Data Models

struct ClipboardItem: Identifiable, Codable {
    let id = UUID()
    let content: String
    let timestamp: Date
    let type: ClipboardType

    enum ClipboardType: String, Codable, CaseIterable {
        case text = "Text"
        case image = "Image" 
        case file = "File"
        case url = "URL"

        var icon: String {
            switch self {
            case .text: return "doc.text"
            case .image: return "photo"
            case .file: return "doc"
            case .url: return "link"
            }
        }
    }
}

// MARK: - Clipboard Manager

class ClipboardManager: ObservableObject {
    @Published var items: [ClipboardItem] = []
    @Published var isMonitoring = true

    private var timer: Timer?
    private var lastChangeCount: Int = 0
    private let maxItems = 1000
    private let pasteboard = NSPasteboard.general

    init() {
        loadItems()
        lastChangeCount = pasteboard.changeCount
        startMonitoring()
    }

    func startMonitoring() {
        guard isMonitoring else { return }

        timer = Timer.scheduledTimer(withTimeInterval: 0.5, repeats: true) { [weak self] _ in
            self?.checkClipboard()
        }
    }

    func stopMonitoring() {
        timer?.invalidate()
        timer = nil
    }

    private func checkClipboard() {
        guard isMonitoring else { return }

        let currentChangeCount = pasteboard.changeCount
        guard currentChangeCount != lastChangeCount else { return }

        lastChangeCount = currentChangeCount
        addCurrentClipboardContent()
    }

    private func addCurrentClipboardContent() {
        var newItem: ClipboardItem?

        // Check for different types of content
        if let string = pasteboard.string(forType: .string), !string.isEmpty {
            let type: ClipboardItem.ClipboardType = string.hasPrefix("http") ? .url : .text
            newItem = ClipboardItem(content: string, timestamp: Date(), type: type)
        } else if let _ = pasteboard.data(forType: .tiff) {
            newItem = ClipboardItem(content: "[Image]", timestamp: Date(), type: .image)
        } else if let fileURL = pasteboard.string(forType: .fileURL) {
            newItem = ClipboardItem(content: fileURL, timestamp: Date(), type: .file)
        }

        if let item = newItem {
            // Avoid duplicates
            if items.first?.content != item.content {
                items.insert(item, at: 0)

                // Limit items
                if items.count > maxItems {
                    items = Array(items.prefix(maxItems))
                }

                saveItems()
            }
        }
    }

    func copyToClipboard(_ item: ClipboardItem) {
        pasteboard.clearContents()
        pasteboard.setString(item.content, forType: .string)

        // Move item to top
        if let index = items.firstIndex(where: { $0.id == item.id }) {
            let movedItem = items.remove(at: index)
            items.insert(movedItem, at: 0)
            saveItems()
        }
    }

    func deleteItem(_ item: ClipboardItem) {
        items.removeAll { $0.id == item.id }
        saveItems()
    }

    func clearAll() {
        items.removeAll()
        saveItems()
    }

    func toggleMonitoring() {
        isMonitoring.toggle()
        if isMonitoring {
            startMonitoring()
        } else {
            stopMonitoring()
        }
    }

    // MARK: - Persistence

    private func saveItems() {
        if let encoded = try? JSONEncoder().encode(items) {
            UserDefaults.standard.set(encoded, forKey: "clipboardItems")
        }
    }

    private func loadItems() {
        if let data = UserDefaults.standard.data(forKey: "clipboardItems"),
           let decoded = try? JSONDecoder().decode([ClipboardItem].self, from: data) {
            items = decoded
        }
    }
}

// MARK: - Views

struct ContentView: View {
    @StateObject private var clipboardManager = ClipboardManager()
    @State private var searchText = ""
    @State private var selectedType: ClipboardItem.ClipboardType?

    var filteredItems: [ClipboardItem] {
        var filtered = clipboardManager.items

        if !searchText.isEmpty {
            filtered = filtered.filter { item in
                item.content.localizedCaseInsensitiveContains(searchText)
            }
        }

        if let type = selectedType {
            filtered = filtered.filter { $0.type == type }
        }

        return filtered
    }

    var body: some View {
        VStack(spacing: 0) {
            // Header
            headerView

            // Search and Filter
            searchAndFilterView

            // Items List
            itemsListView

            // Footer
            footerView
        }
        .frame(width: 400, height: 600)
        .background(Color(NSColor.windowBackgroundColor))
    }

    private var headerView: some View {
        HStack {
            Image(systemName: "doc.on.clipboard")
                .font(.title2)
                .foregroundColor(.accentColor)

            Text("Clipboard History")
                .font(.headline)
                .fontWeight(.semibold)

            Spacer()

            Button(action: clipboardManager.toggleMonitoring) {
                Image(systemName: clipboardManager.isMonitoring ? "pause.circle" : "play.circle")
                    .font(.title2)
                    .foregroundColor(clipboardManager.isMonitoring ? .orange : .green)
            }
            .buttonStyle(PlainButtonStyle())
            .help(clipboardManager.isMonitoring ? "Pause monitoring" : "Resume monitoring")
        }
        .padding()
        .background(Color(NSColor.controlBackgroundColor))
    }

    private var searchAndFilterView: some View {
        VStack(spacing: 8) {
            // Search bar
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondary)

                TextField("Search clipboard history...", text: $searchText)
                    .textFieldStyle(RoundedBorderTextFieldStyle())

                if !searchText.isEmpty {
                    Button("Clear") {
                        searchText = ""
                    }
                    .buttonStyle(PlainButtonStyle())
                    .foregroundColor(.secondary)
                }
            }

            // Type filter
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 8) {
                    Button("All") {
                        selectedType = nil
                    }
                    .buttonStyle(FilterButtonStyle(isSelected: selectedType == nil))

                    ForEach(ClipboardItem.ClipboardType.allCases, id: \.self) { type in
                        Button(type.rawValue) {
                            selectedType = selectedType == type ? nil : type
                        }
                        .buttonStyle(FilterButtonStyle(isSelected: selectedType == type))
                    }
                }
                .padding(.horizontal)
            }
        }
        .padding(.horizontal)
        .padding(.bottom, 8)
    }

    private var itemsListView: some View {
        Group {
            if filteredItems.isEmpty {
                VStack(spacing: 16) {
                    Image(systemName: "doc.on.clipboard")
                        .font(.system(size: 48))
                        .foregroundColor(.secondary)

                    Text(clipboardManager.items.isEmpty ? "No clipboard history yet" : "No items match your search")
                        .font(.headline)
                        .foregroundColor(.secondary)

                    if clipboardManager.items.isEmpty {
                        Text("Copy something to get started!")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else {
                List(filteredItems) { item in
                    ClipboardItemRow(item: item, clipboardManager: clipboardManager)
                }
                .listStyle(PlainListStyle())
            }
        }
    }

    private var footerView: some View {
        HStack {
            Text("\(clipboardManager.items.count) items")
                .font(.caption)
                .foregroundColor(.secondary)

            Spacer()

            if !clipboardManager.items.isEmpty {
                Button("Clear All") {
                    clipboardManager.clearAll()
                }
                .buttonStyle(PlainButtonStyle())
                .foregroundColor(.red)
            }
        }
        .padding()
        .background(Color(NSColor.controlBackgroundColor))
    }
}

struct ClipboardItemRow: View {
    let item: ClipboardItem
    let clipboardManager: ClipboardManager
    @State private var isHovered = false

    var body: some View {
        HStack(spacing: 12) {
            // Type icon
            Image(systemName: item.type.icon)
                .font(.title3)
                .foregroundColor(.accentColor)
                .frame(width: 20)

            // Content
            VStack(alignment: .leading, spacing: 4) {
                Text(item.content)
                    .font(.body)
                    .lineLimit(3)
                    .truncationMode(.tail)

                Text(formatTimestamp(item.timestamp))
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()

            // Action buttons (shown on hover)
            if isHovered {
                HStack(spacing: 8) {
                    Button(action: { clipboardManager.copyToClipboard(item) }) {
                        Image(systemName: "doc.on.doc")
                    }
                    .buttonStyle(PlainButtonStyle())
                    .help("Copy")

                    Button(action: { clipboardManager.deleteItem(item) }) {
                        Image(systemName: "trash")
                    }
                    .buttonStyle(PlainButtonStyle())
                    .foregroundColor(.red)
                    .help("Delete")
                }
            }
        }
        .padding(.vertical, 8)
        .contentShape(Rectangle())
        .onHover { hovering in
            isHovered = hovering
        }
        .onTapGesture {
            clipboardManager.copyToClipboard(item)
        }
    }

    private func formatTimestamp(_ date: Date) -> String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: date, relativeTo: Date())
    }
}

struct FilterButtonStyle: ButtonStyle {
    let isSelected: Bool

    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                RoundedRectangle(cornerRadius: 6)
                    .fill(isSelected ? Color.accentColor : Color(NSColor.controlBackgroundColor))
            )
            .foregroundColor(isSelected ? .white : .primary)
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

// MARK: - App Delegate and Menu Bar

class AppDelegate: NSObject, NSApplicationDelegate {
    var statusItem: NSStatusItem!
    var popover: NSPopover!

    func applicationDidFinishLaunching(_ notification: Notification) {
        setupMenuBar()
        setupPopover()

        // Hide dock icon for menu bar app
        NSApp.setActivationPolicy(.accessory)
    }

    private func setupMenuBar() {
        statusItem = NSStatusBar.system.statusItem(withLength: NSStatusItem.variableLength)

        if let button = statusItem.button {
            button.image = NSImage(systemSymbolName: "doc.on.clipboard", accessibilityDescription: "Clipboard History")
            button.action = #selector(togglePopover)
            button.target = self
        }
    }

    private func setupPopover() {
        popover = NSPopover()
        popover.contentSize = NSSize(width: 400, height: 600)
        popover.behavior = .transient
        popover.contentViewController = NSHostingController(rootView: ContentView())
    }

    @objc func togglePopover() {
        if let button = statusItem.button {
            if popover.isShown {
                popover.performClose(nil)
            } else {
                popover.show(relativeTo: button.bounds, of: button, preferredEdge: NSRectEdge.minY)
            }
        }
    }

    func applicationWillTerminate(_ notification: Notification) {
        // Clean up if needed
    }
}

// MARK: - Main App

@main
struct ClipboardHistoryApp: App {
    @NSApplicationDelegateAdaptor(AppDelegate.self) var appDelegate

    var body: some Scene {
        Settings {
            EmptyView()
        }
    }
}
