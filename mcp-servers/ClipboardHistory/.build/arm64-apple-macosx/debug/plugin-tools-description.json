{"builtTestProducts": [], "copyCommands": {}, "explicitTargetDependencyImportCheckingMode": {"none": {}}, "generatedSourceTargetSet": [], "pluginDescriptions": [], "swiftCommands": {"C.ClipboardHistory-arm64-apple-macosx15.0-debug.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/ClipboardHistory.build/sources", "importPath": "/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/Modules", "inputs": [{"kind": "file", "name": "/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/Sources/ClipboardHistory/main.swift"}, {"kind": "file", "name": "/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}, {"kind": "file", "name": "/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/ClipboardHistory.build/sources"}], "isLibrary": false, "moduleName": "ClipboardHistory", "moduleOutputPath": "/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/Modules/ClipboardHistory.swiftmodule", "objects": ["/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/ClipboardHistory.build/main.swift.o"], "otherArguments": ["-target", "arm64-apple-macosx13.0", "-enable-batch-mode", "-index-store-path", "/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j8", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-Xfrontend", "-entry-point-function-name", "-Xfrontend", "ClipboardHistory_main", "-parse-as-library", "-color-diagnostics", "-swift-version", "5", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g"], "outputFileMapPath": "/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/ClipboardHistory.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/ClipboardHistory.build/main.swift.o"}, {"kind": "file", "name": "/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/Modules/ClipboardHistory.swiftmodule"}], "prepareForIndexing": false, "sources": ["/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/Sources/ClipboardHistory/main.swift"], "tempsPath": "/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/ClipboardHistory.build", "wholeModuleOptimization": false}}, "swiftFrontendCommands": {}, "swiftTargetScanArgs": {"ClipboardHistory": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "ClipboardHistory", "-incremental", "-c", "/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/Sources/ClipboardHistory/main.swift", "-I", "/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/Modules", "-target", "arm64-apple-macosx13.0", "-enable-batch-mode", "-index-store-path", "/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j8", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-Xfrontend", "-entry-point-function-name", "-Xfrontend", "ClipboardHistory_main", "-parse-as-library", "-color-diagnostics", "-swift-version", "5", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-driver-use-frontend-path", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"]}, "targetDependencyMap": {"ClipboardHistory": []}, "testDiscoveryCommands": {}, "testEntryPointCommands": {}, "traitConfiguration": {"enableAllTraits": false}, "writeCommands": {"/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/ClipboardHistory-entitlement.plist": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<entitlement-plist>"}, {"kind": "virtual", "name": "<com.apple.security.get-task-allow>"}], "outputFilePath": "/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/ClipboardHistory-entitlement.plist"}, "/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/ClipboardHistory.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/Sources/ClipboardHistory/main.swift"}], "outputFilePath": "/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/ClipboardHistory.build/sources"}, "/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/ClipboardHistory.product/Objects.LinkFileList": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<link-file-list>"}, {"kind": "file", "name": "/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/ClipboardHistory.build/main.swift.o"}], "outputFilePath": "/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/ClipboardHistory.product/Objects.LinkFileList"}, "/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt": {"alwaysOutOfDate": true, "inputs": [{"kind": "virtual", "name": "<swift-get-version>"}, {"kind": "file", "name": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"}], "outputFilePath": "/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}}}