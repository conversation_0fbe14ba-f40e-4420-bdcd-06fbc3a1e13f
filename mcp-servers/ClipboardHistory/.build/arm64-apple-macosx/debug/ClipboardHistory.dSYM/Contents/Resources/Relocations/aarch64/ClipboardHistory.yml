---
triple:          'arm64-apple-darwin'
binary-path:     '/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/ClipboardHistory'
relocations:
  - { offset: 0xE7E68, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV2id10Foundation4UUIDVvpfi', symObjAddr: 0x0, symBinAddr: 0x1000012C0, symSize: 0x14 }
  - { offset: 0xE7E80, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV2id10Foundation4UUIDVvpfi', symObjAddr: 0x0, symBinAddr: 0x1000012C0, symSize: 0x14 }
  - { offset: 0xE81B2, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemVMa', symObjAddr: 0x98, symBinAddr: 0x100001358, symSize: 0x60 }
  - { offset: 0xE81C6, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV10CodingKeys33_29BA33022BC77D68CF322D73212E107BLLOAFSHAAWl', symObjAddr: 0xD18, symBinAddr: 0x100001FD8, symSize: 0x68 }
  - { offset: 0xE81DA, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV10CodingKeys33_29BA33022BC77D68CF322D73212E107BLLOAFs0D3KeyAAWl', symObjAddr: 0xFB4, symBinAddr: 0x100002274, symSize: 0x68 }
  - { offset: 0xE81EE, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledName, symObjAddr: 0x14E0, symBinAddr: 0x1000027A0, symSize: 0x68 }
  - { offset: 0xE8202, size: 0x8, addend: 0x0, symName: ___swift_project_boxed_opaque_existential_1, symObjAddr: 0x1548, symBinAddr: 0x100002808, symSize: 0x4C }
  - { offset: 0xE8216, size: 0x8, addend: 0x0, symName: '_$s10Foundation4UUIDVACSEAAWl', symObjAddr: 0x1594, symBinAddr: 0x100002854, symSize: 0x6C }
  - { offset: 0xE822A, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DateVACSEAAWl', symObjAddr: 0x1600, symBinAddr: 0x1000028C0, symSize: 0x6C }
  - { offset: 0xE823E, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV0A4TypeOAESEAAWl', symObjAddr: 0x166C, symBinAddr: 0x10000292C, symSize: 0x68 }
  - { offset: 0xE8252, size: 0x8, addend: 0x0, symName: ___swift_destroy_boxed_opaque_existential_1, symObjAddr: 0x1BDC, symBinAddr: 0x100002E9C, symSize: 0x60 }
  - { offset: 0xE8266, size: 0x8, addend: 0x0, symName: '_$sSSWOh', symObjAddr: 0x1C3C, symBinAddr: 0x100002EFC, symSize: 0x28 }
  - { offset: 0xE827A, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DateVACSeAAWl', symObjAddr: 0x1C64, symBinAddr: 0x100002F24, symSize: 0x6C }
  - { offset: 0xE828E, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV0A4TypeOAESeAAWl', symObjAddr: 0x1CD0, symBinAddr: 0x100002F90, symSize: 0x68 }
  - { offset: 0xE82A2, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemVWOc', symObjAddr: 0x1D38, symBinAddr: 0x100002FF8, symSize: 0xF8 }
  - { offset: 0xE82B6, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemVWOh', symObjAddr: 0x1E30, symBinAddr: 0x1000030F0, symSize: 0x94 }
  - { offset: 0xE82CA, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC5itemsSayAA0A4ItemVGvpfP', symObjAddr: 0x1F6C, symBinAddr: 0x10000322C, symSize: 0x64 }
  - { offset: 0xE82F6, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC5itemsSayAA0A4ItemVGvpACTK', symObjAddr: 0x206C, symBinAddr: 0x10000332C, symSize: 0x60 }
  - { offset: 0xE830E, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC5itemsSayAA0A4ItemVGvpACTk', symObjAddr: 0x20CC, symBinAddr: 0x10000338C, symSize: 0x6C }
  - { offset: 0xE8711, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC6_items33_29BA33022BC77D68CF322D73212E107BLL7Combine9PublishedVySayAA0A4ItemVGGvpfi', symObjAddr: 0x23EC, symBinAddr: 0x1000036AC, symSize: 0x30 }
  - { offset: 0xE8729, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC6$items7Combine9PublishedV9PublisherVySayAA0A4ItemVG_GvpACTK', symObjAddr: 0x241C, symBinAddr: 0x1000036DC, symSize: 0xC8 }
  - { offset: 0xE8741, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC6$items7Combine9PublishedV9PublisherVySayAA0A4ItemVG_GvpACTk', symObjAddr: 0x24E4, symBinAddr: 0x1000037A4, symSize: 0xB4 }
  - { offset: 0xE8759, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC12isMonitoringSbvpfP', symObjAddr: 0x29D0, symBinAddr: 0x100003C90, symSize: 0x40 }
  - { offset: 0xE8785, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC12isMonitoringSbvpACTK', symObjAddr: 0x2A10, symBinAddr: 0x100003CD0, symSize: 0x64 }
  - { offset: 0xE879D, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC12isMonitoringSbvpACTk', symObjAddr: 0x2A74, symBinAddr: 0x100003D34, symSize: 0x60 }
  - { offset: 0xE87B5, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC13_isMonitoring33_29BA33022BC77D68CF322D73212E107BLL7Combine9PublishedVySbGvpfi', symObjAddr: 0x2D7C, symBinAddr: 0x10000403C, symSize: 0xC }
  - { offset: 0xE87CD, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC13$isMonitoring7Combine9PublishedV9PublisherVySb_GvpACTK', symObjAddr: 0x2D88, symBinAddr: 0x100004048, symSize: 0xC8 }
  - { offset: 0xE87E5, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC13$isMonitoring7Combine9PublishedV9PublisherVySb_GvpACTk', symObjAddr: 0x2E50, symBinAddr: 0x100004110, symSize: 0xB4 }
  - { offset: 0xE87FD, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC5timer33_29BA33022BC77D68CF322D73212E107BLLSo7NSTimerCSgvpfi', symObjAddr: 0x333C, symBinAddr: 0x1000045FC, symSize: 0x8 }
  - { offset: 0xE8815, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC15lastChangeCount33_29BA33022BC77D68CF322D73212E107BLLSivpfi', symObjAddr: 0x34B0, symBinAddr: 0x100004770, symSize: 0x8 }
  - { offset: 0xE882D, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC8maxItems33_29BA33022BC77D68CF322D73212E107BLLSivpfi', symObjAddr: 0x35EC, symBinAddr: 0x1000048AC, symSize: 0xC }
  - { offset: 0xE8845, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC10pasteboard33_29BA33022BC77D68CF322D73212E107BLLSo12NSPasteboardCvpfi', symObjAddr: 0x3608, symBinAddr: 0x1000048C8, symSize: 0x2C }
  - { offset: 0xE885D, size: 0x8, addend: 0x0, symName: '_$sSo7NSTimerCIeghg_ABIeyBhy_TR', symObjAddr: 0x3B60, symBinAddr: 0x100004E20, symSize: 0x70 }
  - { offset: 0xE8875, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC15startMonitoringyyFySo7NSTimerCYbcfU_TA', symObjAddr: 0x5AB8, symBinAddr: 0x100006D78, symSize: 0x8 }
  - { offset: 0xE8889, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x5AC0, symBinAddr: 0x100006D80, symSize: 0x44 }
  - { offset: 0xE889D, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x5B04, symBinAddr: 0x100006DC4, symSize: 0x18 }
  - { offset: 0xE88B1, size: 0x8, addend: 0x0, symName: '_$sSo7NSTimerCSgWOh', symObjAddr: 0x5B1C, symBinAddr: 0x100006DDC, symSize: 0x30 }
  - { offset: 0xE88C5, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemVSgWOc', symObjAddr: 0x5B4C, symBinAddr: 0x100006E0C, symSize: 0x160 }
  - { offset: 0xE88D9, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemVSgWOh', symObjAddr: 0x5CAC, symBinAddr: 0x100006F6C, symSize: 0xB8 }
  - { offset: 0xE88ED, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemVWOb', symObjAddr: 0x5D64, symBinAddr: 0x100007024, symSize: 0xD4 }
  - { offset: 0xE8901, size: 0x8, addend: 0x0, symName: '_$sSay16ClipboardHistory0A4ItemVGSayxGSlsWl', symObjAddr: 0x5E38, symBinAddr: 0x1000070F8, symSize: 0x6C }
  - { offset: 0xE8915, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledNameAbstract, symObjAddr: 0x5EA4, symBinAddr: 0x100007164, symSize: 0x70 }
  - { offset: 0xE8929, size: 0x8, addend: 0x0, symName: '_$sSay16ClipboardHistory0A4ItemVGWOh', symObjAddr: 0x5F14, symBinAddr: 0x1000071D4, symSize: 0x28 }
  - { offset: 0xE893D, size: 0x8, addend: 0x0, symName: '_$sSSSg_AAtWOh', symObjAddr: 0x5F3C, symBinAddr: 0x1000071FC, symSize: 0x34 }
  - { offset: 0xE8951, size: 0x8, addend: 0x0, symName: '_$ss10ArraySliceVy16ClipboardHistory0C4ItemVGAByxGSTsWl', symObjAddr: 0x5F70, symBinAddr: 0x100007230, symSize: 0x6C }
  - { offset: 0xE8965, size: 0x8, addend: 0x0, symName: '_$sSSSgWOh', symObjAddr: 0x5FDC, symBinAddr: 0x10000729C, symSize: 0x28 }
  - { offset: 0xE8979, size: 0x8, addend: 0x0, symName: '_$sSSSgWOc', symObjAddr: 0x6004, symBinAddr: 0x1000072C4, symSize: 0x3C }
  - { offset: 0xE898D, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV16clipboardManager33_29BA33022BC77D68CF322D73212E107BLLAA0aF0CvpfP', symObjAddr: 0x6178, symBinAddr: 0x100007438, symSize: 0x8C }
  - { offset: 0xE8BBB, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV10searchText33_29BA33022BC77D68CF322D73212E107BLLSSvpfP', symObjAddr: 0x6400, symBinAddr: 0x1000076C0, symSize: 0x90 }
  - { offset: 0xE8BE7, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV11_searchText33_29BA33022BC77D68CF322D73212E107BLL7SwiftUI5StateVySSGvpfi', symObjAddr: 0x65DC, symBinAddr: 0x10000789C, symSize: 0x28 }
  - { offset: 0xE8BFF, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV12selectedType33_29BA33022BC77D68CF322D73212E107BLLAA0A4ItemV0aF0OSgvpfP', symObjAddr: 0x6770, symBinAddr: 0x100007A30, symSize: 0x50 }
  - { offset: 0xE8C2B, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV13_selectedType33_29BA33022BC77D68CF322D73212E107BLL7SwiftUI5StateVyAA0A4ItemV0aF0OSgGvpfi', symObjAddr: 0x68C4, symBinAddr: 0x100007B84, symSize: 0x8 }
  - { offset: 0xE8DC0, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ItemRowV9isHovered33_29BA33022BC77D68CF322D73212E107BLLSbvpfP', symObjAddr: 0xD88C, symBinAddr: 0x10000EB4C, symSize: 0x50 }
  - { offset: 0xE8DED, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ItemRowV10_isHovered33_29BA33022BC77D68CF322D73212E107BLL7SwiftUI5StateVySbGvpfi', symObjAddr: 0xDA14, symBinAddr: 0x10000ECD4, symSize: 0xC }
  - { offset: 0xE8E05, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11AppDelegateC10statusItemSo08NSStatusF0CSgvpfi', symObjAddr: 0x1099C, symBinAddr: 0x100011C5C, symSize: 0x8 }
  - { offset: 0xE8E1D, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11AppDelegateC10statusItemSo08NSStatusF0CSgvpACTK', symObjAddr: 0x109A4, symBinAddr: 0x100011C64, symSize: 0x80 }
  - { offset: 0xE8E35, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11AppDelegateC10statusItemSo08NSStatusF0CSgvpACTk', symObjAddr: 0x10A24, symBinAddr: 0x100011CE4, symSize: 0x8C }
  - { offset: 0xE9038, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11AppDelegateC7popoverSo9NSPopoverCSgvpfi', symObjAddr: 0x10C3C, symBinAddr: 0x100011EFC, symSize: 0x8 }
  - { offset: 0xE9050, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11AppDelegateC7popoverSo9NSPopoverCSgvpACTK', symObjAddr: 0x10C44, symBinAddr: 0x100011F04, symSize: 0x80 }
  - { offset: 0xE9068, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11AppDelegateC7popoverSo9NSPopoverCSgvpACTk', symObjAddr: 0x10CC4, symBinAddr: 0x100011F84, symSize: 0x8C }
  - { offset: 0xE9080, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11AppDelegateCfETo', symObjAddr: 0x1205C, symBinAddr: 0x10001331C, symSize: 0x48 }
  - { offset: 0xE91A5, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0aB3AppV12_appDelegate33_29BA33022BC77D68CF322D73212E107BLL7SwiftUI013NSApplicationE7AdaptorVyAA0cE0CGvpfi', symObjAddr: 0x1215C, symBinAddr: 0x10001341C, symSize: 0x34 }
  - { offset: 0xE91BD, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0aB3AppV11appDelegateAC7SwiftUI013NSApplicationE7AdaptorVyAA0cE0CG_tcfcfA_', symObjAddr: 0x1252C, symBinAddr: 0x1000137EC, symSize: 0x34 }
  - { offset: 0xE91DC, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemVSgWOd', symObjAddr: 0x12624, symBinAddr: 0x1000138E4, symSize: 0x264 }
  - { offset: 0xE91F0, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOWOe', symObjAddr: 0x12888, symBinAddr: 0x100013B48, symSize: 0x78 }
  - { offset: 0xE9204, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC06copyToA0yyAA0A4ItemVFSbAFXEfU_TA', symObjAddr: 0x12900, symBinAddr: 0x100013BC0, symSize: 0x28 }
  - { offset: 0xE9218, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC10deleteItemyyAA0aE0VFSbAFXEfU_TA', symObjAddr: 0x12928, symBinAddr: 0x100013BE8, symSize: 0x28 }
  - { offset: 0xE922C, size: 0x8, addend: 0x0, symName: '_$sSay16ClipboardHistory0A4ItemVGSayxGSMsWl', symObjAddr: 0x12950, symBinAddr: 0x100013C10, symSize: 0x6C }
  - { offset: 0xE9240, size: 0x8, addend: 0x0, symName: '_$sSay16ClipboardHistory0A4ItemVGSayxGSmsWl', symObjAddr: 0x129BC, symBinAddr: 0x100013C7C, symSize: 0x6C }
  - { offset: 0xE9254, size: 0x8, addend: 0x0, symName: '_$sSay16ClipboardHistory0A4ItemVGSayxGSEsSERzlWl', symObjAddr: 0x12A28, symBinAddr: 0x100013CE8, symSize: 0x78 }
  - { offset: 0xE9268, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemVACSEAAWl', symObjAddr: 0x12AA0, symBinAddr: 0x100013D60, symSize: 0x6C }
  - { offset: 0xE927C, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOWOy', symObjAddr: 0x12B0C, symBinAddr: 0x100013DCC, symSize: 0x78 }
  - { offset: 0xE9290, size: 0x8, addend: 0x0, symName: '_$sSay16ClipboardHistory0A4ItemVGSayxGSesSeRzlWl', symObjAddr: 0x12B84, symBinAddr: 0x100013E44, symSize: 0x78 }
  - { offset: 0xE92A4, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemVACSeAAWl', symObjAddr: 0x12BFC, symBinAddr: 0x100013EBC, symSize: 0x6C }
  - { offset: 0xE92B8, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerCMa', symObjAddr: 0x12C68, symBinAddr: 0x100013F28, symSize: 0x60 }
  - { offset: 0xE92CC, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerCAC7Combine16ObservableObjectAAWl', symObjAddr: 0x12CC8, symBinAddr: 0x100013F88, symSize: 0x6C }
  - { offset: 0xE92E0, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI11StateObjectV7StorageOy16ClipboardHistory0F7ManagerC_GWOy', symObjAddr: 0x12D34, symBinAddr: 0x100013FF4, symSize: 0x3C }
  - { offset: 0xE92F4, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI11StateObjectV7StorageOy16ClipboardHistory0F7ManagerC_GWOe', symObjAddr: 0x12D70, symBinAddr: 0x100014030, symSize: 0x3C }
  - { offset: 0xE9308, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI5StateVySSGWOr', symObjAddr: 0x12DAC, symBinAddr: 0x10001406C, symSize: 0x3C }
  - { offset: 0xE931C, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI5StateVySSGWOh', symObjAddr: 0x12DE8, symBinAddr: 0x1000140A8, symSize: 0x34 }
  - { offset: 0xE9330, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI5StateVySSGWOs', symObjAddr: 0x12E1C, symBinAddr: 0x1000140DC, symSize: 0x3C }
  - { offset: 0xE9344, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI5StateVy16ClipboardHistory0D4ItemV0D4TypeOSgGWOr', symObjAddr: 0x12E58, symBinAddr: 0x100014118, symSize: 0x28 }
  - { offset: 0xE9358, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI5StateVy16ClipboardHistory0D4ItemV0D4TypeOSgGWOh', symObjAddr: 0x12E80, symBinAddr: 0x100014140, symSize: 0x28 }
  - { offset: 0xE936C, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI5StateVy16ClipboardHistory0D4ItemV0D4TypeOSgGWOs', symObjAddr: 0x12EA8, symBinAddr: 0x100014168, symSize: 0x28 }
  - { offset: 0xE9380, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV13filteredItemsSayAA0A4ItemVGvgSbAFXEfU0_TA', symObjAddr: 0x12ED0, symBinAddr: 0x100014190, symSize: 0x28 }
  - { offset: 0xE9394, size: 0x8, addend: 0x0, symName: '_$sSay16ClipboardHistory0A4ItemVGSayxGs14_ArrayProtocolsWl', symObjAddr: 0x12EF8, symBinAddr: 0x1000141B8, symSize: 0x6C }
  - { offset: 0xE93A8, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewVWOr', symObjAddr: 0x12F64, symBinAddr: 0x100014224, symSize: 0x68 }
  - { offset: 0xE93BC, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV13filteredItemsSayAA0A4ItemVGvgSbAFXEfU_TA', symObjAddr: 0x12FCC, symBinAddr: 0x10001428C, symSize: 0x28 }
  - { offset: 0xE93D0, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewVWOs', symObjAddr: 0x12FF4, symBinAddr: 0x1000142B4, symSize: 0x68 }
  - { offset: 0xE93E4, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV4bodyQrvg7SwiftUI05TupleD0VyAC06headerD033_29BA33022BC77D68CF322D73212E107BLLQrvpQOy_Qo__AC015searchAndFilterD0AILLQrvpQOy_Qo_AC09itemsListD0AILLQrvpQOy_Qo_AC06footerD0AILLQrvpQOy_Qo_tGyXEfU_TA', symObjAddr: 0x1305C, symBinAddr: 0x10001431C, symSize: 0x8 }
  - { offset: 0xE93F8, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI9TupleViewVyAA15ModifiedContentVyAEyAA6HStackVyACyAEyAEyAA5ImageVAA30_EnvironmentKeyWritingModifierVyAA4FontVSgGGAKyAA5ColorVSgGG_AA4TextVAA6SpacerVAA0D0PAAE4helpyQrAA015LocalizedStringJ0VFQOyA_AAE11buttonStyleyQrqd__AA015PrimitiveButtonU0Rd__lFQOyAA0W0VyAUG_AA05PlainwU0VQo__Qo_tGGAA14_PaddingLayoutVGAA011_BackgrounduL0VyARGG_AEyAEyAA6VStackVyACyAGyACyAEyAiTG_A_AAE09textFieldU0yQrqd__AA0o5FieldU0Rd__lFQOyAA0O5FieldVyAWG_AA013RoundedBordero5FieldU0VQo_AEyA_AAEA3_yQrqd__AAA4_Rd__lFQOyA6_yAWG_A9_Qo_ATGSgtGG_AA06ScrollD0VyAEyAGyACyA_AAEA3_yQrqd__AA0wU0Rd__lFQOyA32__16ClipboardHistory06FilterwU0VQo__AA7ForEachVySayA41_13ClipboardItemV13ClipboardTypeOGA50_A44_GtGGA15_GGtGGA15_GA15_GAA5GroupVyAA012_ConditionalF0VyAEyA22_yACyAU_A2WSgtGGAA010_FlexFrameZ0VGA_AAE04listU0yQrqd__AA04ListU0Rd__lFQOyAA4ListVys5NeverOA46_ySayA48_G10Foundation4UUIDVA41_16ClipboardItemRowVGG_AA0x4ListU0VQo_GGAEyAEyAGyACyAW_AYA35_tGGA15_GA19_GtGACyxGAazAWl', symObjAddr: 0x13064, symBinAddr: 0x100014324, symSize: 0x6C }
  - { offset: 0xE940C, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI6VStackVyAA9TupleViewVyAA15ModifiedContentVyAGyAA6HStackVyAEyAGyAGyAA5ImageVAA30_EnvironmentKeyWritingModifierVyAA4FontVSgGGAMyAA5ColorVSgGG_AA4TextVAA6SpacerVAA0E0PAAE4helpyQrAA015LocalizedStringK0VFQOyA1_AAE11buttonStyleyQrqd__AA015PrimitiveButtonV0Rd__lFQOyAA0X0VyAWG_AA05PlainxV0VQo__Qo_tGGAA14_PaddingLayoutVGAA011_BackgroundvM0VyATGG_AGyAGyACyAEyAIyAEyAGyAkVG_A1_AAE09textFieldV0yQrqd__AA0p5FieldV0Rd__lFQOyAA0P5FieldVyAYG_AA013RoundedBorderp5FieldV0VQo_AGyA1_AAEA5_yQrqd__AAA6_Rd__lFQOyA8_yAYG_A11_Qo_AVGSgtGG_AA06ScrollE0VyAGyAIyAEyA1_AAEA5_yQrqd__AA0xV0Rd__lFQOyA32__16ClipboardHistory06FilterxV0VQo__AA7ForEachVySayA41_13ClipboardItemV13ClipboardTypeOGA50_A44_GtGGA17_GGtGGA17_GA17_GAA5GroupVyAA012_ConditionalG0VyAGyACyAEyAW_A2YSgtGGAA16_FlexFrameLayoutVGA1_AAE04listV0yQrqd__AA04ListV0Rd__lFQOyAA4ListVys5NeverOA46_ySayA48_G10Foundation4UUIDVA41_16ClipboardItemRowVGG_AA0y4ListV0VQo_GGAGyAGyAIyAEyAY_A_A35_tGGA17_GA21_GtGGACyxGAAA0_AAWl', symObjAddr: 0x130D0, symBinAddr: 0x100014390, symSize: 0x6C }
  - { offset: 0xE9420, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI4TextV7StorageOWOe', symObjAddr: 0x13620, symBinAddr: 0x1000148E0, symSize: 0x40 }
  - { offset: 0xE9434, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyAA6VStackVyAA9TupleViewVyACyACyAA6HStackVyAGyACyACyAA5ImageVAA30_EnvironmentKeyWritingModifierVyAA4FontVSgGGAMyAA5ColorVSgGG_AA4TextVAA6SpacerVAA0G0PAAE4helpyQrAA015LocalizedStringK0VFQOyA1_AAE11buttonStyleyQrqd__AA015PrimitiveButtonV0Rd__lFQOyAA0X0VyAWG_AA05PlainxV0VQo__Qo_tGGAA14_PaddingLayoutVGAA011_BackgroundvM0VyATGG_ACyACyAEyAGyAIyAGyACyAkVG_A1_AAE09textFieldV0yQrqd__AA0p5FieldV0Rd__lFQOyAA0P5FieldVyAYG_AA013RoundedBorderp5FieldV0VQo_ACyA1_AAEA5_yQrqd__AAA6_Rd__lFQOyA8_yAYG_A11_Qo_AVGSgtGG_AA06ScrollG0VyACyAIyAGyA1_AAEA5_yQrqd__AA0xV0Rd__lFQOyA32__16ClipboardHistory06FilterxV0VQo__AA7ForEachVySayA41_13ClipboardItemV13ClipboardTypeOGA50_A44_GtGGA17_GGtGGA17_GA17_GAA5GroupVyAA012_ConditionalD0VyACyAEyAGyAW_A2YSgtGGAA16_FlexFrameLayoutVGA1_AAE04listV0yQrqd__AA04ListV0Rd__lFQOyAA4ListVys5NeverOA46_ySayA48_G10Foundation4UUIDVA41_16ClipboardItemRowVGG_AA0y4ListV0VQo_GGACyACyAIyAGyAY_A_A35_tGGA17_GA21_GtGGAA12_FrameLayoutVGACyxq_GAAA0_A2AA0_RzAA0gM0R_rlWl', symObjAddr: 0x13660, symBinAddr: 0x100014920, symSize: 0x84 }
  - { offset: 0xE9448, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI5ColorVAcA10ShapeStyleAAWl', symObjAddr: 0x136E4, symBinAddr: 0x1000149A4, symSize: 0x64 }
  - { offset: 0xE945C, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI5ColorVWOh', symObjAddr: 0x13748, symBinAddr: 0x100014A08, symSize: 0x28 }
  - { offset: 0xE9470, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyACyAA6VStackVyAA9TupleViewVyACyACyAA6HStackVyAGyACyACyAA5ImageVAA30_EnvironmentKeyWritingModifierVyAA4FontVSgGGAMyAA5ColorVSgGG_AA4TextVAA6SpacerVAA0G0PAAE4helpyQrAA015LocalizedStringK0VFQOyA1_AAE11buttonStyleyQrqd__AA015PrimitiveButtonV0Rd__lFQOyAA0X0VyAWG_AA05PlainxV0VQo__Qo_tGGAA14_PaddingLayoutVGAA011_BackgroundvM0VyATGG_ACyACyAEyAGyAIyAGyACyAkVG_A1_AAE09textFieldV0yQrqd__AA0p5FieldV0Rd__lFQOyAA0P5FieldVyAYG_AA013RoundedBorderp5FieldV0VQo_ACyA1_AAEA5_yQrqd__AAA6_Rd__lFQOyA8_yAYG_A11_Qo_AVGSgtGG_AA06ScrollG0VyACyAIyAGyA1_AAEA5_yQrqd__AA0xV0Rd__lFQOyA32__16ClipboardHistory06FilterxV0VQo__AA7ForEachVySayA41_13ClipboardItemV13ClipboardTypeOGA50_A44_GtGGA17_GGtGGA17_GA17_GAA5GroupVyAA012_ConditionalD0VyACyAEyAGyAW_A2YSgtGGAA16_FlexFrameLayoutVGA1_AAE04listV0yQrqd__AA04ListV0Rd__lFQOyAA4ListVys5NeverOA46_ySayA48_G10Foundation4UUIDVA41_16ClipboardItemRowVGG_AA0y4ListV0VQo_GGACyACyAIyAGyAY_A_A35_tGGA17_GA21_GtGGAA12_FrameLayoutVGA21_GACyxq_GAAA0_A2AA0_RzAA0gM0R_rlWl', symObjAddr: 0x13C54, symBinAddr: 0x100014F14, symSize: 0x88 }
  - { offset: 0xE9484, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI24_BackgroundStyleModifierVyAA5ColorVGACyxGAA04ViewE0AAWl', symObjAddr: 0x13CDC, symBinAddr: 0x100014F9C, symSize: 0x6C }
  - { offset: 0xE9498, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI4TextV7StorageOWOy', symObjAddr: 0x14E40, symBinAddr: 0x100016100, symSize: 0x40 }
  - { offset: 0xE94AC, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV06headerD033_29BA33022BC77D68CF322D73212E107BLLQrvg7SwiftUI05TupleD0VyAF0D0PAFE15foregroundColoryQrAF0P0VSgFQOyAjFE4fontyQrAF4FontVSgFQOyAF5ImageV_Qo__Qo__AF4TextVAF6SpacerVAjFE4helpyQrAF18LocalizedStringKeyVFQOyAjFE11buttonStyleyQrqd__AF20PrimitiveButtonStyleRd__lFQOyAF6ButtonVyAVG_AF16PlainButtonStyleVQo__Qo_tGyXEfU_TA', symObjAddr: 0x14E80, symBinAddr: 0x100016140, symSize: 0x8 }
  - { offset: 0xE94C0, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI9TupleViewVyAA15ModifiedContentVyAEyAA5ImageVAA30_EnvironmentKeyWritingModifierVyAA4FontVSgGGAIyAA5ColorVSgGG_AA4TextVAA6SpacerVAA0D0PAAE4helpyQrAA015LocalizedStringI0VFQOyAyAE11buttonStyleyQrqd__AA015PrimitiveButtonT0Rd__lFQOyAA0V0VyASG_AA05PlainvT0VQo__Qo_tGACyxGAaxAWl', symObjAddr: 0x14E88, symBinAddr: 0x100016148, symSize: 0x6C }
  - { offset: 0xE94D4, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI6HStackVyAA9TupleViewVyAA15ModifiedContentVyAGyAA5ImageVAA30_EnvironmentKeyWritingModifierVyAA4FontVSgGGAKyAA5ColorVSgGG_AA4TextVAA6SpacerVAA0E0PAAE4helpyQrAA015LocalizedStringJ0VFQOyA_AAE11buttonStyleyQrqd__AA015PrimitiveButtonU0Rd__lFQOyAA0W0VyAUG_AA05PlainwU0VQo__Qo_tGGACyxGAazAWl', symObjAddr: 0x14EF4, symBinAddr: 0x1000161B4, symSize: 0x6C }
  - { offset: 0xE94E8, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyAA6HStackVyAA9TupleViewVyACyACyAA5ImageVAA30_EnvironmentKeyWritingModifierVyAA4FontVSgGGAKyAA5ColorVSgGG_AA4TextVAA6SpacerVAA0G0PAAE4helpyQrAA015LocalizedStringJ0VFQOyA_AAE11buttonStyleyQrqd__AA015PrimitiveButtonU0Rd__lFQOyAA0W0VyAUG_AA05PlainwU0VQo__Qo_tGGAA14_PaddingLayoutVGACyxq_GAaz2aZRzAA0gL0R_rlWl', symObjAddr: 0x15038, symBinAddr: 0x1000162F8, symSize: 0x84 }
  - { offset: 0xE94FC, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV015searchAndFilterD033_29BA33022BC77D68CF322D73212E107BLLQrvg7SwiftUI05TupleD0VyAF6HStackVyAHyAF0D0PAFE15foregroundColoryQrAF0S0VSgFQOyAF5ImageV_Qo__AlFE14textFieldStyleyQrqd__AF04TextvW0Rd__lFQOyAF0xV0VyAF0X0VG_AF013RoundedBorderxvW0VQo_AlFEAMyQrAPFQOyAlFE06buttonW0yQrqd__AF015PrimitiveButtonW0Rd__lFQOyAF6ButtonVyAYG_AF011PlainButtonW0VQo__Qo_SgtGG_AF06ScrollD0VyAlFE7paddingyQrAF4EdgeO3SetV_12CoreGraphics7CGFloatVSgtFQOyAJyAHyAlFEA2_yQrqd__AF06ButtonW0Rd__lFQOyA6__AA0g6ButtonW0VQo__AF7ForEachVySayAA0A4ItemV0A4TypeOGA34_A28_GtGG_Qo_GtGyXEfU_TA', symObjAddr: 0x15194, symBinAddr: 0x100016454, symSize: 0x8 }
  - { offset: 0xE9510, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI9TupleViewVyAA6HStackVyACyAA15ModifiedContentVyAA5ImageVAA30_EnvironmentKeyWritingModifierVyAA5ColorVSgGG_AA0D0PAAE14textFieldStyleyQrqd__AA04TextoP0Rd__lFQOyAA0qO0VyAA0Q0VG_AA013RoundedBorderqoP0VQo_AGyArAE06buttonP0yQrqd__AA015PrimitiveButtonP0Rd__lFQOyAA0V0VyAXG_AA05PlainvP0VQo_AOGSgtGG_AA06ScrollD0VyAGyAEyACyArAEA1_yQrqd__AA0vP0Rd__lFQOyA5__16ClipboardHistory06FiltervP0VQo__AA7ForEachVySayA16_0Y4ItemV0Y4TypeOGA25_A19_GtGGAA14_PaddingLayoutVGGtGACyxGAaqAWl', symObjAddr: 0x1519C, symBinAddr: 0x10001645C, symSize: 0x6C }
  - { offset: 0xE9524, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI6VStackVyAA9TupleViewVyAA6HStackVyAEyAA15ModifiedContentVyAA5ImageVAA30_EnvironmentKeyWritingModifierVyAA5ColorVSgGG_AA0E0PAAE14textFieldStyleyQrqd__AA04TextpQ0Rd__lFQOyAA0rP0VyAA0R0VG_AA013RoundedBorderrpQ0VQo_AIyAtAE06buttonQ0yQrqd__AA015PrimitiveButtonQ0Rd__lFQOyAA0W0VyAZG_AA05PlainwQ0VQo_AQGSgtGG_AA06ScrollE0VyAIyAGyAEyAtAEA3_yQrqd__AA0wQ0Rd__lFQOyA7__16ClipboardHistory06FilterwQ0VQo__AA7ForEachVySayA18_0Z4ItemV0Z4TypeOGA27_A21_GtGGAA14_PaddingLayoutVGGtGGACyxGAasAWl', symObjAddr: 0x15208, symBinAddr: 0x1000164C8, symSize: 0x6C }
  - { offset: 0xE9538, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyAA6VStackVyAA9TupleViewVyAA6HStackVyAGyACyAA5ImageVAA30_EnvironmentKeyWritingModifierVyAA5ColorVSgGG_AA0G0PAAE14textFieldStyleyQrqd__AA04TextpQ0Rd__lFQOyAA0rP0VyAA0R0VG_AA013RoundedBorderrpQ0VQo_ACyAtAE06buttonQ0yQrqd__AA015PrimitiveButtonQ0Rd__lFQOyAA0W0VyAZG_AA05PlainwQ0VQo_AQGSgtGG_AA06ScrollG0VyACyAIyAGyAtAEA3_yQrqd__AA0wQ0Rd__lFQOyA7__16ClipboardHistory06FilterwQ0VQo__AA7ForEachVySayA18_0Z4ItemV0Z4TypeOGA27_A21_GtGGAA14_PaddingLayoutVGGtGGA33_GACyxq_GAas2aSRzAA0gM0R_rlWl', symObjAddr: 0x15424, symBinAddr: 0x1000166E4, symSize: 0x84 }
  - { offset: 0xE954C, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV09itemsListD033_29BA33022BC77D68CF322D73212E107BLLQrvg7SwiftUI012_ConditionalC0VyAF0D0PAFE5frame8minWidth05idealR003maxR00Q6Height0sU00tU09alignmentQr12CoreGraphics7CGFloatVSg_A5vF9AlignmentVtFQOyAF6VStackVyAF05TupleD0VyAjFE15foregroundColoryQrAF5ColorVSgFQOyAjFE4fontyQrAF4FontVSgFQOyAF5ImageV_Qo__Qo__AF4TextVA14_SgtGG_Qo_AjFE9listStyleyQrqd__AF0F5StyleRd__lFQOyAF0F0Vys5NeverOAF7ForEachVySayAA0A4ItemVG10Foundation4UUIDVAA0A7ItemRowVGG_AF05PlainF5StyleVQo_GyXEfU_TA', symObjAddr: 0x15658, symBinAddr: 0x100016918, symSize: 0x8 }
  - { offset: 0xE9560, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI19_ConditionalContentVyAA08ModifiedD0VyAA6VStackVyAA9TupleViewVyAEyAEyAA5ImageVAA30_EnvironmentKeyWritingModifierVyAA4FontVSgGGAMyAA5ColorVSgGG_AA4TextVAYSgtGGAA16_FlexFrameLayoutVGAA0H0PAAE9listStyleyQrqd__AA04ListU0Rd__lFQOyAA0V0Vys5NeverOAA7ForEachVySay16ClipboardHistory0Z4ItemVG10Foundation4UUIDVA14_0Z7ItemRowVGG_AA05PlainvU0VQo_GACyxq_GAAA4_A2AA4_RzAAA4_R_rlWl', symObjAddr: 0x15660, symBinAddr: 0x100016920, symSize: 0xE4 }
  - { offset: 0xE9574, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyAA6VStackVyAA9TupleViewVyACyACyAA5ImageVAA30_EnvironmentKeyWritingModifierVyAA4FontVSgGGAKyAA5ColorVSgGG_AA4TextVAWSgtGGAA16_FlexFrameLayoutVGACyxq_GAA0G0A2AA3_RzAA0gL0R_rlWl', symObjAddr: 0x15744, symBinAddr: 0x100016A04, symSize: 0x84 }
  - { offset: 0xE9588, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI6VStackVyAA9TupleViewVyAA15ModifiedContentVyAGyAA5ImageVAA30_EnvironmentKeyWritingModifierVyAA4FontVSgGGAKyAA5ColorVSgGG_AA4TextVAWSgtGGACyxGAA0E0AAWl', symObjAddr: 0x157C8, symBinAddr: 0x100016A88, symSize: 0x6C }
  - { offset: 0xE959C, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI4ListVys5NeverOAA7ForEachVySay16ClipboardHistory0G4ItemVG10Foundation4UUIDVAH0gI3RowVGGACyxq_GAA4ViewAAWl', symObjAddr: 0x15834, symBinAddr: 0x100016AF4, symSize: 0x6C }
  - { offset: 0xE95B0, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV06footerD033_29BA33022BC77D68CF322D73212E107BLLQrvg7SwiftUI05TupleD0VyAF4TextV_AF6SpacerVAF0D0PAFE15foregroundColoryQrAF0R0VSgFQOyAnFE11buttonStyleyQrqd__AF015PrimitiveButtonT0Rd__lFQOyAF0V0VyAJG_AF05PlainvT0VQo__Qo_SgtGyXEfU_TA', symObjAddr: 0x158A0, symBinAddr: 0x100016B60, symSize: 0x8 }
  - { offset: 0xE95C4, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI9TupleViewVyAA4TextV_AA6SpacerVAA15ModifiedContentVyAA0D0PAAE11buttonStyleyQrqd__AA015PrimitiveButtonJ0Rd__lFQOyAA0L0VyAEG_AA05PlainlJ0VQo_AA30_EnvironmentKeyWritingModifierVyAA5ColorVSgGGSgtGACyxGAajAWl', symObjAddr: 0x158A8, symBinAddr: 0x100016B68, symSize: 0x6C }
  - { offset: 0xE95D8, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI6HStackVyAA9TupleViewVyAA4TextV_AA6SpacerVAA15ModifiedContentVyAA0E0PAAE11buttonStyleyQrqd__AA015PrimitiveButtonK0Rd__lFQOyAA0M0VyAGG_AA05PlainmK0VQo_AA30_EnvironmentKeyWritingModifierVyAA5ColorVSgGGSgtGGACyxGAalAWl', symObjAddr: 0x15914, symBinAddr: 0x100016BD4, symSize: 0x6C }
  - { offset: 0xE95EC, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyAA6HStackVyAA9TupleViewVyAA4TextV_AA6SpacerVACyAA0G0PAAE11buttonStyleyQrqd__AA015PrimitiveButtonK0Rd__lFQOyAA0M0VyAIG_AA05PlainmK0VQo_AA30_EnvironmentKeyWritingModifierVyAA5ColorVSgGGSgtGGAA14_PaddingLayoutVGACyxq_GAal2aLRzAA0gR0R_rlWl', symObjAddr: 0x15A98, symBinAddr: 0x100016D58, symSize: 0x84 }
  - { offset: 0xE9600, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ItemRowVMa', symObjAddr: 0x15C34, symBinAddr: 0x100016EF4, symSize: 0x60 }
  - { offset: 0xE9614, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewVWOh', symObjAddr: 0x15C94, symBinAddr: 0x100016F54, symSize: 0x5C }
  - { offset: 0xE9628, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI5StateVySbGWOh', symObjAddr: 0x15CF0, symBinAddr: 0x100016FB0, symSize: 0x28 }
  - { offset: 0xE963C, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ItemRowV4bodyQrvg7SwiftUI9TupleViewVyAE0I0PAEE5frame5width6height9alignmentQr12CoreGraphics7CGFloatVSg_AqE9AlignmentVtFQOyAiEE15foregroundColoryQrAE0S0VSgFQOyAiEE4fontyQrAE4FontVSgFQOyAE5ImageV_Qo__Qo__Qo__AE6VStackVyAGyAiEE14truncationModeyQrAE4TextV010TruncationY0OFQOyAiEE9lineLimityQrSiSgFQOyA9__Qo__Qo__A9_tGGAE6SpacerVAE6HStackVyAGyAiEE4helpyQrAE18LocalizedStringKeyVFQOyAiEE11buttonStyleyQrqd__AE20PrimitiveButtonStyleRd__lFQOyAE6ButtonVyA1_G_AE16PlainButtonStyleVQo__Qo__AiEEA22_yQrA24_FQOyAiEEATyQrAWFQOyA32__Qo__Qo_tGGSgtGyXEfU_TA', symObjAddr: 0x15D18, symBinAddr: 0x100016FD8, symSize: 0x8 }
  - { offset: 0xE9650, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI9TupleViewVyAA15ModifiedContentVyAEyAEyAA5ImageVAA30_EnvironmentKeyWritingModifierVyAA4FontVSgGGAIyAA5ColorVSgGGAA12_FrameLayoutVG_AA6VStackVyACyAEyAEyAA4TextVAIySiSgGGAIyAZ14TruncationModeOGG_AZtGGAA6SpacerVAA6HStackVyACyAA0D0PAAE4helpyQrAA015LocalizedStringI0VFQOyA13_AAE11buttonStyleyQrqd__AA015PrimitiveButtonZ0Rd__lFQOyAA6ButtonVyAGG_AA011PlainButtonZ0VQo__Qo__A13_AAEA14_yQrA16_FQOyAEyA24_ARG_Qo_tGGSgtGACyxGAAA12_AAWl', symObjAddr: 0x15D20, symBinAddr: 0x100016FE0, symSize: 0x6C }
  - { offset: 0xE9664, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI6HStackVyAA9TupleViewVyAA15ModifiedContentVyAGyAGyAA5ImageVAA30_EnvironmentKeyWritingModifierVyAA4FontVSgGGAKyAA5ColorVSgGGAA12_FrameLayoutVG_AA6VStackVyAEyAGyAGyAA4TextVAKySiSgGGAKyA0_14TruncationModeOGG_A0_tGGAA6SpacerVACyAEyAA0E0PAAE4helpyQrAA015LocalizedStringJ0VFQOyA13_AAE11buttonStyleyQrqd__AA015PrimitiveButtonZ0Rd__lFQOyAA6ButtonVyAIG_AA011PlainButtonZ0VQo__Qo__A13_AAEA14_yQrA16_FQOyAGyA24_ATG_Qo_tGGSgtGGACyxGAAA12_AAWl', symObjAddr: 0x15D8C, symBinAddr: 0x10001704C, symSize: 0x6C }
  - { offset: 0xE9678, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyAA6HStackVyAA9TupleViewVyACyACyACyAA5ImageVAA30_EnvironmentKeyWritingModifierVyAA4FontVSgGGAKyAA5ColorVSgGGAA12_FrameLayoutVG_AA6VStackVyAGyACyACyAA4TextVAKySiSgGGAKyA0_14TruncationModeOGG_A0_tGGAA6SpacerVAEyAGyAA0G0PAAE4helpyQrAA015LocalizedStringJ0VFQOyA13_AAE11buttonStyleyQrqd__AA015PrimitiveButtonZ0Rd__lFQOyAA6ButtonVyAIG_AA011PlainButtonZ0VQo__Qo__A13_AAEA14_yQrA16_FQOyACyA24_ATG_Qo_tGGSgtGGAA08_PaddingP0VGACyxq_GAAA12_A2AA12_RzAA0gL0R_rlWl', symObjAddr: 0x16048, symBinAddr: 0x100017308, symSize: 0x84 }
  - { offset: 0xE968C, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI9RectangleVAcA5ShapeAAWl', symObjAddr: 0x160CC, symBinAddr: 0x10001738C, symSize: 0x64 }
  - { offset: 0xE96A0, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ItemRowVWOc', symObjAddr: 0x16380, symBinAddr: 0x100017640, symSize: 0x174 }
  - { offset: 0xE96B4, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ItemRowVWOb', symObjAddr: 0x16604, symBinAddr: 0x1000178C4, symSize: 0x104 }
  - { offset: 0xE96C8, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ItemRowV4bodyQrvgySbcfU0_TA', symObjAddr: 0x16708, symBinAddr: 0x1000179C8, symSize: 0x48 }
  - { offset: 0xE96DC, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyACyAA6HStackVyAA9TupleViewVyACyACyACyAA5ImageVAA30_EnvironmentKeyWritingModifierVyAA4FontVSgGGAKyAA5ColorVSgGGAA12_FrameLayoutVG_AA6VStackVyAGyACyACyAA4TextVAKySiSgGGAKyA0_14TruncationModeOGG_A0_tGGAA6SpacerVAEyAGyAA0G0PAAE4helpyQrAA015LocalizedStringJ0VFQOyA13_AAE11buttonStyleyQrqd__AA015PrimitiveButtonZ0Rd__lFQOyAA6ButtonVyAIG_AA011PlainButtonZ0VQo__Qo__A13_AAEA14_yQrA16_FQOyACyA24_ATG_Qo_tGGSgtGGAA08_PaddingP0VGAA01_d5ShapeL0VyAA9RectangleVGGACyxq_GAAA12_A2AA12_RzAA0gL0R_rlWl', symObjAddr: 0x16750, symBinAddr: 0x100017A10, symSize: 0x88 }
  - { offset: 0xE96F0, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI21_ContentShapeModifierVyAA9RectangleVGACyxGAA04ViewE0AAWl', symObjAddr: 0x167D8, symBinAddr: 0x100017A98, symSize: 0x6C }
  - { offset: 0xE9704, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ItemRowV4bodyQrvgyycfU1_TA', symObjAddr: 0x16BA4, symBinAddr: 0x100017E64, symSize: 0x30 }
  - { offset: 0xE9718, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyACyACyAA6HStackVyAA9TupleViewVyACyACyACyAA5ImageVAA30_EnvironmentKeyWritingModifierVyAA4FontVSgGGAKyAA5ColorVSgGGAA12_FrameLayoutVG_AA6VStackVyAGyACyACyAA4TextVAKySiSgGGAKyA0_14TruncationModeOGG_A0_tGGAA6SpacerVAEyAGyAA0G0PAAE4helpyQrAA015LocalizedStringJ0VFQOyA13_AAE11buttonStyleyQrqd__AA015PrimitiveButtonZ0Rd__lFQOyAA6ButtonVyAIG_AA011PlainButtonZ0VQo__Qo__A13_AAEA14_yQrA16_FQOyACyA24_ATG_Qo_tGGSgtGGAA08_PaddingP0VGAA01_d5ShapeL0VyAA9RectangleVGGAA012_HoverRegionL0VGACyxq_GAAA12_A2AA12_RzAA0gL0R_rlWl', symObjAddr: 0x16BD4, symBinAddr: 0x100017E94, symSize: 0x88 }
  - { offset: 0xE972C, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI20_HoverRegionModifierVAcA04ViewE0AAWl', symObjAddr: 0x16C5C, symBinAddr: 0x100017F1C, symSize: 0x64 }
  - { offset: 0xE9740, size: 0x8, addend: 0x0, symName: '_$sSo27NSRelativeDateTimeFormatterCMa', symObjAddr: 0x16F30, symBinAddr: 0x1000181F0, symSize: 0x60 }
  - { offset: 0xE9754, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI24ButtonStyleConfigurationV5LabelVAeA4ViewAAWl', symObjAddr: 0x16F90, symBinAddr: 0x100018250, symSize: 0x6C }
  - { offset: 0xE9768, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyAA24ButtonStyleConfigurationV5LabelVAA14_PaddingLayoutVGACyxq_GAA4ViewA2aLRzAA0K8ModifierR_rlWl', symObjAddr: 0x16FFC, symBinAddr: 0x1000182BC, symSize: 0x84 }
  - { offset: 0xE977C, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyAA24ButtonStyleConfigurationV5LabelVAA14_PaddingLayoutVGWOh', symObjAddr: 0x17080, symBinAddr: 0x100018340, symSize: 0x3C }
  - { offset: 0xE9790, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI16RoundedRectangleVAcA5ShapeAAWl', symObjAddr: 0x170BC, symBinAddr: 0x10001837C, symSize: 0x6C }
  - { offset: 0xE97A4, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI16RoundedRectangleVWOh', symObjAddr: 0x17128, symBinAddr: 0x1000183E8, symSize: 0x5C }
  - { offset: 0xE97B8, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyACyAA24ButtonStyleConfigurationV5LabelVAA14_PaddingLayoutVGAIGACyxq_GAA4ViewA2aMRzAA0K8ModifierR_rlWl', symObjAddr: 0x17184, symBinAddr: 0x100018444, symSize: 0x84 }
  - { offset: 0xE97CC, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI10_ShapeViewVyAA16RoundedRectangleVAA5ColorVGACyxq_GAA0D0AAWl', symObjAddr: 0x17208, symBinAddr: 0x1000184C8, symSize: 0x6C }
  - { offset: 0xE97E0, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI10_ShapeViewVyAA16RoundedRectangleVAA5ColorVGWOh', symObjAddr: 0x17274, symBinAddr: 0x100018534, symSize: 0x7C }
  - { offset: 0xE97F4, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyACyAA24ButtonStyleConfigurationV5LabelVAA14_PaddingLayoutVGAIGWOh', symObjAddr: 0x172F0, symBinAddr: 0x1000185B0, symSize: 0x3C }
  - { offset: 0xE9808, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyACyACyAA24ButtonStyleConfigurationV5LabelVAA14_PaddingLayoutVGAIGAA19_BackgroundModifierVyAA10_ShapeViewVyAA16RoundedRectangleVAA5ColorVGGGACyxq_GAA0N0A2aXRzAA0nL0R_rlWl', symObjAddr: 0x1732C, symBinAddr: 0x1000185EC, symSize: 0x88 }
  - { offset: 0xE981C, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI19_BackgroundModifierVyAA10_ShapeViewVyAA16RoundedRectangleVAA5ColorVGGACyxGAA0fD0AAWl', symObjAddr: 0x173B4, symBinAddr: 0x100018674, symSize: 0x6C }
  - { offset: 0xE9830, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyACyACyAA24ButtonStyleConfigurationV5LabelVAA14_PaddingLayoutVGAIGAA19_BackgroundModifierVyAA10_ShapeViewVyAA16RoundedRectangleVAA5ColorVGGGWOh', symObjAddr: 0x17420, symBinAddr: 0x1000186E0, symSize: 0xB8 }
  - { offset: 0xE9844, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyACyACyACyAA24ButtonStyleConfigurationV5LabelVAA14_PaddingLayoutVGAIGAA19_BackgroundModifierVyAA10_ShapeViewVyAA16RoundedRectangleVAA5ColorVGGGAA022_EnvironmentKeyWritingL0VyASSgGGACyxq_GAA0N0A2AA1_RzAA0nL0R_rlWl', symObjAddr: 0x174D8, symBinAddr: 0x100018798, symSize: 0x88 }
  - { offset: 0xE9858, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI30_EnvironmentKeyWritingModifierVyAA5ColorVSgGACyxGAA04ViewF0AAWl', symObjAddr: 0x17560, symBinAddr: 0x100018820, symSize: 0x6C }
  - { offset: 0xE986C, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyACyACyACyAA24ButtonStyleConfigurationV5LabelVAA14_PaddingLayoutVGAIGAA19_BackgroundModifierVyAA10_ShapeViewVyAA16RoundedRectangleVAA5ColorVGGGAA022_EnvironmentKeyWritingL0VyASSgGGWOh', symObjAddr: 0x175CC, symBinAddr: 0x10001888C, symSize: 0xEC }
  - { offset: 0xE9880, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyACyACyACyACyAA24ButtonStyleConfigurationV5LabelVAA14_PaddingLayoutVGAIGAA19_BackgroundModifierVyAA10_ShapeViewVyAA16RoundedRectangleVAA5ColorVGGGAA022_EnvironmentKeyWritingL0VyASSgGGAA12_ScaleEffectVGACyxq_GAA0N0A2AA4_RzAA0nL0R_rlWl', symObjAddr: 0x176B8, symBinAddr: 0x100018978, symSize: 0x84 }
  - { offset: 0xE9894, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyACyACyACyACyAA24ButtonStyleConfigurationV5LabelVAA14_PaddingLayoutVGAIGAA19_BackgroundModifierVyAA10_ShapeViewVyAA16RoundedRectangleVAA5ColorVGGGAA022_EnvironmentKeyWritingL0VyASSgGGAA12_ScaleEffectVGWOh', symObjAddr: 0x1773C, symBinAddr: 0x1000189FC, symSize: 0xEC }
  - { offset: 0xE98A8, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyACyACyACyACyACyAA24ButtonStyleConfigurationV5LabelVAA14_PaddingLayoutVGAIGAA19_BackgroundModifierVyAA10_ShapeViewVyAA16RoundedRectangleVAA5ColorVGGGAA022_EnvironmentKeyWritingL0VyASSgGGAA12_ScaleEffectVGAA010_AnimationL0VySbGGACyxq_GAA0N0A2AA8_RzAA0nL0R_rlWl', symObjAddr: 0x17828, symBinAddr: 0x100018AE8, symSize: 0x88 }
  - { offset: 0xE98BC, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI18_AnimationModifierVySbGACyxGAA04ViewD0AAWl', symObjAddr: 0x178B0, symBinAddr: 0x100018B70, symSize: 0x6C }
  - { offset: 0xE98D0, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyACyACyACyACyACyAA24ButtonStyleConfigurationV5LabelVAA14_PaddingLayoutVGAIGAA19_BackgroundModifierVyAA10_ShapeViewVyAA16RoundedRectangleVAA5ColorVGGGAA022_EnvironmentKeyWritingL0VyASSgGGAA12_ScaleEffectVGAA010_AnimationL0VySbGGWOh', symObjAddr: 0x1791C, symBinAddr: 0x100018BDC, symSize: 0x10C }
  - { offset: 0xE98E4, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyACyACyACyACyACyAA24ButtonStyleConfigurationV5LabelVAA14_PaddingLayoutVGAIGAA19_BackgroundModifierVyAA10_ShapeViewVyAA16RoundedRectangleVAA5ColorVGGGAA022_EnvironmentKeyWritingL0VyASSgGGAA12_ScaleEffectVGAA010_AnimationL0VySbGGWOc', symObjAddr: 0x17A28, symBinAddr: 0x100018CE8, symSize: 0x2A4 }
  - { offset: 0xE98F8, size: 0x8, addend: 0x0, symName: '_$sSo7NSImageCMa', symObjAddr: 0x17CCC, symBinAddr: 0x100018F8C, symSize: 0x60 }
  - { offset: 0xE990C, size: 0x8, addend: 0x0, symName: '_$sSo9NSPopoverCMa', symObjAddr: 0x17D2C, symBinAddr: 0x100018FEC, symSize: 0x60 }
  - { offset: 0xE9920, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15formatSpecifierySSxmlF', symObjAddr: 0x17D8C, symBinAddr: 0x10001904C, symSize: 0x2A4 }
  - { offset: 0xE9946, size: 0x8, addend: 0x0, symName: '_$s12CoreGraphics7CGFloatVyACxcSzRzlufCSi_Tt0gq5', symObjAddr: 0x18030, symBinAddr: 0x1000192F0, symSize: 0x8 }
  - { offset: 0xE995E, size: 0x8, addend: 0x0, symName: ___swift_project_boxed_opaque_existential_0, symObjAddr: 0x18038, symBinAddr: 0x1000192F8, symSize: 0x4C }
  - { offset: 0xE9972, size: 0x8, addend: 0x0, symName: ___swift_destroy_boxed_opaque_existential_0, symObjAddr: 0x18084, symBinAddr: 0x100019344, symSize: 0x60 }
  - { offset: 0xE9986, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11AppDelegateCMa', symObjAddr: 0x180E4, symBinAddr: 0x1000193A4, symSize: 0x24 }
  - { offset: 0xE999A, size: 0x8, addend: 0x0, symName: '_$sSo12NSStatusItemCSgWOh', symObjAddr: 0x18108, symBinAddr: 0x1000193C8, symSize: 0x30 }
  - { offset: 0xE99AE, size: 0x8, addend: 0x0, symName: '_$sSo9NSPopoverCSgWOh', symObjAddr: 0x18138, symBinAddr: 0x1000193F8, symSize: 0x30 }
  - { offset: 0xE99C2, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI8SettingsVyAA9EmptyViewVGACyxGAA5SceneAAWl', symObjAddr: 0x18168, symBinAddr: 0x100019428, symSize: 0x6C }
  - { offset: 0xE99D6, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0aB3AppVMa', symObjAddr: 0x181D4, symBinAddr: 0x100019494, symSize: 0x60 }
  - { offset: 0xE99EA, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0aB3AppVAC7SwiftUI0C0AAWl', symObjAddr: 0x18234, symBinAddr: 0x1000194F4, symSize: 0x6C }
  - { offset: 0xE99FE, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0aB3AppVWOb', symObjAddr: 0x182A0, symBinAddr: 0x100019560, symSize: 0x48 }
  - { offset: 0xE9A12, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV0A4TypeOSHAASQWb', symObjAddr: 0x182E8, symBinAddr: 0x1000195A8, symSize: 0x14 }
  - { offset: 0xE9A26, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV0A4TypeOAESQAAWl', symObjAddr: 0x182FC, symBinAddr: 0x1000195BC, symSize: 0x68 }
  - { offset: 0xE9A3A, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV0A4TypeOs12CaseIterableAA8AllCasessAFP_SlWT', symObjAddr: 0x18364, symBinAddr: 0x100019624, symSize: 0x14 }
  - { offset: 0xE9A4E, size: 0x8, addend: 0x0, symName: '_$sSay16ClipboardHistory0A4ItemV0A4TypeOGSayxGSlsWl', symObjAddr: 0x18378, symBinAddr: 0x100019638, symSize: 0x6C }
  - { offset: 0xE9A62, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemVs12IdentifiableAA2IDsADP_SHWT', symObjAddr: 0x183E4, symBinAddr: 0x1000196A4, symSize: 0x14 }
  - { offset: 0xE9A76, size: 0x8, addend: 0x0, symName: '_$s10Foundation4UUIDVACSHAAWl', symObjAddr: 0x183F8, symBinAddr: 0x1000196B8, symSize: 0x6C }
  - { offset: 0xE9A8A, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC7Combine16ObservableObjectAA0F19WillChangePublisherAdEP_AD0I0PWT', symObjAddr: 0x18464, symBinAddr: 0x100019724, symSize: 0xC }
  - { offset: 0xE9A9E, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV7SwiftUI0D0AA4BodyAdEP_AGWT', symObjAddr: 0x18470, symBinAddr: 0x100019730, symSize: 0x24 }
  - { offset: 0xE9AB2, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ItemRowV7SwiftUI4ViewAA4BodyAdEP_AGWT', symObjAddr: 0x18494, symBinAddr: 0x100019754, symSize: 0x24 }
  - { offset: 0xE9AC6, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory17FilterButtonStyleV7SwiftUI0dE0AA4BodyAdEP_AD4ViewPWT', symObjAddr: 0x184B8, symBinAddr: 0x100019778, symSize: 0x24 }
  - { offset: 0xE9ADA, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0aB3AppV7SwiftUI0C0AA4BodyAdEP_AD5ScenePWT', symObjAddr: 0x184DC, symBinAddr: 0x10001979C, symSize: 0x24 }
  - { offset: 0xE9AEE, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemVwCP', symObjAddr: 0x18500, symBinAddr: 0x1000197C0, symSize: 0x150 }
  - { offset: 0xE9B02, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemVwxx', symObjAddr: 0x18650, symBinAddr: 0x100019910, symSize: 0x88 }
  - { offset: 0xE9B16, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemVwcp', symObjAddr: 0x186D8, symBinAddr: 0x100019998, symSize: 0xF0 }
  - { offset: 0xE9B2A, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemVwca', symObjAddr: 0x187C8, symBinAddr: 0x100019A88, symSize: 0xF8 }
  - { offset: 0xE9B3E, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemVwtk', symObjAddr: 0x188C0, symBinAddr: 0x100019B80, symSize: 0xCC }
  - { offset: 0xE9B52, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemVwta', symObjAddr: 0x1898C, symBinAddr: 0x100019C4C, symSize: 0xE4 }
  - { offset: 0xE9B66, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemVwet', symObjAddr: 0x18A70, symBinAddr: 0x100019D30, symSize: 0x1C }
  - { offset: 0xE9B7A, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemVwst', symObjAddr: 0x18BB4, symBinAddr: 0x100019E74, symSize: 0x1C }
  - { offset: 0xE9B8E, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemVMr', symObjAddr: 0x18CD0, symBinAddr: 0x100019F90, symSize: 0xE4 }
  - { offset: 0xE9BA2, size: 0x8, addend: 0x0, symName: ___swift_memcpy1_1, symObjAddr: 0x18DB4, symBinAddr: 0x10001A074, symSize: 0xC }
  - { offset: 0xE9BB6, size: 0x8, addend: 0x0, symName: ___swift_noop_void_return, symObjAddr: 0x18DC0, symBinAddr: 0x10001A080, symSize: 0x4 }
  - { offset: 0xE9BCA, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV0A4TypeOwet', symObjAddr: 0x18DC4, symBinAddr: 0x10001A084, symSize: 0x168 }
  - { offset: 0xE9BDE, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV0A4TypeOwst', symObjAddr: 0x18F2C, symBinAddr: 0x10001A1EC, symSize: 0x1FC }
  - { offset: 0xE9BF2, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV0A4TypeOwug', symObjAddr: 0x19128, symBinAddr: 0x10001A3E8, symSize: 0x8 }
  - { offset: 0xE9C06, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV0A4TypeOwup', symObjAddr: 0x19130, symBinAddr: 0x10001A3F0, symSize: 0x4 }
  - { offset: 0xE9C1A, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV0A4TypeOwui', symObjAddr: 0x19134, symBinAddr: 0x10001A3F4, symSize: 0x8 }
  - { offset: 0xE9C2E, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV0A4TypeOMa', symObjAddr: 0x1913C, symBinAddr: 0x10001A3FC, symSize: 0x14 }
  - { offset: 0xE9C42, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV10CodingKeys33_29BA33022BC77D68CF322D73212E107BLLOwet', symObjAddr: 0x19150, symBinAddr: 0x10001A410, symSize: 0x168 }
  - { offset: 0xE9C56, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV10CodingKeys33_29BA33022BC77D68CF322D73212E107BLLOwst', symObjAddr: 0x192B8, symBinAddr: 0x10001A578, symSize: 0x1FC }
  - { offset: 0xE9C6A, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV10CodingKeys33_29BA33022BC77D68CF322D73212E107BLLOwug', symObjAddr: 0x194B4, symBinAddr: 0x10001A774, symSize: 0x8 }
  - { offset: 0xE9C7E, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV10CodingKeys33_29BA33022BC77D68CF322D73212E107BLLOwup', symObjAddr: 0x194BC, symBinAddr: 0x10001A77C, symSize: 0x4 }
  - { offset: 0xE9C92, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV10CodingKeys33_29BA33022BC77D68CF322D73212E107BLLOwui', symObjAddr: 0x194C0, symBinAddr: 0x10001A780, symSize: 0x8 }
  - { offset: 0xE9CA6, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV10CodingKeys33_29BA33022BC77D68CF322D73212E107BLLOMa', symObjAddr: 0x194C8, symBinAddr: 0x10001A788, symSize: 0x14 }
  - { offset: 0xE9CBA, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerCMU', symObjAddr: 0x194DC, symBinAddr: 0x10001A79C, symSize: 0x18 }
  - { offset: 0xE9CCE, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerCMr', symObjAddr: 0x194F4, symBinAddr: 0x10001A7B4, symSize: 0x124 }
  - { offset: 0xE9CE2, size: 0x8, addend: 0x0, symName: '_$s7Combine9PublishedVySay16ClipboardHistory0C4ItemVGGMa', symObjAddr: 0x19618, symBinAddr: 0x10001A8D8, symSize: 0x98 }
  - { offset: 0xE9CF6, size: 0x8, addend: 0x0, symName: '_$s7Combine9PublishedVySbGMa', symObjAddr: 0x196B0, symBinAddr: 0x10001A970, symSize: 0x90 }
  - { offset: 0xE9D0A, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewVwCP', symObjAddr: 0x19740, symBinAddr: 0x10001AA00, symSize: 0x3C }
  - { offset: 0xE9D1E, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewVwxx', symObjAddr: 0x1977C, symBinAddr: 0x10001AA3C, symSize: 0x58 }
  - { offset: 0xE9D32, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewVwcp', symObjAddr: 0x197D4, symBinAddr: 0x10001AA94, symSize: 0xCC }
  - { offset: 0xE9D46, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewVwca', symObjAddr: 0x198A0, symBinAddr: 0x10001AB60, symSize: 0x118 }
  - { offset: 0xE9D5A, size: 0x8, addend: 0x0, symName: ___swift_memcpy64_8, symObjAddr: 0x199B8, symBinAddr: 0x10001AC78, symSize: 0x2C }
  - { offset: 0xE9D6E, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewVwta', symObjAddr: 0x199E4, symBinAddr: 0x10001ACA4, symSize: 0xB4 }
  - { offset: 0xE9D82, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewVwet', symObjAddr: 0x19A98, symBinAddr: 0x10001AD58, symSize: 0x140 }
  - { offset: 0xE9D96, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewVwst', symObjAddr: 0x19BD8, symBinAddr: 0x10001AE98, symSize: 0x1E0 }
  - { offset: 0xE9DAA, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewVMa', symObjAddr: 0x19DB8, symBinAddr: 0x10001B078, symSize: 0x14 }
  - { offset: 0xE9DBE, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ItemRowVwCP', symObjAddr: 0x19DCC, symBinAddr: 0x10001B08C, symSize: 0x1C4 }
  - { offset: 0xE9DD2, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ItemRowVwxx', symObjAddr: 0x19F90, symBinAddr: 0x10001B250, symSize: 0xC0 }
  - { offset: 0xE9DE6, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ItemRowVwcp', symObjAddr: 0x1A050, symBinAddr: 0x10001B310, symSize: 0x16C }
  - { offset: 0xE9DFA, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ItemRowVwca', symObjAddr: 0x1A1BC, symBinAddr: 0x10001B47C, symSize: 0x194 }
  - { offset: 0xE9E0E, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ItemRowVwtk', symObjAddr: 0x1A350, symBinAddr: 0x10001B610, symSize: 0x100 }
  - { offset: 0xE9E22, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ItemRowVwta', symObjAddr: 0x1A450, symBinAddr: 0x10001B710, symSize: 0x14C }
  - { offset: 0xE9E36, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ItemRowVwet', symObjAddr: 0x1A59C, symBinAddr: 0x10001B85C, symSize: 0x1C }
  - { offset: 0xE9E4A, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ItemRowVwst', symObjAddr: 0x1A688, symBinAddr: 0x10001B948, symSize: 0x1C }
  - { offset: 0xE9E5E, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ItemRowVMr', symObjAddr: 0x1A74C, symBinAddr: 0x10001BA0C, symSize: 0xB0 }
  - { offset: 0xE9E72, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory17FilterButtonStyleVwet', symObjAddr: 0x1A7FC, symBinAddr: 0x10001BABC, symSize: 0x198 }
  - { offset: 0xE9E86, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory17FilterButtonStyleVwst', symObjAddr: 0x1A994, symBinAddr: 0x10001BC54, symSize: 0x218 }
  - { offset: 0xE9E9A, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory17FilterButtonStyleVMa', symObjAddr: 0x1ABAC, symBinAddr: 0x10001BE6C, symSize: 0x14 }
  - { offset: 0xE9EAE, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0aB3AppVwCP', symObjAddr: 0x1ABC0, symBinAddr: 0x10001BE80, symSize: 0x44 }
  - { offset: 0xE9EC2, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0aB3AppVwxx', symObjAddr: 0x1AC04, symBinAddr: 0x10001BEC4, symSize: 0x3C }
  - { offset: 0xE9ED6, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0aB3AppVwcp', symObjAddr: 0x1AC40, symBinAddr: 0x10001BF00, symSize: 0x48 }
  - { offset: 0xE9EEA, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0aB3AppVwca', symObjAddr: 0x1AC88, symBinAddr: 0x10001BF48, symSize: 0x48 }
  - { offset: 0xE9EFE, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0aB3AppVwtk', symObjAddr: 0x1ACD0, symBinAddr: 0x10001BF90, symSize: 0x48 }
  - { offset: 0xE9F12, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0aB3AppVwta', symObjAddr: 0x1AD18, symBinAddr: 0x10001BFD8, symSize: 0x48 }
  - { offset: 0xE9F26, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0aB3AppVwet', symObjAddr: 0x1AD60, symBinAddr: 0x10001C020, symSize: 0x1C }
  - { offset: 0xE9F3A, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0aB3AppVwst', symObjAddr: 0x1ADC0, symBinAddr: 0x10001C080, symSize: 0x1C }
  - { offset: 0xE9F4E, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0aB3AppVMr', symObjAddr: 0x1AE24, symBinAddr: 0x10001C0E4, symSize: 0x94 }
  - { offset: 0xE9F62, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI28NSApplicationDelegateAdaptorVy16ClipboardHistory03AppD0CGMa', symObjAddr: 0x1AEB8, symBinAddr: 0x10001C178, symSize: 0x98 }
  - { offset: 0xE9F76, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyACyAA6HStackVyAA9TupleViewVyACyACyAA5ImageVAA30_EnvironmentKeyWritingModifierVyAA4FontVSgGGAKyAA5ColorVSgGG_AA4TextVAA6SpacerVAA0G0PAAE4helpyQrAA015LocalizedStringJ0VFQOyA_AAE11buttonStyleyQrqd__AA015PrimitiveButtonU0Rd__lFQOyAA0W0VyAUG_AA05PlainwU0VQo__Qo_tGGAA14_PaddingLayoutVGAA011_BackgrounduL0VyARGGACyxq_GAaz2aZRzAA0gL0R_rlWl', symObjAddr: 0x1AF78, symBinAddr: 0x10001C238, symSize: 0x88 }
  - { offset: 0xE9F8A, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyACyAA6VStackVyAA9TupleViewVyAA6HStackVyAGyACyAA5ImageVAA30_EnvironmentKeyWritingModifierVyAA5ColorVSgGG_AA0G0PAAE14textFieldStyleyQrqd__AA04TextpQ0Rd__lFQOyAA0rP0VyAA0R0VG_AA013RoundedBorderrpQ0VQo_ACyAtAE06buttonQ0yQrqd__AA015PrimitiveButtonQ0Rd__lFQOyAA0W0VyAZG_AA05PlainwQ0VQo_AQGSgtGG_AA06ScrollG0VyACyAIyAGyAtAEA3_yQrqd__AA0wQ0Rd__lFQOyA7__16ClipboardHistory06FilterwQ0VQo__AA7ForEachVySayA18_0Z4ItemV0Z4TypeOGA27_A21_GtGGAA14_PaddingLayoutVGGtGGA33_GA33_GACyxq_GAas2aSRzAA0gM0R_rlWl', symObjAddr: 0x1B014, symBinAddr: 0x10001C2D4, symSize: 0x84 }
  - { offset: 0xE9F9E, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI5GroupVyAA19_ConditionalContentVyAA08ModifiedE0VyAA6VStackVyAA9TupleViewVyAGyAGyAA5ImageVAA30_EnvironmentKeyWritingModifierVyAA4FontVSgGGAOyAA5ColorVSgGG_AA4TextVA_SgtGGAA16_FlexFrameLayoutVGAA0I0PAAE9listStyleyQrqd__AA04ListV0Rd__lFQOyAA0W0Vys5NeverOAA7ForEachVySay16ClipboardHistory13ClipboardItemVG10Foundation4UUIDVA16_16ClipboardItemRowVGG_AA05PlainwV0VQo_GGACyxGAAA6_A2AA6_RzlWl', symObjAddr: 0x1B0AC, symBinAddr: 0x10001C36C, symSize: 0x78 }
  - { offset: 0xE9FB2, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyACyAA6HStackVyAA9TupleViewVyAA4TextV_AA6SpacerVACyAA0G0PAAE11buttonStyleyQrqd__AA015PrimitiveButtonK0Rd__lFQOyAA0M0VyAIG_AA05PlainmK0VQo_AA30_EnvironmentKeyWritingModifierVyAA5ColorVSgGGSgtGGAA14_PaddingLayoutVGAA011_BackgroundkR0VyAYGGACyxq_GAal2aLRzAA0gR0R_rlWl', symObjAddr: 0x1B138, symBinAddr: 0x10001C3F8, symSize: 0x88 }
  - { offset: 0xE9FC6, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV10CodingKeys33_29BA33022BC77D68CF322D73212E107BLLOs0D3KeyAAs28CustomDebugStringConvertiblePWb', symObjAddr: 0x1B23C, symBinAddr: 0x10001C4FC, symSize: 0x14 }
  - { offset: 0xE9FDA, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV10CodingKeys33_29BA33022BC77D68CF322D73212E107BLLOAFs28CustomDebugStringConvertibleAAWl', symObjAddr: 0x1B250, symBinAddr: 0x10001C510, symSize: 0x68 }
  - { offset: 0xE9FEE, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV10CodingKeys33_29BA33022BC77D68CF322D73212E107BLLOs0D3KeyAAs23CustomStringConvertiblePWb', symObjAddr: 0x1B2B8, symBinAddr: 0x10001C578, symSize: 0x14 }
  - { offset: 0xEA002, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV10CodingKeys33_29BA33022BC77D68CF322D73212E107BLLOAFs23CustomStringConvertibleAAWl', symObjAddr: 0x1B2CC, symBinAddr: 0x10001C58C, symSize: 0x68 }
  - { offset: 0xEA016, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV10CodingKeys33_29BA33022BC77D68CF322D73212E107BLLOSHAASQWb', symObjAddr: 0x1B334, symBinAddr: 0x10001C5F4, symSize: 0x14 }
  - { offset: 0xEA02A, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV10CodingKeys33_29BA33022BC77D68CF322D73212E107BLLOAFSQAAWl', symObjAddr: 0x1B348, symBinAddr: 0x10001C608, symSize: 0x68 }
  - { offset: 0xEA03E, size: 0x8, addend: 0x0, symName: '_$sSo9NSPopoverCSgWOc', symObjAddr: 0x1B3B0, symBinAddr: 0x10001C670, symSize: 0x3C }
  - { offset: 0xEA052, size: 0x8, addend: 0x0, symName: '_$sSo12NSStatusItemCSgWOc', symObjAddr: 0x1B3EC, symBinAddr: 0x10001C6AC, symSize: 0x3C }
  - { offset: 0xEA066, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV0A4TypeOAESYAAWl', symObjAddr: 0x1B428, symBinAddr: 0x10001C6E8, symSize: 0x68 }
  - { offset: 0xEA07A, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI5ImageVWOh', symObjAddr: 0x1B490, symBinAddr: 0x10001C750, symSize: 0x28 }
  - { offset: 0xEA08E, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyAA5ImageVAA30_EnvironmentKeyWritingModifierVyAA4FontVSgGGACyxq_GAA4ViewA2aNRzAA0kI0R_rlWl', symObjAddr: 0x1B4B8, symBinAddr: 0x10001C778, symSize: 0x84 }
  - { offset: 0xEA0A2, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI30_EnvironmentKeyWritingModifierVyAA4FontVSgGACyxGAA04ViewF0AAWl', symObjAddr: 0x1B53C, symBinAddr: 0x10001C7FC, symSize: 0x6C }
  - { offset: 0xEA0B6, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyAA5ImageVAA30_EnvironmentKeyWritingModifierVyAA4FontVSgGGWOh', symObjAddr: 0x1B5A8, symBinAddr: 0x10001C868, symSize: 0x40 }
  - { offset: 0xEA0CA, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyACyAA5ImageVAA30_EnvironmentKeyWritingModifierVyAA4FontVSgGGAGyAA5ColorVSgGGACyxq_GAA4ViewA2aSRzAA0lI0R_rlWl', symObjAddr: 0x1B5E8, symBinAddr: 0x10001C8A8, symSize: 0x88 }
  - { offset: 0xEA0DE, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyACyAA5ImageVAA30_EnvironmentKeyWritingModifierVyAA4FontVSgGGAGyAA5ColorVSgGGWOh', symObjAddr: 0x1B670, symBinAddr: 0x10001C930, symSize: 0x58 }
  - { offset: 0xEA0F2, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyACyACyAA5ImageVAA30_EnvironmentKeyWritingModifierVyAA4FontVSgGGAGyAA5ColorVSgGGAA12_FrameLayoutVGACyxq_GAA4ViewA2aVRzAA0nI0R_rlWl', symObjAddr: 0x1B6C8, symBinAddr: 0x10001C988, symSize: 0x84 }
  - { offset: 0xEA106, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyACyACyAA5ImageVAA30_EnvironmentKeyWritingModifierVyAA4FontVSgGGAGyAA5ColorVSgGGAA12_FrameLayoutVGWOh', symObjAddr: 0x1B74C, symBinAddr: 0x10001CA0C, symSize: 0x58 }
  - { offset: 0xEA11A, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ItemRowV4bodyQrvg7SwiftUI9TupleViewVyAE0I0PAEE5frame5width6height9alignmentQr12CoreGraphics7CGFloatVSg_AqE9AlignmentVtFQOyAiEE15foregroundColoryQrAE0S0VSgFQOyAiEE4fontyQrAE4FontVSgFQOyAE5ImageV_Qo__Qo__Qo__AE6VStackVyAGyAiEE14truncationModeyQrAE4TextV010TruncationY0OFQOyAiEE9lineLimityQrSiSgFQOyA9__Qo__Qo__A9_tGGAE6SpacerVAE6HStackVyAGyAiEE4helpyQrAE18LocalizedStringKeyVFQOyAiEE11buttonStyleyQrqd__AE20PrimitiveButtonStyleRd__lFQOyAE6ButtonVyA1_G_AE16PlainButtonStyleVQo__Qo__AiEEA22_yQrA24_FQOyAiEEATyQrAWFQOyA32__Qo__Qo_tGGSgtGyXEfU_A16_yXEfU_TA', symObjAddr: 0x1B7A4, symBinAddr: 0x10001CA64, symSize: 0x8 }
  - { offset: 0xEA12E, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI9TupleViewVyAA15ModifiedContentVyAEyAA4TextVAA30_EnvironmentKeyWritingModifierVySiSgGGAIyAG14TruncationModeOGG_AGtGACyxGAA0D0AAWl', symObjAddr: 0x1B7AC, symBinAddr: 0x10001CA6C, symSize: 0x6C }
  - { offset: 0xEA142, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI6VStackVyAA9TupleViewVyAA15ModifiedContentVyAGyAA4TextVAA30_EnvironmentKeyWritingModifierVySiSgGGAKyAI14TruncationModeOGG_AItGGACyxGAA0E0AAWl', symObjAddr: 0x1B818, symBinAddr: 0x10001CAD8, symSize: 0x6C }
  - { offset: 0xEA156, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI6VStackVyAA9TupleViewVyAA15ModifiedContentVyAGyAA4TextVAA30_EnvironmentKeyWritingModifierVySiSgGGAKyAI14TruncationModeOGG_AItGGWOh', symObjAddr: 0x1B884, symBinAddr: 0x10001CB44, symSize: 0x104 }
  - { offset: 0xEA16A, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI6HStackVyAA9TupleViewVyAA0E0PAAE4helpyQrAA18LocalizedStringKeyVFQOyAgAE11buttonStyleyQrqd__AA015PrimitiveButtonK0Rd__lFQOyAA0M0VyAA5ImageVG_AA05PlainmK0VQo__Qo__AgAEAHyQrAJFQOyAA15ModifiedContentVyAtA012_EnvironmentI15WritingModifierVyAA5ColorVSgGG_Qo_tGGACyxGAafAWl', symObjAddr: 0x1B988, symBinAddr: 0x10001CC48, symSize: 0x6C }
  - { offset: 0xEA17E, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyACyACyAA5ImageVAA30_EnvironmentKeyWritingModifierVyAA4FontVSgGGAGyAA5ColorVSgGGAA12_FrameLayoutVGWOc', symObjAddr: 0x1BC14, symBinAddr: 0x10001CED4, symSize: 0xC4 }
  - { offset: 0xEA192, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI6VStackVyAA9TupleViewVyAA15ModifiedContentVyAGyAA4TextVAA30_EnvironmentKeyWritingModifierVySiSgGGAKyAI14TruncationModeOGG_AItGGWOc', symObjAddr: 0x1BCD8, symBinAddr: 0x10001CF98, symSize: 0x228 }
  - { offset: 0xEA1A6, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI6HStackVyAA9TupleViewVyAA0E0PAAE4helpyQrAA18LocalizedStringKeyVFQOyAgAE11buttonStyleyQrqd__AA015PrimitiveButtonK0Rd__lFQOyAA0M0VyAA5ImageVG_AA05PlainmK0VQo__Qo__AgAEAHyQrAJFQOyAA15ModifiedContentVyAtA012_EnvironmentI15WritingModifierVyAA5ColorVSgGG_Qo_tGGSgxSgAaf2aFRzlWl', symObjAddr: 0x1C058, symBinAddr: 0x10001D318, symSize: 0x78 }
  - { offset: 0xEA1BA, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ItemRowV4bodyQrvg7SwiftUI9TupleViewVyAE0I0PAEE5frame5width6height9alignmentQr12CoreGraphics7CGFloatVSg_AqE9AlignmentVtFQOyAiEE15foregroundColoryQrAE0S0VSgFQOyAiEE4fontyQrAE4FontVSgFQOyAE5ImageV_Qo__Qo__Qo__AE6VStackVyAGyAiEE14truncationModeyQrAE4TextV010TruncationY0OFQOyAiEE9lineLimityQrSiSgFQOyA9__Qo__Qo__A9_tGGAE6SpacerVAE6HStackVyAGyAiEE4helpyQrAE18LocalizedStringKeyVFQOyAiEE11buttonStyleyQrqd__AE20PrimitiveButtonStyleRd__lFQOyAE6ButtonVyA1_G_AE16PlainButtonStyleVQo__Qo__AiEEA22_yQrA24_FQOyAiEEATyQrAWFQOyA32__Qo__Qo_tGGSgtGyXEfU_A36_yXEfU0_TA', symObjAddr: 0x1C0D0, symBinAddr: 0x10001D390, symSize: 0x8 }
  - { offset: 0xEA1CE, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI9TupleViewVyAA0D0PAAE4helpyQrAA18LocalizedStringKeyVFQOyAeAE11buttonStyleyQrqd__AA015PrimitiveButtonJ0Rd__lFQOyAA0L0VyAA5ImageVG_AA05PlainlJ0VQo__Qo__AeAEAFyQrAHFQOyAA15ModifiedContentVyArA012_EnvironmentH15WritingModifierVyAA5ColorVSgGG_Qo_tGACyxGAadAWl', symObjAddr: 0x1C0D8, symBinAddr: 0x10001D398, symSize: 0x6C }
  - { offset: 0xEA1E2, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ItemRowV4bodyQrvg7SwiftUI9TupleViewVyAE0I0PAEE5frame5width6height9alignmentQr12CoreGraphics7CGFloatVSg_AqE9AlignmentVtFQOyAiEE15foregroundColoryQrAE0S0VSgFQOyAiEE4fontyQrAE4FontVSgFQOyAE5ImageV_Qo__Qo__Qo__AE6VStackVyAGyAiEE14truncationModeyQrAE4TextV010TruncationY0OFQOyAiEE9lineLimityQrSiSgFQOyA9__Qo__Qo__A9_tGGAE6SpacerVAE6HStackVyAGyAiEE4helpyQrAE18LocalizedStringKeyVFQOyAiEE11buttonStyleyQrqd__AE20PrimitiveButtonStyleRd__lFQOyAE6ButtonVyA1_G_AE16PlainButtonStyleVQo__Qo__AiEEA22_yQrA24_FQOyAiEEATyQrAWFQOyA32__Qo__Qo_tGGSgtGyXEfU_A36_yXEfU0_yyScMYccfU_TA', symObjAddr: 0x1C3CC, symBinAddr: 0x10001D68C, symSize: 0x30 }
  - { offset: 0xEA1F6, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI6ButtonVyAA5ImageVGACyxGAA4ViewAAWl', symObjAddr: 0x1C3FC, symBinAddr: 0x10001D6BC, symSize: 0x6C }
  - { offset: 0xEA20A, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI16PlainButtonStyleVAcA09PrimitivedE0AAWl', symObjAddr: 0x1C468, symBinAddr: 0x10001D728, symSize: 0x6C }
  - { offset: 0xEA21E, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ItemRowV4bodyQrvg7SwiftUI9TupleViewVyAE0I0PAEE5frame5width6height9alignmentQr12CoreGraphics7CGFloatVSg_AqE9AlignmentVtFQOyAiEE15foregroundColoryQrAE0S0VSgFQOyAiEE4fontyQrAE4FontVSgFQOyAE5ImageV_Qo__Qo__Qo__AE6VStackVyAGyAiEE14truncationModeyQrAE4TextV010TruncationY0OFQOyAiEE9lineLimityQrSiSgFQOyA9__Qo__Qo__A9_tGGAE6SpacerVAE6HStackVyAGyAiEE4helpyQrAE18LocalizedStringKeyVFQOyAiEE11buttonStyleyQrqd__AE20PrimitiveButtonStyleRd__lFQOyAE6ButtonVyA1_G_AE16PlainButtonStyleVQo__Qo__AiEEA22_yQrA24_FQOyAiEEATyQrAWFQOyA32__Qo__Qo_tGGSgtGyXEfU_A36_yXEfU0_yyScMYccfU1_TA', symObjAddr: 0x1C5E4, symBinAddr: 0x10001D8A4, symSize: 0x30 }
  - { offset: 0xEA232, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyAA4ViewPAAE11buttonStyleyQrqd__AA015PrimitiveButtonG0Rd__lFQOyAA0I0VyAA5ImageVG_AA05PlainiG0VQo_AA30_EnvironmentKeyWritingModifierVyAA5ColorVSgGGACyxq_GAad2aDRzAA0eO0R_rlWl', symObjAddr: 0x1C614, symBinAddr: 0x10001D8D4, symSize: 0xE8 }
  - { offset: 0xEA246, size: 0x8, addend: 0x0, symName: '_$sxSg7SwiftUI4ViewRzlWOc', symObjAddr: 0x1C770, symBinAddr: 0x10001DA30, symSize: 0xA4 }
  - { offset: 0xEA25A, size: 0x8, addend: 0x0, symName: '_$sS2SSysWl', symObjAddr: 0x1C814, symBinAddr: 0x10001DAD4, symSize: 0x64 }
  - { offset: 0xEA26E, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI4TextVWOh', symObjAddr: 0x1C878, symBinAddr: 0x10001DB38, symSize: 0x44 }
  - { offset: 0xEA282, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyAA4TextVAA30_EnvironmentKeyWritingModifierVySiSgGGACyxq_GAA4ViewA2aLRzAA0jI0R_rlWl', symObjAddr: 0x1C8BC, symBinAddr: 0x10001DB7C, symSize: 0x84 }
  - { offset: 0xEA296, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI30_EnvironmentKeyWritingModifierVySiSgGACyxGAA04ViewF0AAWl', symObjAddr: 0x1C940, symBinAddr: 0x10001DC00, symSize: 0x6C }
  - { offset: 0xEA2AA, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyAA4TextVAA30_EnvironmentKeyWritingModifierVySiSgGGWOh', symObjAddr: 0x1C9AC, symBinAddr: 0x10001DC6C, symSize: 0x50 }
  - { offset: 0xEA2BE, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyACyAA4TextVAA30_EnvironmentKeyWritingModifierVySiSgGGAGyAE14TruncationModeOGGACyxq_GAA4ViewA2aPRzAA0lI0R_rlWl', symObjAddr: 0x1C9FC, symBinAddr: 0x10001DCBC, symSize: 0x88 }
  - { offset: 0xEA2D2, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI30_EnvironmentKeyWritingModifierVyAA4TextV14TruncationModeOGACyxGAA04ViewF0AAWl', symObjAddr: 0x1CA84, symBinAddr: 0x10001DD44, symSize: 0x6C }
  - { offset: 0xEA2E6, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyACyAA4TextVAA30_EnvironmentKeyWritingModifierVySiSgGGAGyAE14TruncationModeOGGWOh', symObjAddr: 0x1CAF0, symBinAddr: 0x10001DDB0, symSize: 0xB0 }
  - { offset: 0xEA2FA, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyACyAA4TextVAA30_EnvironmentKeyWritingModifierVySiSgGGAGyAE14TruncationModeOGGWOc', symObjAddr: 0x1CBA0, symBinAddr: 0x10001DE60, symSize: 0x150 }
  - { offset: 0xEA30E, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI4TextVWOc', symObjAddr: 0x1CCF0, symBinAddr: 0x10001DFB0, symSize: 0x84 }
  - { offset: 0xEA322, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyAA4ViewPAAE11buttonStyleyQrqd__AA015PrimitiveButtonG0Rd__lFQOyAA0I0VyAA4TextVG_AA05PlainiG0VQo_AA30_EnvironmentKeyWritingModifierVyAA5ColorVSgGGACyxq_GAad2aDRzAA0eO0R_rlWl', symObjAddr: 0x1CD74, symBinAddr: 0x10001E034, symSize: 0xE8 }
  - { offset: 0xEA336, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI6ButtonVyAA4TextVGACyxGAA4ViewAAWl', symObjAddr: 0x1CE5C, symBinAddr: 0x10001E11C, symSize: 0x6C }
  - { offset: 0xEA34A, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyAA4ViewPAAE11buttonStyleyQrqd__AA015PrimitiveButtonG0Rd__lFQOyAA0I0VyAA4TextVG_AA05PlainiG0VQo_AA30_EnvironmentKeyWritingModifierVyAA5ColorVSgGGSgxSgAad2aDRzlWl', symObjAddr: 0x1D158, symBinAddr: 0x10001E418, symSize: 0x78 }
  - { offset: 0xEA35E, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV06footerD033_29BA33022BC77D68CF322D73212E107BLLQrvg7SwiftUI05TupleD0VyAF4TextV_AF6SpacerVAF0D0PAFE15foregroundColoryQrAF0R0VSgFQOyAnFE11buttonStyleyQrqd__AF015PrimitiveButtonT0Rd__lFQOyAF0V0VyAJG_AF05PlainvT0VQo__Qo_SgtGyXEfU_yyScMYccfU_TA', symObjAddr: 0x1D230, symBinAddr: 0x10001E4F0, symSize: 0x8 }
  - { offset: 0xEA372, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV09itemsListD033_29BA33022BC77D68CF322D73212E107BLLQrvg7SwiftUI012_ConditionalC0VyAF0D0PAFE5frame8minWidth05idealR003maxR00Q6Height0sU00tU09alignmentQr12CoreGraphics7CGFloatVSg_A5vF9AlignmentVtFQOyAF6VStackVyAF05TupleD0VyAjFE15foregroundColoryQrAF5ColorVSgFQOyAjFE4fontyQrAF4FontVSgFQOyAF5ImageV_Qo__Qo__AF4TextVA14_SgtGG_Qo_AjFE9listStyleyQrqd__AF0F5StyleRd__lFQOyAF0F0Vys5NeverOAF7ForEachVySayAA0A4ItemVG10Foundation4UUIDVAA0A7ItemRowVGG_AF05PlainF5StyleVQo_GyXEfU_A34_A28_cfU0_TA', symObjAddr: 0x1D3BC, symBinAddr: 0x10001E67C, symSize: 0x8 }
  - { offset: 0xEA386, size: 0x8, addend: 0x0, symName: '_$sSay16ClipboardHistory0A4ItemVGSayxGSksWl', symObjAddr: 0x1D3C4, symBinAddr: 0x10001E684, symSize: 0x6C }
  - { offset: 0xEA39A, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ItemRowVAC7SwiftUI4ViewAAWl', symObjAddr: 0x1D430, symBinAddr: 0x10001E6F0, symSize: 0x6C }
  - { offset: 0xEA3AE, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemVACs12IdentifiableAAWl', symObjAddr: 0x1D49C, symBinAddr: 0x10001E75C, symSize: 0x6C }
  - { offset: 0xEA3C2, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV09itemsListD033_29BA33022BC77D68CF322D73212E107BLLQrvg7SwiftUI012_ConditionalC0VyAF0D0PAFE5frame8minWidth05idealR003maxR00Q6Height0sU00tU09alignmentQr12CoreGraphics7CGFloatVSg_A5vF9AlignmentVtFQOyAF6VStackVyAF05TupleD0VyAjFE15foregroundColoryQrAF5ColorVSgFQOyAjFE4fontyQrAF4FontVSgFQOyAF5ImageV_Qo__Qo__AF4TextVA14_SgtGG_Qo_AjFE9listStyleyQrqd__AF0F5StyleRd__lFQOyAF0F0Vys5NeverOAF7ForEachVySayAA0A4ItemVG10Foundation4UUIDVAA0A7ItemRowVGG_AF05PlainF5StyleVQo_GyXEfU_A16_yXEfU_TA', symObjAddr: 0x1D8F8, symBinAddr: 0x10001EBB8, symSize: 0x8 }
  - { offset: 0xEA3D6, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI9TupleViewVyAA15ModifiedContentVyAEyAA5ImageVAA30_EnvironmentKeyWritingModifierVyAA4FontVSgGGAIyAA5ColorVSgGG_AA4TextVAUSgtGACyxGAA0D0AAWl', symObjAddr: 0x1D900, symBinAddr: 0x10001EBC0, symSize: 0x6C }
  - { offset: 0xEA3EA, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI6VStackVyAA9TupleViewVyAA15ModifiedContentVyAGyAA5ImageVAA30_EnvironmentKeyWritingModifierVyAA4FontVSgGGAKyAA5ColorVSgGG_AA4TextVAWSgtGGWOh', symObjAddr: 0x1D96C, symBinAddr: 0x10001EC2C, symSize: 0xB4 }
  - { offset: 0xEA3FE, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyAA6VStackVyAA9TupleViewVyACyACyAA5ImageVAA30_EnvironmentKeyWritingModifierVyAA4FontVSgGGAKyAA5ColorVSgGG_AA4TextVAWSgtGGAA16_FlexFrameLayoutVGWOh', symObjAddr: 0x1DA20, symBinAddr: 0x10001ECE0, symSize: 0xB4 }
  - { offset: 0xEA412, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyAA6VStackVyAA9TupleViewVyACyACyAA5ImageVAA30_EnvironmentKeyWritingModifierVyAA4FontVSgGGAKyAA5ColorVSgGG_AA4TextVAWSgtGGAA16_FlexFrameLayoutVGWOr', symObjAddr: 0x1DAD4, symBinAddr: 0x10001ED94, symSize: 0xDC }
  - { offset: 0xEA426, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI4TextVSgWOy', symObjAddr: 0x1DBB0, symBinAddr: 0x10001EE70, symSize: 0x58 }
  - { offset: 0xEA43A, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI4FontV6DesignOSgWOh', symObjAddr: 0x1DC08, symBinAddr: 0x10001EEC8, symSize: 0x68 }
  - { offset: 0xEA44E, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyACyAA5ImageVAA30_EnvironmentKeyWritingModifierVyAA4FontVSgGGAGyAA5ColorVSgGGWOc', symObjAddr: 0x1DC70, symBinAddr: 0x10001EF30, symSize: 0xA8 }
  - { offset: 0xEA462, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI4TextVSgWOc', symObjAddr: 0x1DD18, symBinAddr: 0x10001EFD8, symSize: 0xB4 }
  - { offset: 0xEA476, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI4TextVSgxSgAA4ViewA2aFRzlWl', symObjAddr: 0x1DDCC, symBinAddr: 0x10001F08C, symSize: 0x78 }
  - { offset: 0xEA48A, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI4TextVSgWOh', symObjAddr: 0x1DE44, symBinAddr: 0x10001F104, symSize: 0x54 }
  - { offset: 0xEA49E, size: 0x8, addend: 0x0, symName: '_$sSa12_endMutationyyF', symObjAddr: 0x1DE98, symBinAddr: 0x10001F158, symSize: 0xC }
  - { offset: 0xEA4B6, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI9TextFieldVA2A0C0VRszrlE_4textACyAEGAA18LocalizedStringKeyV_AA7BindingVySSGtcfcySbcfU_', symObjAddr: 0x1DEA4, symBinAddr: 0x10001F164, symSize: 0x4 }
  - { offset: 0xEA4CE, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI9TextFieldVA2A0C0VRszrlE_4textACyAEGAA18LocalizedStringKeyV_AA7BindingVySSGtcfcyycfU0_', symObjAddr: 0x1DEA8, symBinAddr: 0x10001F168, symSize: 0x4 }
  - { offset: 0xEA4E6, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI4ListVAAs5NeverORszrlE_10rowContentACyAeA7ForEachVyqd__7Element_2IDQYd__qd_0_GGqd___qd_0_AIQyd__ctcALRs_SkRd__AA4ViewRd_0_s12IdentifiableANRQr0_lufcALyXEfU_', symObjAddr: 0x1DEAC, symBinAddr: 0x10001F16C, symSize: 0x300 }
  - { offset: 0xEA519, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI4ListVAAs5NeverORszrlE_10rowContentACyAeA7ForEachVyqd__7Element_2IDQYd__qd_0_GGqd___qd_0_AIQyd__ctcALRs_SkRd__AA4ViewRd_0_s12IdentifiableANRQr0_lufcALyXEfU_TA', symObjAddr: 0x1E1AC, symBinAddr: 0x10001F46C, symSize: 0x34 }
  - { offset: 0xEA52D, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI4ListVAAs5NeverORszrlE_10rowContentACyAeA7ForEachVyqd__7Element_2IDQYd__qd_0_GGqd___qd_0_AIQyd__ctcALRs_SkRd__AA4ViewRd_0_s12IdentifiableANRQr0_lufcALyXEfU_qd_0_ANcfU_', symObjAddr: 0x1E1E0, symBinAddr: 0x10001F4A0, symSize: 0x104 }
  - { offset: 0xEA560, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI4ListVAAs5NeverORszrlE_10rowContentACyAeA7ForEachVyqd__7Element_2IDQYd__qd_0_GGqd___qd_0_AIQyd__ctcALRs_SkRd__AA4ViewRd_0_s12IdentifiableANRQr0_lufcALyXEfU_qd_0_ANcfU_TA', symObjAddr: 0x1E330, symBinAddr: 0x10001F5F0, symSize: 0x30 }
  - { offset: 0xEA574, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ItemRowVWOh', symObjAddr: 0x1E360, symBinAddr: 0x10001F620, symSize: 0xCC }
  - { offset: 0xEA588, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV015searchAndFilterD033_29BA33022BC77D68CF322D73212E107BLLQrvg7SwiftUI05TupleD0VyAF6HStackVyAHyAF0D0PAFE15foregroundColoryQrAF0S0VSgFQOyAF5ImageV_Qo__AlFE14textFieldStyleyQrqd__AF04TextvW0Rd__lFQOyAF0xV0VyAF0X0VG_AF013RoundedBorderxvW0VQo_AlFEAMyQrAPFQOyAlFE06buttonW0yQrqd__AF015PrimitiveButtonW0Rd__lFQOyAF6ButtonVyAYG_AF011PlainButtonW0VQo__Qo_SgtGG_AF06ScrollD0VyAlFE7paddingyQrAF4EdgeO3SetV_12CoreGraphics7CGFloatVSgtFQOyAJyAHyAlFEA2_yQrqd__AF06ButtonW0Rd__lFQOyA6__AA0g6ButtonW0VQo__AF7ForEachVySayAA0A4ItemV0A4TypeOGA34_A28_GtGG_Qo_GtGyXEfU_A12_yXEfU_TA', symObjAddr: 0x1E42C, symBinAddr: 0x10001F6EC, symSize: 0x8 }
  - { offset: 0xEA59C, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI9TupleViewVyAA15ModifiedContentVyAA5ImageVAA30_EnvironmentKeyWritingModifierVyAA5ColorVSgGG_AA0D0PAAE14textFieldStyleyQrqd__AA04TextnO0Rd__lFQOyAA0pN0VyAA0P0VG_AA013RoundedBorderpnO0VQo_AEyApAE06buttonO0yQrqd__AA015PrimitiveButtonO0Rd__lFQOyAA0U0VyAVG_AA05PlainuO0VQo_AMGSgtGACyxGAaoAWl', symObjAddr: 0x1E434, symBinAddr: 0x10001F6F4, symSize: 0x6C }
  - { offset: 0xEA5B0, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI6HStackVyAA9TupleViewVyAA15ModifiedContentVyAA5ImageVAA30_EnvironmentKeyWritingModifierVyAA5ColorVSgGG_AA0E0PAAE14textFieldStyleyQrqd__AA04TextoP0Rd__lFQOyAA0qO0VyAA0Q0VG_AA013RoundedBorderqoP0VQo_AGyArAE06buttonP0yQrqd__AA015PrimitiveButtonP0Rd__lFQOyAA0V0VyAXG_AA05PlainvP0VQo_AOGSgtGGACyxGAaqAWl', symObjAddr: 0x1E4A0, symBinAddr: 0x10001F760, symSize: 0x6C }
  - { offset: 0xEA5C4, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV015searchAndFilterD033_29BA33022BC77D68CF322D73212E107BLLQrvg7SwiftUI05TupleD0VyAF6HStackVyAHyAF0D0PAFE15foregroundColoryQrAF0S0VSgFQOyAF5ImageV_Qo__AlFE14textFieldStyleyQrqd__AF04TextvW0Rd__lFQOyAF0xV0VyAF0X0VG_AF013RoundedBorderxvW0VQo_AlFEAMyQrAPFQOyAlFE06buttonW0yQrqd__AF015PrimitiveButtonW0Rd__lFQOyAF6ButtonVyAYG_AF011PlainButtonW0VQo__Qo_SgtGG_AF06ScrollD0VyAlFE7paddingyQrAF4EdgeO3SetV_12CoreGraphics7CGFloatVSgtFQOyAJyAHyAlFEA2_yQrqd__AF06ButtonW0Rd__lFQOyA6__AA0g6ButtonW0VQo__AF7ForEachVySayAA0A4ItemV0A4TypeOGA34_A28_GtGG_Qo_GtGyXEfU_A39_yXEfU0_TA', symObjAddr: 0x1E654, symBinAddr: 0x10001F914, symSize: 0x8 }
  - { offset: 0xEA5D8, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyAA6HStackVyAA9TupleViewVyAA0G0PAAE11buttonStyleyQrqd__AA06ButtonI0Rd__lFQOyAA0J0VyAA4TextVG_16ClipboardHistory06FilterjI0VQo__AA7ForEachVySayAQ0L4ItemV0L4TypeOGAzTGtGGAA14_PaddingLayoutVGACyxq_GAah2aHRzAA0G8ModifierR_rlWl', symObjAddr: 0x1E65C, symBinAddr: 0x10001F91C, symSize: 0x84 }
  - { offset: 0xEA5EC, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI6HStackVyAA9TupleViewVyAA0E0PAAE11buttonStyleyQrqd__AA06ButtonG0Rd__lFQOyAA0H0VyAA4TextVG_16ClipboardHistory06FilterhG0VQo__AA7ForEachVySayAO0J4ItemV0J4TypeOGAxRGtGGACyxGAafAWl', symObjAddr: 0x1E6E0, symBinAddr: 0x10001F9A0, symSize: 0x6C }
  - { offset: 0xEA600, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI10ScrollViewVyAA15ModifiedContentVyAA6HStackVyAA05TupleD0VyAA0D0PAAE11buttonStyleyQrqd__AA06ButtonJ0Rd__lFQOyAA0K0VyAA4TextVG_16ClipboardHistory06FilterkJ0VQo__AA7ForEachVySayAS0M4ItemV0M4TypeOGA0_AVGtGGAA14_PaddingLayoutVGGACyxGAajAWl', symObjAddr: 0x1E74C, symBinAddr: 0x10001FA0C, symSize: 0x6C }
  - { offset: 0xEA614, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV015searchAndFilterD033_29BA33022BC77D68CF322D73212E107BLLQrvg7SwiftUI05TupleD0VyAF6HStackVyAHyAF0D0PAFE15foregroundColoryQrAF0S0VSgFQOyAF5ImageV_Qo__AlFE14textFieldStyleyQrqd__AF04TextvW0Rd__lFQOyAF0xV0VyAF0X0VG_AF013RoundedBorderxvW0VQo_AlFEAMyQrAPFQOyAlFE06buttonW0yQrqd__AF015PrimitiveButtonW0Rd__lFQOyAF6ButtonVyAYG_AF011PlainButtonW0VQo__Qo_SgtGG_AF06ScrollD0VyAlFE7paddingyQrAF4EdgeO3SetV_12CoreGraphics7CGFloatVSgtFQOyAJyAHyAlFEA2_yQrqd__AF06ButtonW0Rd__lFQOyA6__AA0g6ButtonW0VQo__AF7ForEachVySayAA0A4ItemV0A4TypeOGA34_A28_GtGG_Qo_GtGyXEfU_A39_yXEfU0_A37_yXEfU_TA', symObjAddr: 0x1EA20, symBinAddr: 0x10001FCE0, symSize: 0x8 }
  - { offset: 0xEA628, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI9TupleViewVyAA0D0PAAE11buttonStyleyQrqd__AA06ButtonF0Rd__lFQOyAA0G0VyAA4TextVG_16ClipboardHistory06FiltergF0VQo__AA7ForEachVySayAM0I4ItemV0I4TypeOGAvPGtGACyxGAadAWl', symObjAddr: 0x1EA28, symBinAddr: 0x10001FCE8, symSize: 0x6C }
  - { offset: 0xEA63C, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV015searchAndFilterD033_29BA33022BC77D68CF322D73212E107BLLQrvg7SwiftUI05TupleD0VyAF6HStackVyAHyAF0D0PAFE15foregroundColoryQrAF0S0VSgFQOyAF5ImageV_Qo__AlFE14textFieldStyleyQrqd__AF04TextvW0Rd__lFQOyAF0xV0VyAF0X0VG_AF013RoundedBorderxvW0VQo_AlFEAMyQrAPFQOyAlFE06buttonW0yQrqd__AF015PrimitiveButtonW0Rd__lFQOyAF6ButtonVyAYG_AF011PlainButtonW0VQo__Qo_SgtGG_AF06ScrollD0VyAlFE7paddingyQrAF4EdgeO3SetV_12CoreGraphics7CGFloatVSgtFQOyAJyAHyAlFEA2_yQrqd__AF06ButtonW0Rd__lFQOyA6__AA0g6ButtonW0VQo__AF7ForEachVySayAA0A4ItemV0A4TypeOGA34_A28_GtGG_Qo_GtGyXEfU_A39_yXEfU0_A37_yXEfU_yyScMYccfU_TA', symObjAddr: 0x1ED3C, symBinAddr: 0x10001FFFC, symSize: 0x8 }
  - { offset: 0xEA650, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory17FilterButtonStyleVAC7SwiftUI0dE0AAWl', symObjAddr: 0x1ED44, symBinAddr: 0x100020004, symSize: 0x68 }
  - { offset: 0xEA664, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV015searchAndFilterD033_29BA33022BC77D68CF322D73212E107BLLQrvg7SwiftUI05TupleD0VyAF6HStackVyAHyAF0D0PAFE15foregroundColoryQrAF0S0VSgFQOyAF5ImageV_Qo__AlFE14textFieldStyleyQrqd__AF04TextvW0Rd__lFQOyAF0xV0VyAF0X0VG_AF013RoundedBorderxvW0VQo_AlFEAMyQrAPFQOyAlFE06buttonW0yQrqd__AF015PrimitiveButtonW0Rd__lFQOyAF6ButtonVyAYG_AF011PlainButtonW0VQo__Qo_SgtGG_AF06ScrollD0VyAlFE7paddingyQrAF4EdgeO3SetV_12CoreGraphics7CGFloatVSgtFQOyAJyAHyAlFEA2_yQrqd__AF06ButtonW0Rd__lFQOyA6__AA0g6ButtonW0VQo__AF7ForEachVySayAA0A4ItemV0A4TypeOGA34_A28_GtGG_Qo_GtGyXEfU_A39_yXEfU0_A37_yXEfU_A28_A34_cfU0_TA', symObjAddr: 0x1EE0C, symBinAddr: 0x1000200CC, symSize: 0x8 }
  - { offset: 0xEA678, size: 0x8, addend: 0x0, symName: '_$sSay16ClipboardHistory0A4ItemV0A4TypeOGSayxGSksWl', symObjAddr: 0x1EE14, symBinAddr: 0x1000200D4, symSize: 0x6C }
  - { offset: 0xEA68C, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV0A4TypeOAESHAAWl', symObjAddr: 0x1EE80, symBinAddr: 0x100020140, symSize: 0x68 }
  - { offset: 0xEA6A0, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI7ForEachVySay16ClipboardHistory0E4ItemV0E4TypeOGAhA4ViewPAAE11buttonStyleyQrqd__AA06ButtonK0Rd__lFQOyAA0L0VyAA4TextVG_AD06FilterlK0VQo_GACyxq_q0_GAaj2aJR0_rlWl', symObjAddr: 0x1EEE8, symBinAddr: 0x1000201A8, symSize: 0xD0 }
  - { offset: 0xEA6B4, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV015searchAndFilterD033_29BA33022BC77D68CF322D73212E107BLLQrvg7SwiftUI05TupleD0VyAF6HStackVyAHyAF0D0PAFE15foregroundColoryQrAF0S0VSgFQOyAF5ImageV_Qo__AlFE14textFieldStyleyQrqd__AF04TextvW0Rd__lFQOyAF0xV0VyAF0X0VG_AF013RoundedBorderxvW0VQo_AlFEAMyQrAPFQOyAlFE06buttonW0yQrqd__AF015PrimitiveButtonW0Rd__lFQOyAF6ButtonVyAYG_AF011PlainButtonW0VQo__Qo_SgtGG_AF06ScrollD0VyAlFE7paddingyQrAF4EdgeO3SetV_12CoreGraphics7CGFloatVSgtFQOyAJyAHyAlFEA2_yQrqd__AF06ButtonW0Rd__lFQOyA6__AA0g6ButtonW0VQo__AF7ForEachVySayAA0A4ItemV0A4TypeOGA34_A28_GtGG_Qo_GtGyXEfU_A39_yXEfU0_A37_yXEfU_A28_A34_cfU0_yyScMYccfU_TA', symObjAddr: 0x1F018, symBinAddr: 0x1000202D8, symSize: 0xC }
  - { offset: 0xEA6C8, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV0A4TypeOSgWOc', symObjAddr: 0x1F024, symBinAddr: 0x1000202E4, symSize: 0x14 }
  - { offset: 0xEA6DC, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyAA5ImageVAA30_EnvironmentKeyWritingModifierVyAA5ColorVSgGGACyxq_GAA4ViewA2aNRzAA0kI0R_rlWl', symObjAddr: 0x1F038, symBinAddr: 0x1000202F8, symSize: 0x84 }
  - { offset: 0xEA6F0, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyAA5ImageVAA30_EnvironmentKeyWritingModifierVyAA5ColorVSgGGWOh', symObjAddr: 0x1F0BC, symBinAddr: 0x10002037C, symSize: 0x40 }
  - { offset: 0xEA704, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI9TextFieldVyAA0C0VGACyxGAA4ViewAAWl', symObjAddr: 0x1F0FC, symBinAddr: 0x1000203BC, symSize: 0x6C }
  - { offset: 0xEA718, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI27RoundedBorderTextFieldStyleVAcA0efG0AAWl', symObjAddr: 0x1F168, symBinAddr: 0x100020428, symSize: 0x6C }
  - { offset: 0xEA72C, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI15ModifiedContentVyAA5ImageVAA30_EnvironmentKeyWritingModifierVyAA5ColorVSgGGWOc', symObjAddr: 0x1F1D4, symBinAddr: 0x100020494, symSize: 0x70 }
  - { offset: 0xEA740, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV015searchAndFilterD033_29BA33022BC77D68CF322D73212E107BLLQrvg7SwiftUI05TupleD0VyAF6HStackVyAHyAF0D0PAFE15foregroundColoryQrAF0S0VSgFQOyAF5ImageV_Qo__AlFE14textFieldStyleyQrqd__AF04TextvW0Rd__lFQOyAF0xV0VyAF0X0VG_AF013RoundedBorderxvW0VQo_AlFEAMyQrAPFQOyAlFE06buttonW0yQrqd__AF015PrimitiveButtonW0Rd__lFQOyAF6ButtonVyAYG_AF011PlainButtonW0VQo__Qo_SgtGG_AF06ScrollD0VyAlFE7paddingyQrAF4EdgeO3SetV_12CoreGraphics7CGFloatVSgtFQOyAJyAHyAlFEA2_yQrqd__AF06ButtonW0Rd__lFQOyA6__AA0g6ButtonW0VQo__AF7ForEachVySayAA0A4ItemV0A4TypeOGA34_A28_GtGG_Qo_GtGyXEfU_A12_yXEfU_yyScMYccfU_TA', symObjAddr: 0x1F2A4, symBinAddr: 0x100020564, symSize: 0x8 }
  - { offset: 0xEA754, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV06headerD033_29BA33022BC77D68CF322D73212E107BLLQrvg7SwiftUI05TupleD0VyAF0D0PAFE15foregroundColoryQrAF0P0VSgFQOyAjFE4fontyQrAF4FontVSgFQOyAF5ImageV_Qo__Qo__AF4TextVAF6SpacerVAjFE4helpyQrAF18LocalizedStringKeyVFQOyAjFE11buttonStyleyQrqd__AF20PrimitiveButtonStyleRd__lFQOyAF6ButtonVyAVG_AF16PlainButtonStyleVQo__Qo_tGyXEfU_AVyXEfU_TA', symObjAddr: 0x1F2AC, symBinAddr: 0x10002056C, symSize: 0x8 }
  - { offset: 0xEA768, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI6ButtonVyAA15ModifiedContentVyAEyAA5ImageVAA30_EnvironmentKeyWritingModifierVyAA4FontVSgGGAIyAA5ColorVSgGGGACyxGAA4ViewAAWl', symObjAddr: 0x1F2B4, symBinAddr: 0x100020574, symSize: 0x6C }
  - { offset: 0xEA77C, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV06headerD033_29BA33022BC77D68CF322D73212E107BLLQrvg7SwiftUI05TupleD0VyAF0D0PAFE15foregroundColoryQrAF0P0VSgFQOyAjFE4fontyQrAF4FontVSgFQOyAF5ImageV_Qo__Qo__AF4TextVAF6SpacerVAjFE4helpyQrAF18LocalizedStringKeyVFQOyAjFE11buttonStyleyQrqd__AF20PrimitiveButtonStyleRd__lFQOyAF6ButtonVyAVG_AF16PlainButtonStyleVQo__Qo_tGyXEfU_yycAA0A7ManagerCcfu_yycfu0_TA', symObjAddr: 0x1F320, symBinAddr: 0x1000205E0, symSize: 0x8 }
  - { offset: 0xEA790, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerCSgWOh', symObjAddr: 0x20300, symBinAddr: 0x1000215C0, symSize: 0x28 }
  - { offset: 0xEA7A4, size: 0x8, addend: 0x0, symName: '_$sSay16ClipboardHistory0A4ItemVGWOc', symObjAddr: 0x20328, symBinAddr: 0x1000215E8, symSize: 0x34 }
  - { offset: 0xEA7F3, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV2id10Foundation4UUIDVvg', symObjAddr: 0x14, symBinAddr: 0x1000012D4, symSize: 0x40 }
  - { offset: 0xEA824, size: 0x8, addend: 0x0, symName: '_$ss27_finalizeUninitializedArrayySayxGABnlF', symObjAddr: 0x418, symBinAddr: 0x1000016D8, symSize: 0x40 }
  - { offset: 0xEA855, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV0A4TypeOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x5B8, symBinAddr: 0x100001878, symSize: 0x48 }
  - { offset: 0xEA871, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV0A4TypeOSHAASH9hashValueSivgTW', symObjAddr: 0x600, symBinAddr: 0x1000018C0, symSize: 0x3C }
  - { offset: 0xEA88D, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV0A4TypeOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x63C, symBinAddr: 0x1000018FC, symSize: 0x44 }
  - { offset: 0xEA8A9, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV0A4TypeOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x680, symBinAddr: 0x100001940, symSize: 0x44 }
  - { offset: 0xEA8DF, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV0A4TypeOSeAASe4fromxs7Decoder_p_tKcfCTW', symObjAddr: 0x728, symBinAddr: 0x1000019E8, symSize: 0x74 }
  - { offset: 0xEA902, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV0A4TypeOSEAASE6encode2toys7Encoder_p_tKFTW', symObjAddr: 0x79C, symBinAddr: 0x100001A5C, symSize: 0x6C }
  - { offset: 0xEA957, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV10CodingKeys33_29BA33022BC77D68CF322D73212E107BLLOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0xEF0, symBinAddr: 0x1000021B0, symSize: 0x14 }
  - { offset: 0xEA973, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV10CodingKeys33_29BA33022BC77D68CF322D73212E107BLLOs28CustomDebugStringConvertibleAAsAGP16debugDescriptionSSvgTW', symObjAddr: 0xF88, symBinAddr: 0x100002248, symSize: 0x2C }
  - { offset: 0xEA98F, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV10CodingKeys33_29BA33022BC77D68CF322D73212E107BLLOs23CustomStringConvertibleAAsAGP11descriptionSSvgTW', symObjAddr: 0x101C, symBinAddr: 0x1000022DC, symSize: 0x2C }
  - { offset: 0xEA9F0, size: 0x8, addend: 0x0, symName: '_$sSa9removeAll15keepingCapacityySb_tFfA_', symObjAddr: 0x54F4, symBinAddr: 0x1000067B4, symSize: 0x18 }
  - { offset: 0xEAAC5, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV7contentSSvg', symObjAddr: 0x54, symBinAddr: 0x100001314, symSize: 0x44 }
  - { offset: 0xEAAD9, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV9timestamp10Foundation4DateVvg', symObjAddr: 0xF8, symBinAddr: 0x1000013B8, symSize: 0x58 }
  - { offset: 0xEAAED, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV4typeAC0A4TypeOvg', symObjAddr: 0x150, symBinAddr: 0x100001410, symSize: 0x20 }
  - { offset: 0xEAB01, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV0A4TypeO4iconSSvg', symObjAddr: 0x170, symBinAddr: 0x100001430, symSize: 0x104 }
  - { offset: 0xEAB31, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV0A4TypeO8rawValueAESgSS_tcfC', symObjAddr: 0x274, symBinAddr: 0x100001534, symSize: 0x1A4 }
  - { offset: 0xEAB53, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV0A4TypeO8allCasesSayAEGvgZ', symObjAddr: 0x458, symBinAddr: 0x100001718, symSize: 0x5C }
  - { offset: 0xEAB73, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV0A4TypeO8rawValueSSvg', symObjAddr: 0x4B4, symBinAddr: 0x100001774, symSize: 0x104 }
  - { offset: 0xEAB9C, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV0A4TypeOSYAASY8rawValuexSg03RawF0Qz_tcfCTW', symObjAddr: 0x6C4, symBinAddr: 0x100001984, symSize: 0x34 }
  - { offset: 0xEABB0, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV0A4TypeOSYAASY8rawValue03RawF0QzvgTW', symObjAddr: 0x6F8, symBinAddr: 0x1000019B8, symSize: 0x30 }
  - { offset: 0xEABD0, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV10CodingKeys33_29BA33022BC77D68CF322D73212E107BLLO11stringValueAFSgSS_tcfC', symObjAddr: 0x808, symBinAddr: 0x100001AC8, symSize: 0x2C8 }
  - { offset: 0xEABF2, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV10CodingKeys33_29BA33022BC77D68CF322D73212E107BLLO8intValueAFSgSi_tcfC', symObjAddr: 0xAD0, symBinAddr: 0x100001D90, symSize: 0x18 }
  - { offset: 0xEAC14, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV10CodingKeys33_29BA33022BC77D68CF322D73212E107BLLO21__derived_enum_equalsySbAF_AFtFZ', symObjAddr: 0xAE8, symBinAddr: 0x100001DA8, symSize: 0x100 }
  - { offset: 0xEAC58, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV10CodingKeys33_29BA33022BC77D68CF322D73212E107BLLO4hash4intoys6HasherVz_tF', symObjAddr: 0xBE8, symBinAddr: 0x100001EA8, symSize: 0xC0 }
  - { offset: 0xEAC88, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV0A4TypeOs12CaseIterableAAsAFP8allCases03AllH0QzvgZTW', symObjAddr: 0xCA8, symBinAddr: 0x100001F68, symSize: 0x28 }
  - { offset: 0xEAC9C, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV10CodingKeys33_29BA33022BC77D68CF322D73212E107BLLO9hashValueSivg', symObjAddr: 0xCD0, symBinAddr: 0x100001F90, symSize: 0x48 }
  - { offset: 0xEACBE, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV10CodingKeys33_29BA33022BC77D68CF322D73212E107BLLO8intValueSiSgvg', symObjAddr: 0xD80, symBinAddr: 0x100002040, symSize: 0x1C }
  - { offset: 0xEACE0, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV10CodingKeys33_29BA33022BC77D68CF322D73212E107BLLO11stringValueSSvg', symObjAddr: 0xD9C, symBinAddr: 0x10000205C, symSize: 0x104 }
  - { offset: 0xEAD09, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV10CodingKeys33_29BA33022BC77D68CF322D73212E107BLLOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0xEA0, symBinAddr: 0x100002160, symSize: 0x20 }
  - { offset: 0xEAD1D, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV10CodingKeys33_29BA33022BC77D68CF322D73212E107BLLOSHAASH9hashValueSivgTW', symObjAddr: 0xEC0, symBinAddr: 0x100002180, symSize: 0x18 }
  - { offset: 0xEAD31, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV10CodingKeys33_29BA33022BC77D68CF322D73212E107BLLOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0xED8, symBinAddr: 0x100002198, symSize: 0x18 }
  - { offset: 0xEAD45, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV10CodingKeys33_29BA33022BC77D68CF322D73212E107BLLOs0D3KeyAAsAGP11stringValueSSvgTW', symObjAddr: 0xF04, symBinAddr: 0x1000021C4, symSize: 0x18 }
  - { offset: 0xEAD59, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV10CodingKeys33_29BA33022BC77D68CF322D73212E107BLLOs0D3KeyAAsAGP11stringValuexSgSS_tcfCTW', symObjAddr: 0xF1C, symBinAddr: 0x1000021DC, symSize: 0x28 }
  - { offset: 0xEAD6D, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV10CodingKeys33_29BA33022BC77D68CF322D73212E107BLLOs0D3KeyAAsAGP8intValueSiSgvgTW', symObjAddr: 0xF44, symBinAddr: 0x100002204, symSize: 0x1C }
  - { offset: 0xEAD81, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV10CodingKeys33_29BA33022BC77D68CF322D73212E107BLLOs0D3KeyAAsAGP8intValuexSgSi_tcfCTW', symObjAddr: 0xF60, symBinAddr: 0x100002220, symSize: 0x28 }
  - { offset: 0xEAD95, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV6encode2toys7Encoder_p_tKF', symObjAddr: 0x1048, symBinAddr: 0x100002308, symSize: 0x498 }
  - { offset: 0xEADCD, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV7content9timestamp4typeACSS_10Foundation4DateVAC0A4TypeOtcfC', symObjAddr: 0x16D4, symBinAddr: 0x100002994, symSize: 0xA4 }
  - { offset: 0xEADE1, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemV4fromACs7Decoder_p_tKcfC', symObjAddr: 0x1778, symBinAddr: 0x100002A38, symSize: 0x464 }
  - { offset: 0xEAE04, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemVs12IdentifiableAAsADP2id2IDQzvgTW', symObjAddr: 0x1EC4, symBinAddr: 0x100003184, symSize: 0x14 }
  - { offset: 0xEAE1F, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemVSeAASe4fromxs7Decoder_p_tKcfCTW', symObjAddr: 0x1ED8, symBinAddr: 0x100003198, symSize: 0x50 }
  - { offset: 0xEAE33, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A4ItemVSEAASE6encode2toys7Encoder_p_tKFTW', symObjAddr: 0x1F28, symBinAddr: 0x1000031E8, symSize: 0x44 }
  - { offset: 0xEAE9C, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC7Combine16ObservableObjectAadEP16objectWillChange0fhI9PublisherQzvgTW', symObjAddr: 0x6140, symBinAddr: 0x100007400, symSize: 0x38 }
  - { offset: 0xEAECE, size: 0x8, addend: 0x0, symName: '_$s7Combine9PublishedV12wrappedValueACyxGx_tcfC', symObjAddr: 0x1FD0, symBinAddr: 0x100003290, symSize: 0x9C }
  - { offset: 0xEAEF0, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC5itemsSayAA0A4ItemVGvg', symObjAddr: 0x2138, symBinAddr: 0x1000033F8, symSize: 0x98 }
  - { offset: 0xEAF14, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC5itemsSayAA0A4ItemVGvs', symObjAddr: 0x21D0, symBinAddr: 0x100003490, symSize: 0xA0 }
  - { offset: 0xEAF47, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC5itemsSayAA0A4ItemVGvM', symObjAddr: 0x2270, symBinAddr: 0x100003530, symSize: 0xBC }
  - { offset: 0xEAF6B, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC5itemsSayAA0A4ItemVGvM.resume.0', symObjAddr: 0x232C, symBinAddr: 0x1000035EC, symSize: 0xC0 }
  - { offset: 0xEAF8C, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC6$items7Combine9PublishedV9PublisherVySayAA0A4ItemVG_Gvg', symObjAddr: 0x2598, symBinAddr: 0x100003858, symSize: 0x70 }
  - { offset: 0xEAFB0, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC6$items7Combine9PublishedV9PublisherVySayAA0A4ItemVG_Gvs', symObjAddr: 0x2608, symBinAddr: 0x1000038C8, symSize: 0xF0 }
  - { offset: 0xEAFE4, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC6$items7Combine9PublishedV9PublisherVySayAA0A4ItemVG_GvM', symObjAddr: 0x26F8, symBinAddr: 0x1000039B8, symSize: 0xB8 }
  - { offset: 0xEB008, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC6$items7Combine9PublishedV9PublisherVySayAA0A4ItemVG_GvM.resume.0', symObjAddr: 0x27B0, symBinAddr: 0x100003A70, symSize: 0xDC }
  - { offset: 0xEB029, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC6_items33_29BA33022BC77D68CF322D73212E107BLL7Combine9PublishedVySayAA0A4ItemVGGvg', symObjAddr: 0x288C, symBinAddr: 0x100003B4C, symSize: 0x70 }
  - { offset: 0xEB03D, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC6_items33_29BA33022BC77D68CF322D73212E107BLL7Combine9PublishedVySayAA0A4ItemVGGvs', symObjAddr: 0x28FC, symBinAddr: 0x100003BBC, symSize: 0xD4 }
  - { offset: 0xEB051, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC12isMonitoringSbvg', symObjAddr: 0x2AD4, symBinAddr: 0x100003D94, symSize: 0x9C }
  - { offset: 0xEB075, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC12isMonitoringSbvs', symObjAddr: 0x2B70, symBinAddr: 0x100003E30, symSize: 0x90 }
  - { offset: 0xEB0A8, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC12isMonitoringSbvM', symObjAddr: 0x2C00, symBinAddr: 0x100003EC0, symSize: 0xBC }
  - { offset: 0xEB0CC, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC12isMonitoringSbvM.resume.0', symObjAddr: 0x2CBC, symBinAddr: 0x100003F7C, symSize: 0xC0 }
  - { offset: 0xEB0ED, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC13$isMonitoring7Combine9PublishedV9PublisherVySb_Gvg', symObjAddr: 0x2F04, symBinAddr: 0x1000041C4, symSize: 0x70 }
  - { offset: 0xEB111, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC13$isMonitoring7Combine9PublishedV9PublisherVySb_Gvs', symObjAddr: 0x2F74, symBinAddr: 0x100004234, symSize: 0xF0 }
  - { offset: 0xEB145, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC13$isMonitoring7Combine9PublishedV9PublisherVySb_GvM', symObjAddr: 0x3064, symBinAddr: 0x100004324, symSize: 0xB8 }
  - { offset: 0xEB169, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC13$isMonitoring7Combine9PublishedV9PublisherVySb_GvM.resume.0', symObjAddr: 0x311C, symBinAddr: 0x1000043DC, symSize: 0xDC }
  - { offset: 0xEB18A, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC13_isMonitoring33_29BA33022BC77D68CF322D73212E107BLL7Combine9PublishedVySbGvg', symObjAddr: 0x31F8, symBinAddr: 0x1000044B8, symSize: 0x70 }
  - { offset: 0xEB19E, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC13_isMonitoring33_29BA33022BC77D68CF322D73212E107BLL7Combine9PublishedVySbGvs', symObjAddr: 0x3268, symBinAddr: 0x100004528, symSize: 0xD4 }
  - { offset: 0xEB1B2, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC5timer33_29BA33022BC77D68CF322D73212E107BLLSo7NSTimerCSgvg', symObjAddr: 0x3344, symBinAddr: 0x100004604, symSize: 0x64 }
  - { offset: 0xEB1C6, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC5timer33_29BA33022BC77D68CF322D73212E107BLLSo7NSTimerCSgvs', symObjAddr: 0x33A8, symBinAddr: 0x100004668, symSize: 0x84 }
  - { offset: 0xEB1DA, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC5timer33_29BA33022BC77D68CF322D73212E107BLLSo7NSTimerCSgvM', symObjAddr: 0x342C, symBinAddr: 0x1000046EC, symSize: 0x48 }
  - { offset: 0xEB1EE, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC5timer33_29BA33022BC77D68CF322D73212E107BLLSo7NSTimerCSgvM.resume.0', symObjAddr: 0x3474, symBinAddr: 0x100004734, symSize: 0x3C }
  - { offset: 0xEB202, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC15lastChangeCount33_29BA33022BC77D68CF322D73212E107BLLSivg', symObjAddr: 0x34B8, symBinAddr: 0x100004778, symSize: 0x58 }
  - { offset: 0xEB216, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC15lastChangeCount33_29BA33022BC77D68CF322D73212E107BLLSivs', symObjAddr: 0x3510, symBinAddr: 0x1000047D0, symSize: 0x58 }
  - { offset: 0xEB22A, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC15lastChangeCount33_29BA33022BC77D68CF322D73212E107BLLSivM', symObjAddr: 0x3568, symBinAddr: 0x100004828, symSize: 0x48 }
  - { offset: 0xEB23E, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC15lastChangeCount33_29BA33022BC77D68CF322D73212E107BLLSivM.resume.0', symObjAddr: 0x35B0, symBinAddr: 0x100004870, symSize: 0x3C }
  - { offset: 0xEB252, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC8maxItems33_29BA33022BC77D68CF322D73212E107BLLSivg', symObjAddr: 0x35F8, symBinAddr: 0x1000048B8, symSize: 0x10 }
  - { offset: 0xEB306, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC10pasteboard33_29BA33022BC77D68CF322D73212E107BLLSo12NSPasteboardCvg', symObjAddr: 0x3634, symBinAddr: 0x1000048F4, symSize: 0x38 }
  - { offset: 0xEB321, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerCACycfC', symObjAddr: 0x366C, symBinAddr: 0x10000492C, symSize: 0x38 }
  - { offset: 0xEB335, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerCACycfc', symObjAddr: 0x36A4, symBinAddr: 0x100004964, symSize: 0x254 }
  - { offset: 0xEB359, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC15startMonitoringyyF', symObjAddr: 0x38F8, symBinAddr: 0x100004BB8, symSize: 0x1B4 }
  - { offset: 0xEB37C, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC15startMonitoringyyFySo7NSTimerCYbcfU_', symObjAddr: 0x3AAC, symBinAddr: 0x100004D6C, symSize: 0xB4 }
  - { offset: 0xEB3B7, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC14stopMonitoringyyF', symObjAddr: 0x3BD0, symBinAddr: 0x100004E90, symSize: 0x144 }
  - { offset: 0xEB3DB, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC05checkA033_29BA33022BC77D68CF322D73212E107BLLyyF', symObjAddr: 0x3D14, symBinAddr: 0x100004FD4, symSize: 0x13C }
  - { offset: 0xEB415, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC010addCurrentA7Content33_29BA33022BC77D68CF322D73212E107BLLyyF', symObjAddr: 0x3E50, symBinAddr: 0x100005110, symSize: 0xF44 }
  - { offset: 0xEB4B4, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC06copyToA0yyAA0A4ItemVF', symObjAddr: 0x4D94, symBinAddr: 0x100006054, symSize: 0x3A8 }
  - { offset: 0xEB515, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC06copyToA0yyAA0A4ItemVFSbAFXEfU_', symObjAddr: 0x513C, symBinAddr: 0x1000063FC, symSize: 0x12C }
  - { offset: 0xEB557, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC10deleteItemyyAA0aE0VF', symObjAddr: 0x5268, symBinAddr: 0x100006528, symSize: 0xCC }
  - { offset: 0xEB58A, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC10deleteItemyyAA0aE0VFSbAFXEfU_', symObjAddr: 0x5334, symBinAddr: 0x1000065F4, symSize: 0x12C }
  - { offset: 0xEB5CC, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC8clearAllyyF', symObjAddr: 0x5460, symBinAddr: 0x100006720, symSize: 0x94 }
  - { offset: 0xEB5F0, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC16toggleMonitoringyyF', symObjAddr: 0x550C, symBinAddr: 0x1000067CC, symSize: 0xA4 }
  - { offset: 0xEB614, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC9saveItems33_29BA33022BC77D68CF322D73212E107BLLyyF', symObjAddr: 0x55B0, symBinAddr: 0x100006870, symSize: 0x244 }
  - { offset: 0xEB656, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerC9loadItems33_29BA33022BC77D68CF322D73212E107BLLyyF', symObjAddr: 0x57F4, symBinAddr: 0x100006AB4, symSize: 0x288 }
  - { offset: 0xEB720, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerCfd', symObjAddr: 0x6040, symBinAddr: 0x100007300, symSize: 0xC0 }
  - { offset: 0xEB744, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ManagerCfD', symObjAddr: 0x6100, symBinAddr: 0x1000073C0, symSize: 0x40 }
  - { offset: 0xEB768, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV16clipboardManager33_29BA33022BC77D68CF322D73212E107BLLAA0aF0Cvg', symObjAddr: 0x6204, symBinAddr: 0x1000074C4, symSize: 0x84 }
  - { offset: 0xEB788, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV17_clipboardManager33_29BA33022BC77D68CF322D73212E107BLL7SwiftUI11StateObjectVyAA0aF0CGvpfi', symObjAddr: 0x6288, symBinAddr: 0x100007548, symSize: 0x10 }
  - { offset: 0xEB79C, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV17_clipboardManager33_29BA33022BC77D68CF322D73212E107BLL7SwiftUI11StateObjectVyAA0aF0CGvpfiAJycfu_AJycfu0_', symObjAddr: 0x6298, symBinAddr: 0x100007558, symSize: 0x28 }
  - { offset: 0xEB9C8, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV17$clipboardManager33_29BA33022BC77D68CF322D73212E107BLL7SwiftUI14ObservedObjectV7WrapperVyAA0aF0C_Gvg', symObjAddr: 0x62C0, symBinAddr: 0x100007580, symSize: 0x84 }
  - { offset: 0xEB9DC, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV17_clipboardManager33_29BA33022BC77D68CF322D73212E107BLL7SwiftUI11StateObjectVyAA0aF0CGvg', symObjAddr: 0x6344, symBinAddr: 0x100007604, symSize: 0x48 }
  - { offset: 0xEB9F0, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV17_clipboardManager33_29BA33022BC77D68CF322D73212E107BLL7SwiftUI11StateObjectVyAA0aF0CGvs', symObjAddr: 0x638C, symBinAddr: 0x10000764C, symSize: 0x74 }
  - { offset: 0xEBA04, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV10searchText33_29BA33022BC77D68CF322D73212E107BLLSSvg', symObjAddr: 0x6490, symBinAddr: 0x100007750, symSize: 0x98 }
  - { offset: 0xEBA18, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV10searchText33_29BA33022BC77D68CF322D73212E107BLLSSvs', symObjAddr: 0x6528, symBinAddr: 0x1000077E8, symSize: 0xB4 }
  - { offset: 0xEBA2C, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV11$searchText33_29BA33022BC77D68CF322D73212E107BLL7SwiftUI7BindingVySSGvg', symObjAddr: 0x6604, symBinAddr: 0x1000078C4, symSize: 0xB0 }
  - { offset: 0xEBA40, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV11_searchText33_29BA33022BC77D68CF322D73212E107BLL7SwiftUI5StateVySSGvg', symObjAddr: 0x66B4, symBinAddr: 0x100007974, symSize: 0x48 }
  - { offset: 0xEBA54, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV11_searchText33_29BA33022BC77D68CF322D73212E107BLL7SwiftUI5StateVySSGvs', symObjAddr: 0x66FC, symBinAddr: 0x1000079BC, symSize: 0x74 }
  - { offset: 0xEBA68, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV12selectedType33_29BA33022BC77D68CF322D73212E107BLLAA0A4ItemV0aF0OSgvg', symObjAddr: 0x67C0, symBinAddr: 0x100007A80, symSize: 0x7C }
  - { offset: 0xEBA7C, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV12selectedType33_29BA33022BC77D68CF322D73212E107BLLAA0A4ItemV0aF0OSgvs', symObjAddr: 0x683C, symBinAddr: 0x100007AFC, symSize: 0x88 }
  - { offset: 0xEBA90, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV13$selectedType33_29BA33022BC77D68CF322D73212E107BLL7SwiftUI7BindingVyAA0A4ItemV0aF0OSgGvg', symObjAddr: 0x68CC, symBinAddr: 0x100007B8C, symSize: 0x94 }
  - { offset: 0xEBAA4, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV13_selectedType33_29BA33022BC77D68CF322D73212E107BLL7SwiftUI5StateVyAA0A4ItemV0aF0OSgGvg', symObjAddr: 0x6960, symBinAddr: 0x100007C20, symSize: 0x34 }
  - { offset: 0xEBAB8, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV13_selectedType33_29BA33022BC77D68CF322D73212E107BLL7SwiftUI5StateVyAA0A4ItemV0aF0OSgGvs', symObjAddr: 0x6994, symBinAddr: 0x100007C54, symSize: 0x48 }
  - { offset: 0xEBACC, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV13filteredItemsSayAA0A4ItemVGvg', symObjAddr: 0x69DC, symBinAddr: 0x100007C9C, symSize: 0x404 }
  - { offset: 0xEBB26, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV13filteredItemsSayAA0A4ItemVGvgSbAFXEfU_', symObjAddr: 0x6DE0, symBinAddr: 0x1000080A0, symSize: 0x154 }
  - { offset: 0xEBB67, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV13filteredItemsSayAA0A4ItemVGvgSbAFXEfU0_', symObjAddr: 0x6F34, symBinAddr: 0x1000081F4, symSize: 0x9C }
  - { offset: 0xEBBAF, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV4bodyQrvg', symObjAddr: 0x6FD0, symBinAddr: 0x100008290, symSize: 0x3A0 }
  - { offset: 0xEBBD3, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV4bodyQrvg7SwiftUI05TupleD0VyAC06headerD033_29BA33022BC77D68CF322D73212E107BLLQrvpQOy_Qo__AC015searchAndFilterD0AILLQrvpQOy_Qo_AC09itemsListD0AILLQrvpQOy_Qo_AC06footerD0AILLQrvpQOy_Qo_tGyXEfU_', symObjAddr: 0x7370, symBinAddr: 0x100008630, symSize: 0x47C }
  - { offset: 0xEBC06, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV06headerD033_29BA33022BC77D68CF322D73212E107BLLQrvg', symObjAddr: 0x77EC, symBinAddr: 0x100008AAC, symSize: 0x278 }
  - { offset: 0xEBC2A, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV06headerD033_29BA33022BC77D68CF322D73212E107BLLQrvg7SwiftUI05TupleD0VyAF0D0PAFE15foregroundColoryQrAF0P0VSgFQOyAjFE4fontyQrAF4FontVSgFQOyAF5ImageV_Qo__Qo__AF4TextVAF6SpacerVAjFE4helpyQrAF18LocalizedStringKeyVFQOyAjFE11buttonStyleyQrqd__AF20PrimitiveButtonStyleRd__lFQOyAF6ButtonVyAVG_AF16PlainButtonStyleVQo__Qo_tGyXEfU_', symObjAddr: 0x8280, symBinAddr: 0x100009540, symSize: 0x9EC }
  - { offset: 0xEBC55, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV06headerD033_29BA33022BC77D68CF322D73212E107BLLQrvg7SwiftUI05TupleD0VyAF0D0PAFE15foregroundColoryQrAF0P0VSgFQOyAjFE4fontyQrAF4FontVSgFQOyAF5ImageV_Qo__Qo__AF4TextVAF6SpacerVAjFE4helpyQrAF18LocalizedStringKeyVFQOyAjFE11buttonStyleyQrqd__AF20PrimitiveButtonStyleRd__lFQOyAF6ButtonVyAVG_AF16PlainButtonStyleVQo__Qo_tGyXEfU_yycAA0A7ManagerCcfu_', symObjAddr: 0x8C6C, symBinAddr: 0x100009F2C, symSize: 0x38 }
  - { offset: 0xEBC7D, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV06headerD033_29BA33022BC77D68CF322D73212E107BLLQrvg7SwiftUI05TupleD0VyAF0D0PAFE15foregroundColoryQrAF0P0VSgFQOyAjFE4fontyQrAF4FontVSgFQOyAF5ImageV_Qo__Qo__AF4TextVAF6SpacerVAjFE4helpyQrAF18LocalizedStringKeyVFQOyAjFE11buttonStyleyQrqd__AF20PrimitiveButtonStyleRd__lFQOyAF6ButtonVyAVG_AF16PlainButtonStyleVQo__Qo_tGyXEfU_yycAA0A7ManagerCcfu_yycfu0_', symObjAddr: 0x8CA4, symBinAddr: 0x100009F64, symSize: 0x3C }
  - { offset: 0xEBCA5, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV06headerD033_29BA33022BC77D68CF322D73212E107BLLQrvg7SwiftUI05TupleD0VyAF0D0PAFE15foregroundColoryQrAF0P0VSgFQOyAjFE4fontyQrAF4FontVSgFQOyAF5ImageV_Qo__Qo__AF4TextVAF6SpacerVAjFE4helpyQrAF18LocalizedStringKeyVFQOyAjFE11buttonStyleyQrqd__AF20PrimitiveButtonStyleRd__lFQOyAF6ButtonVyAVG_AF16PlainButtonStyleVQo__Qo_tGyXEfU_AVyXEfU_', symObjAddr: 0x8CE0, symBinAddr: 0x100009FA0, symSize: 0x364 }
  - { offset: 0xEBCD8, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI11ViewBuilderV15buildExpressionyxxAA0C0RzlFZ', symObjAddr: 0x7A64, symBinAddr: 0x100008D24, symSize: 0x44 }
  - { offset: 0xEBD01, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV015searchAndFilterD033_29BA33022BC77D68CF322D73212E107BLLQrvg', symObjAddr: 0x7AA8, symBinAddr: 0x100008D68, symSize: 0x1F8 }
  - { offset: 0xEBD25, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV015searchAndFilterD033_29BA33022BC77D68CF322D73212E107BLLQrvg7SwiftUI05TupleD0VyAF6HStackVyAHyAF0D0PAFE15foregroundColoryQrAF0S0VSgFQOyAF5ImageV_Qo__AlFE14textFieldStyleyQrqd__AF04TextvW0Rd__lFQOyAF0xV0VyAF0X0VG_AF013RoundedBorderxvW0VQo_AlFEAMyQrAPFQOyAlFE06buttonW0yQrqd__AF015PrimitiveButtonW0Rd__lFQOyAF6ButtonVyAYG_AF011PlainButtonW0VQo__Qo_SgtGG_AF06ScrollD0VyAlFE7paddingyQrAF4EdgeO3SetV_12CoreGraphics7CGFloatVSgtFQOyAJyAHyAlFEA2_yQrqd__AF06ButtonW0Rd__lFQOyA6__AA0g6ButtonW0VQo__AF7ForEachVySayAA0A4ItemV0A4TypeOGA34_A28_GtGG_Qo_GtGyXEfU_', symObjAddr: 0x9084, symBinAddr: 0x10000A344, symSize: 0x330 }
  - { offset: 0xEBD50, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV015searchAndFilterD033_29BA33022BC77D68CF322D73212E107BLLQrvg7SwiftUI05TupleD0VyAF6HStackVyAHyAF0D0PAFE15foregroundColoryQrAF0S0VSgFQOyAF5ImageV_Qo__AlFE14textFieldStyleyQrqd__AF04TextvW0Rd__lFQOyAF0xV0VyAF0X0VG_AF013RoundedBorderxvW0VQo_AlFEAMyQrAPFQOyAlFE06buttonW0yQrqd__AF015PrimitiveButtonW0Rd__lFQOyAF6ButtonVyAYG_AF011PlainButtonW0VQo__Qo_SgtGG_AF06ScrollD0VyAlFE7paddingyQrAF4EdgeO3SetV_12CoreGraphics7CGFloatVSgtFQOyAJyAHyAlFEA2_yQrqd__AF06ButtonW0Rd__lFQOyA6__AA0g6ButtonW0VQo__AF7ForEachVySayAA0A4ItemV0A4TypeOGA34_A28_GtGG_Qo_GtGyXEfU_A12_yXEfU_', symObjAddr: 0x93B4, symBinAddr: 0x10000A674, symSize: 0x9C0 }
  - { offset: 0xEBD7C, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV015searchAndFilterD033_29BA33022BC77D68CF322D73212E107BLLQrvg7SwiftUI05TupleD0VyAF6HStackVyAHyAF0D0PAFE15foregroundColoryQrAF0S0VSgFQOyAF5ImageV_Qo__AlFE14textFieldStyleyQrqd__AF04TextvW0Rd__lFQOyAF0xV0VyAF0X0VG_AF013RoundedBorderxvW0VQo_AlFEAMyQrAPFQOyAlFE06buttonW0yQrqd__AF015PrimitiveButtonW0Rd__lFQOyAF6ButtonVyAYG_AF011PlainButtonW0VQo__Qo_SgtGG_AF06ScrollD0VyAlFE7paddingyQrAF4EdgeO3SetV_12CoreGraphics7CGFloatVSgtFQOyAJyAHyAlFEA2_yQrqd__AF06ButtonW0Rd__lFQOyA6__AA0g6ButtonW0VQo__AF7ForEachVySayAA0A4ItemV0A4TypeOGA34_A28_GtGG_Qo_GtGyXEfU_A12_yXEfU_yyScMYccfU_', symObjAddr: 0x9DB8, symBinAddr: 0x10000B078, symSize: 0xDC }
  - { offset: 0xEBDA7, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV015searchAndFilterD033_29BA33022BC77D68CF322D73212E107BLLQrvg7SwiftUI05TupleD0VyAF6HStackVyAHyAF0D0PAFE15foregroundColoryQrAF0S0VSgFQOyAF5ImageV_Qo__AlFE14textFieldStyleyQrqd__AF04TextvW0Rd__lFQOyAF0xV0VyAF0X0VG_AF013RoundedBorderxvW0VQo_AlFEAMyQrAPFQOyAlFE06buttonW0yQrqd__AF015PrimitiveButtonW0Rd__lFQOyAF6ButtonVyAYG_AF011PlainButtonW0VQo__Qo_SgtGG_AF06ScrollD0VyAlFE7paddingyQrAF4EdgeO3SetV_12CoreGraphics7CGFloatVSgtFQOyAJyAHyAlFEA2_yQrqd__AF06ButtonW0Rd__lFQOyA6__AA0g6ButtonW0VQo__AF7ForEachVySayAA0A4ItemV0A4TypeOGA34_A28_GtGG_Qo_GtGyXEfU_A39_yXEfU0_', symObjAddr: 0x9EDC, symBinAddr: 0x10000B19C, symSize: 0x248 }
  - { offset: 0xEBDD2, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV015searchAndFilterD033_29BA33022BC77D68CF322D73212E107BLLQrvg7SwiftUI05TupleD0VyAF6HStackVyAHyAF0D0PAFE15foregroundColoryQrAF0S0VSgFQOyAF5ImageV_Qo__AlFE14textFieldStyleyQrqd__AF04TextvW0Rd__lFQOyAF0xV0VyAF0X0VG_AF013RoundedBorderxvW0VQo_AlFEAMyQrAPFQOyAlFE06buttonW0yQrqd__AF015PrimitiveButtonW0Rd__lFQOyAF6ButtonVyAYG_AF011PlainButtonW0VQo__Qo_SgtGG_AF06ScrollD0VyAlFE7paddingyQrAF4EdgeO3SetV_12CoreGraphics7CGFloatVSgtFQOyAJyAHyAlFEA2_yQrqd__AF06ButtonW0Rd__lFQOyA6__AA0g6ButtonW0VQo__AF7ForEachVySayAA0A4ItemV0A4TypeOGA34_A28_GtGG_Qo_GtGyXEfU_A39_yXEfU0_A37_yXEfU_', symObjAddr: 0xA124, symBinAddr: 0x10000B3E4, symSize: 0x718 }
  - { offset: 0xEBDFD, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV015searchAndFilterD033_29BA33022BC77D68CF322D73212E107BLLQrvg7SwiftUI05TupleD0VyAF6HStackVyAHyAF0D0PAFE15foregroundColoryQrAF0S0VSgFQOyAF5ImageV_Qo__AlFE14textFieldStyleyQrqd__AF04TextvW0Rd__lFQOyAF0xV0VyAF0X0VG_AF013RoundedBorderxvW0VQo_AlFEAMyQrAPFQOyAlFE06buttonW0yQrqd__AF015PrimitiveButtonW0Rd__lFQOyAF6ButtonVyAYG_AF011PlainButtonW0VQo__Qo_SgtGG_AF06ScrollD0VyAlFE7paddingyQrAF4EdgeO3SetV_12CoreGraphics7CGFloatVSgtFQOyAJyAHyAlFEA2_yQrqd__AF06ButtonW0Rd__lFQOyA6__AA0g6ButtonW0VQo__AF7ForEachVySayAA0A4ItemV0A4TypeOGA34_A28_GtGG_Qo_GtGyXEfU_A39_yXEfU0_A37_yXEfU_yyScMYccfU_', symObjAddr: 0xA83C, symBinAddr: 0x10000BAFC, symSize: 0x90 }
  - { offset: 0xEBE28, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV015searchAndFilterD033_29BA33022BC77D68CF322D73212E107BLLQrvg7SwiftUI05TupleD0VyAF6HStackVyAHyAF0D0PAFE15foregroundColoryQrAF0S0VSgFQOyAF5ImageV_Qo__AlFE14textFieldStyleyQrqd__AF04TextvW0Rd__lFQOyAF0xV0VyAF0X0VG_AF013RoundedBorderxvW0VQo_AlFEAMyQrAPFQOyAlFE06buttonW0yQrqd__AF015PrimitiveButtonW0Rd__lFQOyAF6ButtonVyAYG_AF011PlainButtonW0VQo__Qo_SgtGG_AF06ScrollD0VyAlFE7paddingyQrAF4EdgeO3SetV_12CoreGraphics7CGFloatVSgtFQOyAJyAHyAlFEA2_yQrqd__AF06ButtonW0Rd__lFQOyA6__AA0g6ButtonW0VQo__AF7ForEachVySayAA0A4ItemV0A4TypeOGA34_A28_GtGG_Qo_GtGyXEfU_A39_yXEfU0_A37_yXEfU_A28_A34_cfU0_', symObjAddr: 0xA8D4, symBinAddr: 0x10000BB94, symSize: 0x4C8 }
  - { offset: 0xEBE62, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV015searchAndFilterD033_29BA33022BC77D68CF322D73212E107BLLQrvg7SwiftUI05TupleD0VyAF6HStackVyAHyAF0D0PAFE15foregroundColoryQrAF0S0VSgFQOyAF5ImageV_Qo__AlFE14textFieldStyleyQrqd__AF04TextvW0Rd__lFQOyAF0xV0VyAF0X0VG_AF013RoundedBorderxvW0VQo_AlFEAMyQrAPFQOyAlFE06buttonW0yQrqd__AF015PrimitiveButtonW0Rd__lFQOyAF6ButtonVyAYG_AF011PlainButtonW0VQo__Qo_SgtGG_AF06ScrollD0VyAlFE7paddingyQrAF4EdgeO3SetV_12CoreGraphics7CGFloatVSgtFQOyAJyAHyAlFEA2_yQrqd__AF06ButtonW0Rd__lFQOyA6__AA0g6ButtonW0VQo__AF7ForEachVySayAA0A4ItemV0A4TypeOGA34_A28_GtGG_Qo_GtGyXEfU_A39_yXEfU0_A37_yXEfU_A28_A34_cfU0_yyScMYccfU_', symObjAddr: 0xAD9C, symBinAddr: 0x10000C05C, symSize: 0x1D0 }
  - { offset: 0xEBEA4, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV09itemsListD033_29BA33022BC77D68CF322D73212E107BLLQrvg', symObjAddr: 0x7CA0, symBinAddr: 0x100008F60, symSize: 0x98 }
  - { offset: 0xEBEC8, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV09itemsListD033_29BA33022BC77D68CF322D73212E107BLLQrvg7SwiftUI012_ConditionalC0VyAF0D0PAFE5frame8minWidth05idealR003maxR00Q6Height0sU00tU09alignmentQr12CoreGraphics7CGFloatVSg_A5vF9AlignmentVtFQOyAF6VStackVyAF05TupleD0VyAjFE15foregroundColoryQrAF5ColorVSgFQOyAjFE4fontyQrAF4FontVSgFQOyAF5ImageV_Qo__Qo__AF4TextVA14_SgtGG_Qo_AjFE9listStyleyQrqd__AF0F5StyleRd__lFQOyAF0F0Vys5NeverOAF7ForEachVySayAA0A4ItemVG10Foundation4UUIDVAA0A7ItemRowVGG_AF05PlainF5StyleVQo_GyXEfU_', symObjAddr: 0xAF6C, symBinAddr: 0x10000C22C, symSize: 0xA48 }
  - { offset: 0xEBEF5, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV09itemsListD033_29BA33022BC77D68CF322D73212E107BLLQrvg7SwiftUI012_ConditionalC0VyAF0D0PAFE5frame8minWidth05idealR003maxR00Q6Height0sU00tU09alignmentQr12CoreGraphics7CGFloatVSg_A5vF9AlignmentVtFQOyAF6VStackVyAF05TupleD0VyAjFE15foregroundColoryQrAF5ColorVSgFQOyAjFE4fontyQrAF4FontVSgFQOyAF5ImageV_Qo__Qo__AF4TextVA14_SgtGG_Qo_AjFE9listStyleyQrqd__AF0F5StyleRd__lFQOyAF0F0Vys5NeverOAF7ForEachVySayAA0A4ItemVG10Foundation4UUIDVAA0A7ItemRowVGG_AF05PlainF5StyleVQo_GyXEfU_A16_yXEfU_', symObjAddr: 0xB9B4, symBinAddr: 0x10000CC74, symSize: 0xAD8 }
  - { offset: 0xEBF22, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV09itemsListD033_29BA33022BC77D68CF322D73212E107BLLQrvg7SwiftUI012_ConditionalC0VyAF0D0PAFE5frame8minWidth05idealR003maxR00Q6Height0sU00tU09alignmentQr12CoreGraphics7CGFloatVSg_A5vF9AlignmentVtFQOyAF6VStackVyAF05TupleD0VyAjFE15foregroundColoryQrAF5ColorVSgFQOyAjFE4fontyQrAF4FontVSgFQOyAF5ImageV_Qo__Qo__AF4TextVA14_SgtGG_Qo_AjFE9listStyleyQrqd__AF0F5StyleRd__lFQOyAF0F0Vys5NeverOAF7ForEachVySayAA0A4ItemVG10Foundation4UUIDVAA0A7ItemRowVGG_AF05PlainF5StyleVQo_GyXEfU_A34_A28_cfU0_', symObjAddr: 0xC570, symBinAddr: 0x10000D830, symSize: 0x1E8 }
  - { offset: 0xEBF68, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV06footerD033_29BA33022BC77D68CF322D73212E107BLLQrvg', symObjAddr: 0x7D38, symBinAddr: 0x100008FF8, symSize: 0x278 }
  - { offset: 0xEBF8D, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV06footerD033_29BA33022BC77D68CF322D73212E107BLLQrvg7SwiftUI05TupleD0VyAF4TextV_AF6SpacerVAF0D0PAFE15foregroundColoryQrAF0R0VSgFQOyAnFE11buttonStyleyQrqd__AF015PrimitiveButtonT0Rd__lFQOyAF0V0VyAJG_AF05PlainvT0VQo__Qo_SgtGyXEfU_', symObjAddr: 0xCA1C, symBinAddr: 0x10000DCDC, symSize: 0xB58 }
  - { offset: 0xEBFBA, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV06footerD033_29BA33022BC77D68CF322D73212E107BLLQrvg7SwiftUI05TupleD0VyAF4TextV_AF6SpacerVAF0D0PAFE15foregroundColoryQrAF0R0VSgFQOyAnFE11buttonStyleyQrqd__AF015PrimitiveButtonT0Rd__lFQOyAF0V0VyAJG_AF05PlainvT0VQo__Qo_SgtGyXEfU_yyScMYccfU_', symObjAddr: 0xD574, symBinAddr: 0x10000E834, symSize: 0xAC }
  - { offset: 0xEBFEF, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI11ViewBuilderV10buildBlockyAA05TupleC0VyxxQp_tGxxQpRvzAA0C0RzlFZ', symObjAddr: 0x7FB0, symBinAddr: 0x100009270, symSize: 0x228 }
  - { offset: 0xEC011, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI6VStackV9alignment7spacing7contentACyxGAA19HorizontalAlignmentV_12CoreGraphics7CGFloatVSgxyXEtcfcfA_', symObjAddr: 0x81D8, symBinAddr: 0x100009498, symSize: 0x20 }
  - { offset: 0xEC03D, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI4ViewPAAE5frame5width6height9alignmentQr12CoreGraphics7CGFloatVSg_AkA9AlignmentVtFfA1_', symObjAddr: 0x81F8, symBinAddr: 0x1000094B8, symSize: 0x20 }
  - { offset: 0xEC067, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI4ViewPAAE10background_20ignoresSafeAreaEdgesQrqd___AA4EdgeO3SetVtAA10ShapeStyleRd__lFfA0_', symObjAddr: 0x8218, symBinAddr: 0x1000094D8, symSize: 0x24 }
  - { offset: 0xEC09E, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI4ViewPAAE7paddingyQrAA4EdgeO3SetV_12CoreGraphics7CGFloatVSgtFfA_', symObjAddr: 0x9064, symBinAddr: 0x10000A324, symSize: 0x20 }
  - { offset: 0xEC0C8, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI11ViewBuilderV7buildIfyxSgAeA0C0RzlFZ', symObjAddr: 0x9E94, symBinAddr: 0x10000B154, symSize: 0x48 }
  - { offset: 0xEC0F2, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI4ViewPAAE5frame8minWidth05idealF003maxF00E6Height0gI00hI09alignmentQr12CoreGraphics7CGFloatVSg_A5oA9AlignmentVtFfA5_', symObjAddr: 0xC48C, symBinAddr: 0x10000D74C, symSize: 0x20 }
  - { offset: 0xEC11C, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI11ViewBuilderV11buildEither5firstAA19_ConditionalContentVyxq_Gx_tAA0C0RzAaIR_r0_lFZ', symObjAddr: 0xC4AC, symBinAddr: 0x10000D76C, symSize: 0xC4 }
  - { offset: 0xEC153, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI11ViewBuilderV11buildEither6secondAA19_ConditionalContentVyxq_Gq__tAA0C0RzAaIR_r0_lFZ', symObjAddr: 0xC958, symBinAddr: 0x10000DC18, symSize: 0xC4 }
  - { offset: 0xEC18A, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV7SwiftUI0D0AadEP05_makeD04view6inputsAD01_D7OutputsVAD11_GraphValueVyxG_AD01_D6InputsVtFZTW', symObjAddr: 0xD7B8, symBinAddr: 0x10000EA78, symSize: 0x14 }
  - { offset: 0xEC1A6, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV7SwiftUI0D0AadEP05_makeD4List4view6inputsAD01_dH7OutputsVAD11_GraphValueVyxG_AD01_dH6InputsVtFZTW', symObjAddr: 0xD7CC, symBinAddr: 0x10000EA8C, symSize: 0x14 }
  - { offset: 0xEC1C2, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV7SwiftUI0D0AadEP14_viewListCount6inputsSiSgAD01_dhI6InputsV_tFZTW', symObjAddr: 0xD7E0, symBinAddr: 0x10000EAA0, symSize: 0x18 }
  - { offset: 0xEC1DE, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI4ViewPAAE12onTapGesture5count7performQrSi_yyctFfA_', symObjAddr: 0xFFE8, symBinAddr: 0x1000112A8, symSize: 0x18 }
  - { offset: 0xEC208, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ItemRowV7SwiftUI4ViewAadEP05_makeG04view6inputsAD01_G7OutputsVAD11_GraphValueVyxG_AD01_G6InputsVtFZTW', symObjAddr: 0x1002C, symBinAddr: 0x1000112EC, symSize: 0x14 }
  - { offset: 0xEC224, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ItemRowV7SwiftUI4ViewAadEP05_makeG4List4view6inputsAD01_gI7OutputsVAD11_GraphValueVyxG_AD01_gI6InputsVtFZTW', symObjAddr: 0x10040, symBinAddr: 0x100011300, symSize: 0x14 }
  - { offset: 0xEC240, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ItemRowV7SwiftUI4ViewAadEP14_viewListCount6inputsSiSgAD01_giJ6InputsV_tFZTW', symObjAddr: 0x10054, symBinAddr: 0x100011314, symSize: 0x18 }
  - { offset: 0xEC25C, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI5ShapePAAE4fill_5styleQrqd___AA9FillStyleVtAA0cG0Rd__lFfA0_', symObjAddr: 0x10918, symBinAddr: 0x100011BD8, symSize: 0x24 }
  - { offset: 0xEC293, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI4ViewPAAE10background_9alignmentQrqd___AA9AlignmentVtAaBRd__lFfA0_', symObjAddr: 0x1093C, symBinAddr: 0x100011BFC, symSize: 0x24 }
  - { offset: 0xEC2CA, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI4ViewPAAE11scaleEffect_6anchorQr12CoreGraphics7CGFloatV_AA9UnitPointVtFfA0_', symObjAddr: 0x10960, symBinAddr: 0x100011C20, symSize: 0x20 }
  - { offset: 0xEC539, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI11ViewBuilderV10buildBlockyxxAA0C0RzlFZ', symObjAddr: 0x823C, symBinAddr: 0x1000094FC, symSize: 0x44 }
  - { offset: 0xEC55B, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI6HStackV9alignment7spacing7contentACyxGAA17VerticalAlignmentV_12CoreGraphics7CGFloatVSgxyXEtcfcfA_', symObjAddr: 0x9044, symBinAddr: 0x10000A304, symSize: 0x20 }
  - { offset: 0xEC587, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI9TextFieldVA2A0C0VRszrlE_4textACyAEGAA18LocalizedStringKeyV_AA7BindingVySSGtcfC', symObjAddr: 0x9D74, symBinAddr: 0x10000B034, symSize: 0x44 }
  - { offset: 0xEC5A3, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI4ListVAAs5NeverORszrlE_10rowContentACyAeA7ForEachVyqd__7Element_2IDQYd__qd_0_GGqd___qd_0_AIQyd__ctcALRs_SkRd__AA4ViewRd_0_s12IdentifiableANRQr0_lufC', symObjAddr: 0xC7D4, symBinAddr: 0x10000DA94, symSize: 0x184 }
  - { offset: 0xEC5DA, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI4ViewPAAE12contentShape_6eoFillQrqd___SbtAA0E0Rd__lFfA0_', symObjAddr: 0xFE44, symBinAddr: 0x100011104, symSize: 0x1C }
  - { offset: 0xEC66E, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory17FilterButtonStyleV10isSelectedACSb_tcfC', symObjAddr: 0xA8CC, symBinAddr: 0x10000BB8C, symSize: 0x8 }
  - { offset: 0xEC689, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ItemRowV4item16clipboardManagerAcA0aC0V_AA0aG0CtcfC', symObjAddr: 0xC758, symBinAddr: 0x10000DA18, symSize: 0x7C }
  - { offset: 0xEC6A4, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewVACycfC', symObjAddr: 0xD620, symBinAddr: 0x10000E8E0, symSize: 0x198 }
  - { offset: 0xEC6C9, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11ContentViewV7SwiftUI0D0AadEP4body4BodyQzvgTW', symObjAddr: 0xD7F8, symBinAddr: 0x10000EAB8, symSize: 0x44 }
  - { offset: 0xEC6DD, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ItemRowV4itemAA0aC0Vvg', symObjAddr: 0xD83C, symBinAddr: 0x10000EAFC, symSize: 0x1C }
  - { offset: 0xEC6F1, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ItemRowV16clipboardManagerAA0aF0Cvg', symObjAddr: 0xD858, symBinAddr: 0x10000EB18, symSize: 0x34 }
  - { offset: 0xEC705, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ItemRowV9isHovered33_29BA33022BC77D68CF322D73212E107BLLSbvg', symObjAddr: 0xD8DC, symBinAddr: 0x10000EB9C, symSize: 0x98 }
  - { offset: 0xEC719, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ItemRowV9isHovered33_29BA33022BC77D68CF322D73212E107BLLSbvs', symObjAddr: 0xD974, symBinAddr: 0x10000EC34, symSize: 0xA0 }
  - { offset: 0xEC72D, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ItemRowV10$isHovered33_29BA33022BC77D68CF322D73212E107BLL7SwiftUI7BindingVySbGvg', symObjAddr: 0xDA20, symBinAddr: 0x10000ECE0, symSize: 0xB0 }
  - { offset: 0xEC741, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ItemRowV10_isHovered33_29BA33022BC77D68CF322D73212E107BLL7SwiftUI5StateVySbGvg', symObjAddr: 0xDAD0, symBinAddr: 0x10000ED90, symSize: 0x48 }
  - { offset: 0xEC755, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ItemRowV10_isHovered33_29BA33022BC77D68CF322D73212E107BLL7SwiftUI5StateVySbGvs', symObjAddr: 0xDB18, symBinAddr: 0x10000EDD8, symSize: 0x58 }
  - { offset: 0xEC770, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ItemRowV4bodyQrvg', symObjAddr: 0xDB70, symBinAddr: 0x10000EE30, symSize: 0x5A4 }
  - { offset: 0xEC795, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ItemRowV4bodyQrvg7SwiftUI9TupleViewVyAE0I0PAEE5frame5width6height9alignmentQr12CoreGraphics7CGFloatVSg_AqE9AlignmentVtFQOyAiEE15foregroundColoryQrAE0S0VSgFQOyAiEE4fontyQrAE4FontVSgFQOyAE5ImageV_Qo__Qo__Qo__AE6VStackVyAGyAiEE14truncationModeyQrAE4TextV010TruncationY0OFQOyAiEE9lineLimityQrSiSgFQOyA9__Qo__Qo__A9_tGGAE6SpacerVAE6HStackVyAGyAiEE4helpyQrAE18LocalizedStringKeyVFQOyAiEE11buttonStyleyQrqd__AE20PrimitiveButtonStyleRd__lFQOyAE6ButtonVyA1_G_AE16PlainButtonStyleVQo__Qo__AiEEA22_yQrA24_FQOyAiEEATyQrAWFQOyA32__Qo__Qo_tGGSgtGyXEfU_', symObjAddr: 0xE114, symBinAddr: 0x10000F3D4, symSize: 0x7B8 }
  - { offset: 0xEC7C3, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ItemRowV4bodyQrvg7SwiftUI9TupleViewVyAE0I0PAEE5frame5width6height9alignmentQr12CoreGraphics7CGFloatVSg_AqE9AlignmentVtFQOyAiEE15foregroundColoryQrAE0S0VSgFQOyAiEE4fontyQrAE4FontVSgFQOyAE5ImageV_Qo__Qo__Qo__AE6VStackVyAGyAiEE14truncationModeyQrAE4TextV010TruncationY0OFQOyAiEE9lineLimityQrSiSgFQOyA9__Qo__Qo__A9_tGGAE6SpacerVAE6HStackVyAGyAiEE4helpyQrAE18LocalizedStringKeyVFQOyAiEE11buttonStyleyQrqd__AE20PrimitiveButtonStyleRd__lFQOyAE6ButtonVyA1_G_AE16PlainButtonStyleVQo__Qo__AiEEA22_yQrA24_FQOyAiEEATyQrAWFQOyA32__Qo__Qo_tGGSgtGyXEfU_A16_yXEfU_', symObjAddr: 0xE8CC, symBinAddr: 0x10000FB8C, symSize: 0x714 }
  - { offset: 0xEC7F0, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ItemRowV4bodyQrvg7SwiftUI9TupleViewVyAE0I0PAEE5frame5width6height9alignmentQr12CoreGraphics7CGFloatVSg_AqE9AlignmentVtFQOyAiEE15foregroundColoryQrAE0S0VSgFQOyAiEE4fontyQrAE4FontVSgFQOyAE5ImageV_Qo__Qo__Qo__AE6VStackVyAGyAiEE14truncationModeyQrAE4TextV010TruncationY0OFQOyAiEE9lineLimityQrSiSgFQOyA9__Qo__Qo__A9_tGGAE6SpacerVAE6HStackVyAGyAiEE4helpyQrAE18LocalizedStringKeyVFQOyAiEE11buttonStyleyQrqd__AE20PrimitiveButtonStyleRd__lFQOyAE6ButtonVyA1_G_AE16PlainButtonStyleVQo__Qo__AiEEA22_yQrA24_FQOyAiEEATyQrAWFQOyA32__Qo__Qo_tGGSgtGyXEfU_A36_yXEfU0_', symObjAddr: 0xF184, symBinAddr: 0x100010444, symSize: 0x9B8 }
  - { offset: 0xEC81D, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ItemRowV4bodyQrvg7SwiftUI9TupleViewVyAE0I0PAEE5frame5width6height9alignmentQr12CoreGraphics7CGFloatVSg_AqE9AlignmentVtFQOyAiEE15foregroundColoryQrAE0S0VSgFQOyAiEE4fontyQrAE4FontVSgFQOyAE5ImageV_Qo__Qo__Qo__AE6VStackVyAGyAiEE14truncationModeyQrAE4TextV010TruncationY0OFQOyAiEE9lineLimityQrSiSgFQOyA9__Qo__Qo__A9_tGGAE6SpacerVAE6HStackVyAGyAiEE4helpyQrAE18LocalizedStringKeyVFQOyAiEE11buttonStyleyQrqd__AE20PrimitiveButtonStyleRd__lFQOyAE6ButtonVyA1_G_AE16PlainButtonStyleVQo__Qo__AiEEA22_yQrA24_FQOyAiEEATyQrAWFQOyA32__Qo__Qo_tGGSgtGyXEfU_A36_yXEfU0_yyScMYccfU_', symObjAddr: 0xFB3C, symBinAddr: 0x100010DFC, symSize: 0xC8 }
  - { offset: 0xEC84A, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ItemRowV4bodyQrvg7SwiftUI9TupleViewVyAE0I0PAEE5frame5width6height9alignmentQr12CoreGraphics7CGFloatVSg_AqE9AlignmentVtFQOyAiEE15foregroundColoryQrAE0S0VSgFQOyAiEE4fontyQrAE4FontVSgFQOyAE5ImageV_Qo__Qo__Qo__AE6VStackVyAGyAiEE14truncationModeyQrAE4TextV010TruncationY0OFQOyAiEE9lineLimityQrSiSgFQOyA9__Qo__Qo__A9_tGGAE6SpacerVAE6HStackVyAGyAiEE4helpyQrAE18LocalizedStringKeyVFQOyAiEE11buttonStyleyQrqd__AE20PrimitiveButtonStyleRd__lFQOyAE6ButtonVyA1_G_AE16PlainButtonStyleVQo__Qo__AiEEA22_yQrA24_FQOyAiEEATyQrAWFQOyA32__Qo__Qo_tGGSgtGyXEfU_A36_yXEfU0_A1_yXEfU0_', symObjAddr: 0xFC04, symBinAddr: 0x100010EC4, symSize: 0xBC }
  - { offset: 0xEC865, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ItemRowV4bodyQrvg7SwiftUI9TupleViewVyAE0I0PAEE5frame5width6height9alignmentQr12CoreGraphics7CGFloatVSg_AqE9AlignmentVtFQOyAiEE15foregroundColoryQrAE0S0VSgFQOyAiEE4fontyQrAE4FontVSgFQOyAE5ImageV_Qo__Qo__Qo__AE6VStackVyAGyAiEE14truncationModeyQrAE4TextV010TruncationY0OFQOyAiEE9lineLimityQrSiSgFQOyA9__Qo__Qo__A9_tGGAE6SpacerVAE6HStackVyAGyAiEE4helpyQrAE18LocalizedStringKeyVFQOyAiEE11buttonStyleyQrqd__AE20PrimitiveButtonStyleRd__lFQOyAE6ButtonVyA1_G_AE16PlainButtonStyleVQo__Qo__AiEEA22_yQrA24_FQOyAiEEATyQrAWFQOyA32__Qo__Qo_tGGSgtGyXEfU_A36_yXEfU0_yyScMYccfU1_', symObjAddr: 0xFCC0, symBinAddr: 0x100010F80, symSize: 0xC8 }
  - { offset: 0xEC892, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ItemRowV4bodyQrvg7SwiftUI9TupleViewVyAE0I0PAEE5frame5width6height9alignmentQr12CoreGraphics7CGFloatVSg_AqE9AlignmentVtFQOyAiEE15foregroundColoryQrAE0S0VSgFQOyAiEE4fontyQrAE4FontVSgFQOyAE5ImageV_Qo__Qo__Qo__AE6VStackVyAGyAiEE14truncationModeyQrAE4TextV010TruncationY0OFQOyAiEE9lineLimityQrSiSgFQOyA9__Qo__Qo__A9_tGGAE6SpacerVAE6HStackVyAGyAiEE4helpyQrAE18LocalizedStringKeyVFQOyAiEE11buttonStyleyQrqd__AE20PrimitiveButtonStyleRd__lFQOyAE6ButtonVyA1_G_AE16PlainButtonStyleVQo__Qo__AiEEA22_yQrA24_FQOyAiEEATyQrAWFQOyA32__Qo__Qo_tGGSgtGyXEfU_A36_yXEfU0_A1_yXEfU2_', symObjAddr: 0xFD88, symBinAddr: 0x100011048, symSize: 0xBC }
  - { offset: 0xEC8AD, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ItemRowV4bodyQrvgySbcfU0_', symObjAddr: 0xFE60, symBinAddr: 0x100011120, symSize: 0xC0 }
  - { offset: 0xEC8EA, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ItemRowV4bodyQrvgyycfU1_', symObjAddr: 0xFF20, symBinAddr: 0x1000111E0, symSize: 0xC8 }
  - { offset: 0xEC918, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ItemRowV15formatTimestamp33_29BA33022BC77D68CF322D73212E107BLLySS10Foundation4DateVF', symObjAddr: 0xEFE0, symBinAddr: 0x1000102A0, symSize: 0x1A4 }
  - { offset: 0xEC9D7, size: 0x8, addend: 0x0, symName: '_$sSo27NSRelativeDateTimeFormatterCABycfC', symObjAddr: 0x10000, symBinAddr: 0x1000112C0, symSize: 0x2C }
  - { offset: 0xEC9EB, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0A7ItemRowV7SwiftUI4ViewAadEP4body4BodyQzvgTW', symObjAddr: 0x1006C, symBinAddr: 0x10001132C, symSize: 0x14 }
  - { offset: 0xEC9FF, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory17FilterButtonStyleV10isSelectedSbvg', symObjAddr: 0x10080, symBinAddr: 0x100011340, symSize: 0x8 }
  - { offset: 0xECA1A, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory17FilterButtonStyleV8makeBody13configurationQr7SwiftUI0dE13ConfigurationV_tF', symObjAddr: 0x10088, symBinAddr: 0x100011348, symSize: 0x844 }
  - { offset: 0xECA50, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI16RoundedRectangleV12cornerRadius5styleAC12CoreGraphics7CGFloatV_AA0C11CornerStyleOtcfcfA0_', symObjAddr: 0x108CC, symBinAddr: 0x100011B8C, symSize: 0x4C }
  - { offset: 0xECA64, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory17FilterButtonStyleV7SwiftUI0dE0AadEP8makeBody13configuration0I0QzAD0dE13ConfigurationV_tFTW', symObjAddr: 0x10980, symBinAddr: 0x100011C40, symSize: 0x1C }
  - { offset: 0xECA78, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11AppDelegateC10statusItemSo08NSStatusF0CSgvg', symObjAddr: 0x10AB0, symBinAddr: 0x100011D70, symSize: 0x6C }
  - { offset: 0xECA9D, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11AppDelegateC10statusItemSo08NSStatusF0CSgvs', symObjAddr: 0x10B1C, symBinAddr: 0x100011DDC, symSize: 0x94 }
  - { offset: 0xECAD2, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11AppDelegateC10statusItemSo08NSStatusF0CSgvM', symObjAddr: 0x10BB0, symBinAddr: 0x100011E70, symSize: 0x50 }
  - { offset: 0xECAF7, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11AppDelegateC10statusItemSo08NSStatusF0CSgvM.resume.0', symObjAddr: 0x10C00, symBinAddr: 0x100011EC0, symSize: 0x3C }
  - { offset: 0xECB19, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11AppDelegateC7popoverSo9NSPopoverCSgvg', symObjAddr: 0x10D50, symBinAddr: 0x100012010, symSize: 0x6C }
  - { offset: 0xECB3E, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11AppDelegateC7popoverSo9NSPopoverCSgvs', symObjAddr: 0x10DBC, symBinAddr: 0x10001207C, symSize: 0x94 }
  - { offset: 0xECB73, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11AppDelegateC7popoverSo9NSPopoverCSgvM', symObjAddr: 0x10E50, symBinAddr: 0x100012110, symSize: 0x50 }
  - { offset: 0xECB98, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11AppDelegateC7popoverSo9NSPopoverCSgvM.resume.0', symObjAddr: 0x10EA0, symBinAddr: 0x100012160, symSize: 0x3C }
  - { offset: 0xECBBA, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11AppDelegateC29applicationDidFinishLaunchingyy10Foundation12NotificationVF', symObjAddr: 0x10EDC, symBinAddr: 0x10001219C, symSize: 0x140 }
  - { offset: 0xECBF0, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11AppDelegateC29applicationDidFinishLaunchingyy10Foundation12NotificationVFTo', symObjAddr: 0x1101C, symBinAddr: 0x1000122DC, symSize: 0xE0 }
  - { offset: 0xECC04, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11AppDelegateC12setupMenuBar33_29BA33022BC77D68CF322D73212E107BLLyyF', symObjAddr: 0x110FC, symBinAddr: 0x1000123BC, symSize: 0x304 }
  - { offset: 0xECC4F, size: 0x8, addend: 0x0, symName: '_$sSo7NSImageC16systemSymbolName24accessibilityDescriptionABSgSS_SSSgtcfCTO', symObjAddr: 0x11400, symBinAddr: 0x1000126C0, symSize: 0xE8 }
  - { offset: 0xECC63, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11AppDelegateC12setupPopover33_29BA33022BC77D68CF322D73212E107BLLyyF', symObjAddr: 0x114E8, symBinAddr: 0x1000127A8, symSize: 0x318 }
  - { offset: 0xECC90, size: 0x8, addend: 0x0, symName: '_$sSo9NSPopoverCABycfC', symObjAddr: 0x11800, symBinAddr: 0x100012AC0, symSize: 0x2C }
  - { offset: 0xECCA4, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11AppDelegateC13togglePopoveryyF', symObjAddr: 0x1182C, symBinAddr: 0x100012AEC, symSize: 0x56C }
  - { offset: 0xECCE8, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11AppDelegateC13togglePopoveryyFTo', symObjAddr: 0x11D98, symBinAddr: 0x100013058, symSize: 0x4C }
  - { offset: 0xECCFC, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11AppDelegateC24applicationWillTerminateyy10Foundation12NotificationVF', symObjAddr: 0x11DE4, symBinAddr: 0x1000130A4, symSize: 0x1C }
  - { offset: 0xECD32, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11AppDelegateC24applicationWillTerminateyy10Foundation12NotificationVFTo', symObjAddr: 0x11E00, symBinAddr: 0x1000130C0, symSize: 0xE0 }
  - { offset: 0xECD4D, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11AppDelegateCACycfC', symObjAddr: 0x11EE0, symBinAddr: 0x1000131A0, symSize: 0x24 }
  - { offset: 0xECD61, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11AppDelegateCACycfc', symObjAddr: 0x11F04, symBinAddr: 0x1000131C4, symSize: 0xF0 }
  - { offset: 0xECD86, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11AppDelegateCACycfcTo', symObjAddr: 0x11FF4, symBinAddr: 0x1000132B4, symSize: 0x20 }
  - { offset: 0xECD9A, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory11AppDelegateCfD', symObjAddr: 0x12014, symBinAddr: 0x1000132D4, symSize: 0x48 }
  - { offset: 0xECDBF, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0aB3AppV11appDelegateAA0cE0Cvg', symObjAddr: 0x120A4, symBinAddr: 0x100013364, symSize: 0xB8 }
  - { offset: 0xECDE5, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0aB3AppV12_appDelegate33_29BA33022BC77D68CF322D73212E107BLL7SwiftUI013NSApplicationE7AdaptorVyAA0cE0CGvg', symObjAddr: 0x12190, symBinAddr: 0x100013450, symSize: 0x44 }
  - { offset: 0xECDF9, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0aB3AppV12_appDelegate33_29BA33022BC77D68CF322D73212E107BLL7SwiftUI013NSApplicationE7AdaptorVyAA0cE0CGvs', symObjAddr: 0x121D4, symBinAddr: 0x100013494, symSize: 0xA8 }
  - { offset: 0xECE14, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0aB3AppV4bodyQrvg', symObjAddr: 0x1227C, symBinAddr: 0x10001353C, symSize: 0x150 }
  - { offset: 0xECE39, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0aB3AppV4bodyQrvg7SwiftUI9EmptyViewVyXEfU_', symObjAddr: 0x123CC, symBinAddr: 0x10001368C, symSize: 0x58 }
  - { offset: 0xECE5C, size: 0x8, addend: 0x0, symName: '_$s7SwiftUI12SceneBuilderV15buildExpressionyxxAA0C0RzlFZ', symObjAddr: 0x12424, symBinAddr: 0x1000136E4, symSize: 0x44 }
  - { offset: 0xECE8A, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0aB3AppV5$mainyyFZ', symObjAddr: 0x12468, symBinAddr: 0x100013728, symSize: 0x34 }
  - { offset: 0xECEAA, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0aB3AppVACycfC', symObjAddr: 0x1249C, symBinAddr: 0x10001375C, symSize: 0x90 }
  - { offset: 0xECED0, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0aB3AppV11appDelegateAC7SwiftUI013NSApplicationE7AdaptorVyAA0cE0CG_tcfC', symObjAddr: 0x12560, symBinAddr: 0x100013820, symSize: 0x44 }
  - { offset: 0xECEE4, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0aB3AppV7SwiftUI0C0AadEP4body4BodyQzvgTW', symObjAddr: 0x125A4, symBinAddr: 0x100013864, symSize: 0x14 }
  - { offset: 0xECEFF, size: 0x8, addend: 0x0, symName: '_$s16ClipboardHistory0aB3AppV7SwiftUI0C0AadEPxycfCTW', symObjAddr: 0x125B8, symBinAddr: 0x100013878, symSize: 0x14 }
  - { offset: 0xECF13, size: 0x8, addend: 0x0, symName: _ClipboardHistory_main, symObjAddr: 0x125CC, symBinAddr: 0x10001388C, symSize: 0x18 }
  - { offset: 0xECF27, size: 0x8, addend: 0x0, symName: '_$sSo27NSRelativeDateTimeFormatterCABycfcTO', symObjAddr: 0x125E4, symBinAddr: 0x1000138A4, symSize: 0x20 }
  - { offset: 0xECF3B, size: 0x8, addend: 0x0, symName: '_$sSo9NSPopoverCABycfcTO', symObjAddr: 0x12604, symBinAddr: 0x1000138C4, symSize: 0x20 }
  - { offset: 0xED105, size: 0x8, addend: 0x0, symName: _NSVariableStatusItemLength, symObjAddr: 0x20370, symBinAddr: 0x100022470, symSize: 0x0 }
...
