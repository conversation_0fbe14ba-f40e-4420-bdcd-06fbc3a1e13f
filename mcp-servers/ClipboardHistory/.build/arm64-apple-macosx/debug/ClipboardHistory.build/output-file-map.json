{"": {"swift-dependencies": "/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/ClipboardHistory.build/master.swiftdeps"}, "/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/Sources/ClipboardHistory/main.swift": {"dependencies": "/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/ClipboardHistory.build/main.d", "object": "/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/ClipboardHistory.build/main.swift.o", "swiftmodule": "/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/ClipboardHistory.build/main~partial.swiftmodule", "swift-dependencies": "/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/ClipboardHistory.build/main.swiftdeps"}}