client:
  name: basic
  file-system: device-agnostic
tools: {}
targets:
  "ClipboardHistory-arm64-apple-macosx15.0-debug.exe": ["<ClipboardHistory-arm64-apple-macosx15.0-debug.exe>"]
  "ClipboardHistory-arm64-apple-macosx15.0-debug.module": ["<ClipboardHistory-arm64-apple-macosx15.0-debug.module>"]
  "PackageStructure": ["<PackageStructure>"]
  "main": ["<ClipboardHistory-arm64-apple-macosx15.0-debug.exe>","<ClipboardHistory-arm64-apple-macosx15.0-debug.module>"]
  "test": ["<ClipboardHistory-arm64-apple-macosx15.0-debug.exe>","<ClipboardHistory-arm64-apple-macosx15.0-debug.module>"]
default: "main"
nodes:
  "/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/Sources/ClipboardHistory/":
    is-directory-structure: true
    content-exclusion-patterns: [".git",".build"]
  "/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/ClipboardHistory":
    is-mutated: true
commands:
  "/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/ClipboardHistory-entitlement.plist":
    tool: write-auxiliary-file
    inputs: ["<entitlement-plist>","<com.apple.security.get-task-allow>"]
    outputs: ["/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/ClipboardHistory-entitlement.plist"]
    description: "Write auxiliary file /private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/ClipboardHistory-entitlement.plist"

  "/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/ClipboardHistory.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/Sources/ClipboardHistory/main.swift"]
    outputs: ["/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/ClipboardHistory.build/sources"]
    description: "Write auxiliary file /private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/ClipboardHistory.build/sources"

  "/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/ClipboardHistory.product/Objects.LinkFileList":
    tool: write-auxiliary-file
    inputs: ["<link-file-list>","/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/ClipboardHistory.build/main.swift.o"]
    outputs: ["/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/ClipboardHistory.product/Objects.LinkFileList"]
    description: "Write auxiliary file /private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/ClipboardHistory.product/Objects.LinkFileList"

  "/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt":
    tool: write-auxiliary-file
    inputs: ["<swift-get-version>","/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"]
    outputs: ["/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"]
    always-out-of-date: "true"
    description: "Write auxiliary file /private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"

  "<ClipboardHistory-arm64-apple-macosx15.0-debug.exe>":
    tool: phony
    inputs: ["<ClipboardHistory-arm64-apple-macosx15.0-debug.exe-CodeSigning>"]
    outputs: ["<ClipboardHistory-arm64-apple-macosx15.0-debug.exe>"]

  "<ClipboardHistory-arm64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/ClipboardHistory.build/main.swift.o","/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/Modules/ClipboardHistory.swiftmodule"]
    outputs: ["<ClipboardHistory-arm64-apple-macosx15.0-debug.module>"]

  "C.ClipboardHistory-arm64-apple-macosx15.0-debug.exe":
    tool: shell
    inputs: ["/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/ClipboardHistory.build/main.swift.o","/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/ClipboardHistory.product/Objects.LinkFileList"]
    outputs: ["/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/ClipboardHistory"]
    description: "Linking ./.build/arm64-apple-macosx/debug/ClipboardHistory"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-L","/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug","-o","/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/ClipboardHistory","-module-name","ClipboardHistory","-Xlinker","-no_warn_duplicate_libraries","-emit-executable","-Xlinker","-alias","-Xlinker","_ClipboardHistory_main","-Xlinker","_main","-Xlinker","-rpath","-Xlinker","@loader_path","@/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/ClipboardHistory.product/Objects.LinkFileList","-target","arm64-apple-macosx13.0","-Xlinker","-add_ast_path","-Xlinker","/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/Modules/ClipboardHistory.swiftmodule","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g"]

  "C.ClipboardHistory-arm64-apple-macosx15.0-debug.exe-entitlements":
    tool: shell
    inputs: ["/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/ClipboardHistory","/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/ClipboardHistory-entitlement.plist"]
    outputs: ["<ClipboardHistory-arm64-apple-macosx15.0-debug.exe-CodeSigning>"]
    description: "Applying debug entitlements to ./.build/arm64-apple-macosx/debug/ClipboardHistory"
    args: ["codesign","--force","--sign","-","--entitlements","/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/ClipboardHistory-entitlement.plist","/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/ClipboardHistory"]

  "C.ClipboardHistory-arm64-apple-macosx15.0-debug.module":
    tool: shell
    inputs: ["/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/Sources/ClipboardHistory/main.swift","/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt","/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/ClipboardHistory.build/sources"]
    outputs: ["/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/ClipboardHistory.build/main.swift.o","/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/Modules/ClipboardHistory.swiftmodule"]
    description: "Compiling Swift Module 'ClipboardHistory' (1 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","ClipboardHistory","-emit-dependencies","-emit-module","-emit-module-path","/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/Modules/ClipboardHistory.swiftmodule","-output-file-map","/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/ClipboardHistory.build/output-file-map.json","-incremental","-c","@/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/ClipboardHistory.build/sources","-I","/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/Modules","-target","arm64-apple-macosx13.0","-enable-batch-mode","-index-store-path","/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/index/store","-Onone","-enable-testing","-j8","-DSWIFT_PACKAGE","-DDEBUG","-module-cache-path","/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/.build/arm64-apple-macosx/debug/ModuleCache","-parseable-output","-Xfrontend","-entry-point-function-name","-Xfrontend","ClipboardHistory_main","-parse-as-library","-color-diagnostics","-swift-version","5","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-Xcc","-fPIC","-Xcc","-g"]

  "PackageStructure":
    tool: package-structure-tool
    inputs: ["/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/Sources/ClipboardHistory/","/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/Package.swift","/private/var/www/2025/ollamar1/beauty-crm/ClipboardHistory/Package.resolved"]
    outputs: ["<PackageStructure>"]
    description: "Planning build"
    allow-missing-inputs: true

