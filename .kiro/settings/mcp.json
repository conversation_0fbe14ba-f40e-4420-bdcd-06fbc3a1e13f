{"mcpServers": {"fetch": {"args": ["mcp-server-fetch"], "autoApprove": [], "command": "uvx", "disabled": false, "env": {}}, "context7": {"args": ["-y", "@upstash/context7-mcp"], "autoApprove": [], "command": "npx", "disabled": false, "env": {}}, "beauty_crm_appointment": {"args": ["-y", "@modelcontextprotocol/server-postgres", "postgresql://beauty_crm:beauty_crm_password@localhost:5432/beauty_crm_appointment"], "command": "npx", "disabled": true}, "beauty_crm_planner": {"args": ["-y", "@modelcontextprotocol/server-postgres", "postgresql://beauty_crm:beauty_crm_password@localhost:5432/beauty_crm_planner"], "command": "npx", "disabled": true}, "Prisma-Local": {"args": ["-y", "prisma", "mcp"], "command": "npx", "disabled": false}, "playwright": {"args": ["-y", "@playwright/mcp"], "command": "npx", "disabled": false}, "web-search": {"command": "npx", "args": ["-y", "@pskill9/web-search-mcp"], "env": {}, "disabled": true, "autoApprove": []}, "youtube-mcp": {"command": "npx", "args": ["-y", "@spolepaka/youtube-mcp"], "env": {}, "disabled": true, "autoApprove": []}, "file-system": {"command": "npx", "args": ["-y", "@open-source/file-system-mcp"], "env": {}, "disabled": true, "autoApprove": []}}}