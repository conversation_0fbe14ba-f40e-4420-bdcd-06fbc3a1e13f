# 🔄 Beauty CRM CDC Configuration

## Overview

This document describes the Change Data Capture (CDC) configuration for the Beauty CRM event-driven architecture. The CDC pipeline enables real-time synchronization between the appointment planner service and appointment management service using the transactional outbox pattern.

## Architecture

```
┌─────────────────┐    ┌──────────────┐    ┌─────────────┐    ┌──────────────┐    ┌─────────────────┐
│ Appointment     │    │ PostgreSQL   │    │ De<PERSON>zium    │    │ <PERSON><PERSON><PERSON>        │    │ NATS Connector  │
│ Planner Service │───▶│ Outbox Table │───▶│ Connector   │───▶│ Topics       │───▶│ (Bridge)        │
└─────────────────┘    └──────────────┘    └─────────────┘    └─────────────┘    └─────────────────┘
                                                                                           │
                                                                                           ▼
                                                                                  ┌─────────────────┐
                                                                                  │ NATS JetStream  │
                                                                                  │ (Event Store)   │
                                                                                  └─────────────────┘
                                                                                           │
                                                                                           ▼
                                                                                  ┌─────────────────┐
                                                                                  │ Appointment     │
                                                                                  │ Management      │
                                                                                  │ Service         │
                                                                                  └─────────────────┘
```

## Components

### 1. PostgreSQL Outbox Table

**Table**: `appointment_outbox`
**Purpose**: Stores domain events atomically with business data

**Schema**:
```sql
CREATE TABLE appointment_outbox (
  id            TEXT PRIMARY KEY DEFAULT gen_random_uuid(),
  aggregateId   TEXT NOT NULL,
  aggregateType TEXT NOT NULL DEFAULT 'appointment',
  eventType     TEXT NOT NULL,
  eventId       TEXT UNIQUE NOT NULL,
  eventVersion  INTEGER NOT NULL DEFAULT 1,
  eventData     JSONB NOT NULL,
  eventMetadata JSONB,
  source        TEXT NOT NULL DEFAULT 'appointment-planner-backend',
  timestamp     TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  processed     BOOLEAN NOT NULL DEFAULT false,
  processedAt   TIMESTAMP(3),
  retryCount    INTEGER NOT NULL DEFAULT 0,
  lastRetryAt   TIMESTAMP(3),
  errorMessage  TEXT,
  createdAt     TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updatedAt     TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

### 2. Debezium PostgreSQL Connector

**Configuration**: `services/orchestration/debezium/connectors/appointment-outbox-connector.json`

**Key Settings**:
- **Database**: `beauty_crm_postgres:5432/beauty_crm_appointment`
- **Table**: `public.appointment_outbox`
- **Plugin**: `pgoutput` (PostgreSQL logical replication)
- **Transformation**: Outbox Event Router

**Outbox Transformation**:
```json
{
  "transforms": "outbox",
  "transforms.outbox.type": "io.debezium.transforms.outbox.EventRouter",
  "transforms.outbox.table.field.event.id": "eventId",
  "transforms.outbox.table.field.event.key": "aggregateId",
  "transforms.outbox.table.field.event.timestamp": "timestamp",
  "transforms.outbox.table.field.event.payload": "eventData",
  "transforms.outbox.route.by.field": "aggregateType",
  "transforms.outbox.route.topic.replacement": "${routedByValue}.events"
}
```

### 3. Kafka Topics

**Generated Topics**:
- `appointment.events` - Transformed outbox events
- `beauty_crm.appointment.schema_changes` - Schema change events

**Message Format** (after transformation):
```json
{
  "eventId": "uuid",
  "aggregateId": "appointment-123",
  "eventType": "AppointmentCreated",
  "eventVersion": 1,
  "source": "appointment-planner-backend",
  "timestamp": "2025-01-01T12:00:00.000Z",
  "payload": {
    "appointmentId": "appointment-123",
    "customerId": "customer-456",
    "treatmentId": "treatment-789",
    "salonId": "salon-101",
    "scheduledAt": "2025-01-01T14:00:00.000Z",
    "status": "scheduled"
  },
  "metadata": {
    "correlationId": "correlation-123",
    "causationId": "causation-456"
  }
}
```

### 4. NATS Connector

**Purpose**: Bridges Kafka topics to NATS JetStream
**Location**: `services/orchestration/debezium/nats-connector/`

**Configuration**:
- **Kafka Topics**: `appointment.events`
- **NATS Stream**: `APPOINTMENT_EVENTS`
- **Subject Pattern**: `appointment.events.{eventType}`

**Event Processing**:
1. Consumes from Kafka `appointment.events` topic
2. Transforms to NATS JetStream format
3. Publishes to `appointment.events.AppointmentCreated` subject
4. Provides deduplication using `eventId` as `msgID`

### 5. NATS JetStream

**Stream**: `APPOINTMENT_EVENTS`
**Subjects**: `appointment.events.*`
**Retention**: Interest-based with deduplication
**Storage**: File-based persistence

## Event Flow

### 1. Event Creation
```typescript
// In appointment planner service
const event = createAppointmentCreatedEvent({
  appointmentId: 'appointment-123',
  customerId: 'customer-456',
  // ... other data
});

await outboxManager.storeEvent(event, transaction);
```

### 2. CDC Processing
1. **PostgreSQL**: Event stored in `appointment_outbox` table
2. **Debezium**: Detects change via logical replication
3. **Transformation**: Outbox router transforms to domain event
4. **Kafka**: Event published to `appointment.events` topic

### 3. NATS Publishing
1. **NATS Connector**: Consumes from Kafka
2. **Transformation**: Converts to NATS format
3. **JetStream**: Event stored with deduplication
4. **Subject**: `appointment.events.AppointmentCreated`

### 4. Event Consumption
```typescript
// In appointment management service
const subscription = await jetstream.subscribe('appointment.events.AppointmentCreated');
for await (const message of subscription) {
  const event = jc.decode(message.data);
  await handleAppointmentCreated(event);
  message.ack();
}
```

## Configuration Files

### Updated Files
1. **Connector Config**: `services/orchestration/debezium/connectors/appointment-outbox-connector.json`
   - Updated database hostname to `beauty_crm_postgres`
   - Fixed timestamp field mapping to `timestamp`

2. **Setup Script**: `services/orchestration/debezium/setup-connectors.sh`
   - Updated PostgreSQL hostname reference

3. **NATS Connector**: `services/orchestration/debezium/nats-connector/src/index.js`
   - Updated to handle outbox-transformed events
   - Fixed event processing for new schema
   - Updated default Kafka topic to `appointment.events`

4. **Docker Compose**: `services/orchestration/docker-compose.debezium.yml`
   - Updated NATS connector environment variables

## Testing

### Manual Testing
```bash
# Run the CDC pipeline test
./services/orchestration/debezium/test-cdc-pipeline.sh
```

### Verification Steps
1. **Check Connector Status**:
   ```bash
   curl http://localhost:8083/connectors/beauty-crm-appointment-outbox-connector/status
   ```

2. **Monitor Kafka Topics**:
   ```bash
   kafka-topics.sh --bootstrap-server localhost:9092 --list
   ```

3. **Check NATS Streams**:
   ```bash
   nats --server=nats://localhost:4222 stream info APPOINTMENT_EVENTS
   ```

4. **Insert Test Event**:
   ```sql
   INSERT INTO appointment_outbox (
     id, aggregateId, eventType, eventId, eventData, source
   ) VALUES (
     gen_random_uuid(), 'test-appointment', 'AppointmentCreated', 
     'test-event-' || extract(epoch from now()), 
     '{"appointmentId": "test-appointment", "status": "scheduled"}',
     'appointment-planner-backend'
   );
   ```

## Monitoring

### Health Checks
- **PostgreSQL**: `pg_isready -h beauty_crm_postgres -U beauty_crm`
- **Debezium**: `curl -f http://localhost:8083`
- **Kafka**: `kafka-broker-api-versions --bootstrap-server localhost:9092`
- **NATS**: `curl -f http://localhost:8222/varz`

### Monitoring Endpoints
- **Debezium UI**: http://debezium-ui.localhost
- **NATS Monitoring**: http://nats.localhost
- **Kafka Manager**: (if configured)

## Troubleshooting

### Common Issues
1. **Connector Not Starting**: Check PostgreSQL connection and replication slot
2. **Events Not Flowing**: Verify outbox table permissions and publication
3. **NATS Connection Issues**: Check NATS connector logs and network connectivity
4. **Duplicate Events**: Verify deduplication is working with `msgID`

### Log Locations
- **Debezium**: `docker logs beauty_crm_debezium_connect`
- **NATS Connector**: `docker logs beauty_crm_nats_connector`
- **Kafka**: `docker logs beauty_crm_kafka`


## Tasks

### Extracted Tasks

- [ ] **Database**: `beauty_crm_postgres:5432/beauty_crm_appointment` - M1
- [ ] **Table**: `public.appointment_outbox` - M2
- [ ] **Plugin**: `pgoutput` (PostgreSQL logical replication) - M3
- [ ] **Transformation**: Outbox Event Router - M4
- [ ] `appointment.events` - Transformed outbox events - M5
- [ ] `beauty_crm.appointment.schema_changes` - Schema change events - M6
- [ ] **Kafka Topics**: `appointment.events` - M7
- [ ] **NATS Stream**: `APPOINTMENT_EVENTS` - M8
- [ ] **Subject Pattern**: `appointment.events.{eventType}` - M9
- [ ] Updated database hostname to `beauty_crm_postgres` - M10
- [ ] Fixed timestamp field mapping to `timestamp` - M11
- [ ] Updated PostgreSQL hostname reference - M12
- [ ] Updated to handle outbox-transformed events - M13
- [ ] Fixed event processing for new schema - M14
- [ ] Updated default Kafka topic to `appointment.events` - M15
- [ ] Updated NATS connector environment variables - M16
- [ ] **PostgreSQL**: `pg_isready -h beauty_crm_postgres -U beauty_crm` - M17
- [ ] **Debezium**: `curl -f http://localhost:8083` - M18
- [ ] **Kafka**: `kafka-broker-api-versions --bootstrap-server localhost:9092` - M19
- [ ] **NATS**: `curl -f http://localhost:8222/varz` - M20
- [ ] **Debezium UI**: http://debezium-ui.localhost - M21
- [ ] **NATS Monitoring**: http://nats.localhost - M22
- [ ] **Kafka Manager**: (if configured) - M23
- [ ] **Debezium**: `docker logs beauty_crm_debezium_connect` - M24
- [ ] **NATS Connector**: `docker logs beauty_crm_nats_connector` - M25
- [ ] **Kafka**: `docker logs beauty_crm_kafka` - M26

### General Tasks

- [ ] Review and update content - M1
- [ ] Add missing information - M2
- [ ] Improve structure - M3
- [ ] Add examples - M4
- [ ] Validate accuracy - M5

