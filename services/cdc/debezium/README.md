# 🔄 Debezium CDC Configuration for Beauty CRM

This directory contains the Debezium Change Data Capture (CDC) configuration for the Beauty CRM event-driven architecture. It enables reliable event publishing from the transactional outbox pattern to NATS JetStream.

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌──────────────┐    ┌─────────────┐    ┌──────────────┐
│   Appointment   │    │  PostgreSQL  │    │  Debezium   │    │    Kafka     │
│   Service       │───▶│   Outbox     │───▶│  Connector  │───▶│   Topics     │
│                 │    │   Table      │    │             │    │              │
└─────────────────┘    └──────────────┘    └─────────────┘    └──────────────┘
                                                                       │
                                                                       ▼
┌─────────────────┐    ┌──────────────┐    ┌─────────────┐    ┌──────────────┐
│   Consuming     │◀───│     NATS     │◀───│    NATS     │◀───│    Kafka     │
│   Services      │    │  JetStream   │    │ Connector   │    │  Consumer    │
│                 │    │              │    │             │    │              │
└─────────────────┘    └──────────────┘    └─────────────┘    └──────────────┘
```

## 📁 Directory Structure

```
debezium/
├── README.md                           # This file
├── docker-compose.debezium.yml         # Debezium services configuration
├── connectors/
│   └── appointment-outbox-connector.json  # Outbox table connector config
├── nats-connector/
│   ├── Dockerfile                      # NATS bridge container
│   ├── package.json                    # Node.js dependencies
│   ├── src/index.js                    # Main connector logic
│   └── health-check.js                 # Health monitoring
├── setup-connectors.sh                 # Connector setup script
└── test-event-flow.sh                  # End-to-end testing script
```

## 🚀 Quick Start

### 1. Enable Debezium in Tilt

```bash
# Start with Debezium enabled
tilt up --debezium

# Or enable specific services
tilt up --databases --appointment --debezium
```

### 2. Setup Connectors

```bash
# Run the setup script to configure connectors
./services/orchestration/debezium/setup-connectors.sh
```

### 3. Test Event Flow

```bash
# Run end-to-end test
./services/orchestration/debezium/test-event-flow.sh
```

## 🔧 Configuration Details

### Debezium Connector

The appointment outbox connector monitors the `appointment_outbox` table and publishes changes to Kafka topics:

- **Source**: `public.appointment_outbox` table
- **Target**: Kafka topic `appointment.events`
- **Transform**: Outbox Event Router pattern
- **Replication**: PostgreSQL logical replication with `pgoutput`

### NATS Connector

The custom NATS connector bridges Kafka topics to NATS JetStream:

- **Source**: Kafka topics from Debezium
- **Target**: NATS JetStream `APPOINTMENT_EVENTS` stream
- **Subjects**: `appointment.events.*`
- **Deduplication**: Message ID based on event ID

## 📊 Monitoring & Management

### Debezium UI
- **URL**: http://debezium-ui.localhost
- **Purpose**: Monitor connector status, view configurations
- **Features**: Real-time connector health, task management

### Debezium Connect API
- **URL**: http://debezium.localhost
- **Endpoints**:
  - `GET /connectors` - List all connectors
  - `GET /connectors/{name}/status` - Check connector status
  - `POST /connectors` - Create new connector
  - `PUT /connectors/{name}/config` - Update connector config

### NATS Monitoring
```bash
# Check stream info (requires NATS CLI)
nats stream info APPOINTMENT_EVENTS

# Subscribe to events
nats sub "appointment.events.*"

# View stream messages
nats stream view APPOINTMENT_EVENTS
```

## 🧪 Testing

### Manual Testing

1. **Create an appointment** via the API:
```bash
curl -X POST http://localhost:3000/appointments/v2 \
  -H "Content-Type: application/json" \
  -d '{
    "salonId": "test-salon",
    "customerName": "Test Customer",
    "customerEmail": "<EMAIL>",
    "treatmentName": "Test Treatment",
    "startTime": "2024-01-01T10:00:00Z",
    "endTime": "2024-01-01T11:00:00Z"
  }'
```

2. **Check outbox table**:
```sql
SELECT * FROM appointment_outbox ORDER BY created_at DESC LIMIT 5;
```

3. **Monitor NATS events**:
```bash
nats sub "appointment.events.*"
```

### Automated Testing

Run the comprehensive test suite:
```bash
./services/orchestration/debezium/test-event-flow.sh
```

## 🔍 Troubleshooting

### Common Issues

1. **Connector fails to start**
   - Check PostgreSQL connectivity
   - Verify replication slot exists
   - Check publication permissions

2. **No events in NATS**
   - Verify NATS connector is running
   - Check Kafka topic has messages
   - Validate NATS stream configuration

3. **Replication lag**
   - Monitor Debezium metrics
   - Check PostgreSQL replication slots
   - Verify network connectivity

### Debug Commands

```bash
# Check connector status
curl http://debezium.localhost/connectors/beauty-crm-appointment-outbox-connector/status

# View connector logs
docker logs beauty_crm_debezium_connect

# Check PostgreSQL replication
SELECT * FROM pg_replication_slots;

# Monitor Kafka topics
docker exec beauty_crm_kafka kafka-console-consumer.sh \
  --bootstrap-server localhost:9092 \
  --topic beauty_crm.public.appointment_outbox
```

## 🔐 Security Considerations

- PostgreSQL replication user has minimal required permissions
- Kafka internal communication (no external exposure)
- NATS connector validates message schemas
- Debezium UI access restricted to development environment

## 📈 Performance Tuning

### Debezium Connector
- `max.batch.size`: 2048 (adjust based on load)
- `poll.interval.ms`: 1000 (balance latency vs throughput)
- `heartbeat.interval.ms`: 30000 (keep connection alive)

### NATS Connector
- Consumer group for horizontal scaling
- Batch processing for high throughput
- Error handling with retry logic

## 🔄 Maintenance

### Regular Tasks
1. Monitor replication slot size
2. Clean up old Kafka topics
3. Rotate NATS stream messages
4. Update connector configurations

### Backup & Recovery
- PostgreSQL replication slots are recreated automatically
- Kafka topics retain configurable message history
- NATS streams have configurable retention policies

## 📚 References

- [Debezium PostgreSQL Connector](https://debezium.io/documentation/reference/connectors/postgresql.html)
- [Outbox Event Router](https://debezium.io/documentation/reference/transformations/outbox-event-router.html)
- [NATS JetStream](https://docs.nats.io/nats-concepts/jetstream)
- [Beauty CRM Event Architecture](../../../docs/event-architecture.md)


## Tasks

### Extracted Tasks

- [ ] **Source**: `public.appointment_outbox` table - M1
- [ ] **Target**: Kafka topic `appointment.events` - M2
- [ ] **Transform**: Outbox Event Router pattern - M3
- [ ] **Replication**: PostgreSQL logical replication with `pgoutput` - M4
- [ ] **Source**: Kafka topics from Debezium - M5
- [ ] **Target**: NATS JetStream `APPOINTMENT_EVENTS` stream - M6
- [ ] **Subjects**: `appointment.events.*` - M7
- [ ] **Deduplication**: Message ID based on event ID - M8
- [ ] **URL**: http://debezium-ui.localhost - M9
- [ ] **Purpose**: Monitor connector status, view configurations - M10
- [ ] **Features**: Real-time connector health, task management - M11
- [ ] **URL**: http://debezium.localhost - M12
- [ ] **Endpoints**: - M13
- [ ] `GET /connectors` - List all connectors - M14
- [ ] `GET /connectors/{name}/status` - Check connector status - M15
- [ ] `POST /connectors` - Create new connector - M16
- [ ] `PUT /connectors/{name}/config` - Update connector config - M17
- [ ] Check PostgreSQL connectivity - M18
- [ ] Verify replication slot exists - M19
- [ ] Check publication permissions - M20
- [ ] Verify NATS connector is running - M21
- [ ] Check Kafka topic has messages - M22
- [ ] Validate NATS stream configuration - M23
- [ ] Monitor Debezium metrics - M24
- [ ] Check PostgreSQL replication slots - M25
- [ ] Verify network connectivity - M26
- [ ] PostgreSQL replication user has minimal required permissions - M27
- [ ] Kafka internal communication (no external exposure) - M28
- [ ] NATS connector validates message schemas - M29
- [ ] Debezium UI access restricted to development environment - M30
- [ ] `max.batch.size`: 2048 (adjust based on load) - M31
- [ ] `poll.interval.ms`: 1000 (balance latency vs throughput) - M32
- [ ] `heartbeat.interval.ms`: 30000 (keep connection alive) - M33
- [ ] Consumer group for horizontal scaling - M34
- [ ] Batch processing for high throughput - M35
- [ ] Error handling with retry logic - M36
- [ ] PostgreSQL replication slots are recreated automatically - M37
- [ ] Kafka topics retain configurable message history - M38
- [ ] NATS streams have configurable retention policies - M39
- [ ] [Debezium PostgreSQL Connector](https://debezium.io/documentation/reference/connectors/postgresql.html) - M40
- [ ] [Outbox Event Router](https://debezium.io/documentation/reference/transformations/outbox-event-router.html) - M41
- [ ] [NATS JetStream](https://docs.nats.io/nats-concepts/jetstream) - M42
- [ ] [Beauty CRM Event Architecture](../../../docs/event-architecture.md) - M43

### General Tasks

- [ ] Review and update content - M1
- [ ] Add missing information - M2
- [ ] Improve structure - M3
- [ ] Add examples - M4
- [ ] Validate accuracy - M5

