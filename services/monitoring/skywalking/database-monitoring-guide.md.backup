# Beauty CRM PostgreSQL Database Monitoring with SkyWalking

## Overview
This guide explains how to access and use the comprehensive PostgreSQL database monitoring setup in SkyWalking for the Beauty CRM project.

## Access Points

### 1. SkyWalking UI
- **URL**: http://skywalking.beauty-crm.localhost/
- **Description**: Main SkyWalking dashboard with database monitoring capabilities

### 2. PostgreSQL Metrics Exporter
- **URL**: http://postgres-metrics.beauty-crm.localhost/metrics
- **Description**: Raw PostgreSQL metrics in Prometheus format
- **Direct Port**: http://localhost:9187/metrics

### 3. OpenTelemetry Collector
- **URL**: http://otel-skywalking.beauty-crm.localhost/
- **Description**: Metrics collection and processing endpoint

## Database Monitoring Features

### 1. Database Overview Dashboard
Navigate to: **SkyWalking UI > Database > PostgreSQL Overview**

**Key Metrics Available:**
- Database connection count and utilization
- Database size and growth trends
- Buffer cache hit ratio
- Transaction rates (commits/rollbacks)
- Query performance metrics

### 2. Performance Monitoring
Navigate to: **SkyWalking UI > Database > Performance**

**Available Metrics:**
- Query execution rates
- I/O operations (blocks read/written)
- Data modification rates (inserts/updates/deletes)
- Error rates and deadlocks
- Temporary file usage

### 3. Table-Level Statistics
Navigate to: **SkyWalking UI > Database > Tables**

**Table Metrics:**
- Table sizes and growth
- Index sizes and usage
- Sequential vs index scan ratios
- Vacuum and analyze activity
- Row modification statistics

### 4. System Resources
Navigate to: **SkyWalking UI > Database > System**

**Resource Metrics:**
- Database uptime
- Connection utilization percentage
- Background writer activity
- Checkpoint operations
- WAL (Write-Ahead Log) statistics

## Database Services in Topology

The following database services are monitored:

1. **beauty-crm-postgres**: Main database metrics service
2. **beauty-crm-postgres-instance**: Database instance health monitoring
3. **beauty-crm-postgres-tables**: Table-level statistics
4. **beauty-crm-postgres-indexes**: Index performance monitoring
5. **beauty-crm-postgres-queries**: Query performance analysis

## Navigation Guide

### Step 1: Access SkyWalking UI
1. Open your browser and go to: http://skywalking.beauty-crm.localhost/
2. You should see the main SkyWalking dashboard

### Step 2: Navigate to Database Monitoring
1. Look for the **Services** or **Topology** section in the main navigation
2. Find the database services (they will be labeled with "beauty-crm-postgres" prefix)
3. Click on any database service to view detailed metrics

### Step 3: View Database Dashboards
1. In the service view, look for dashboard or metrics tabs
2. You can switch between different time ranges (last hour, day, week)
3. Use the dashboard templates we've configured for comprehensive views

### Step 4: Monitor Real-Time Metrics
1. Database metrics are updated every 30 seconds
2. Look for alerts or anomalies in the metrics
3. Use the topology view to see database relationships with other services

## Key Metrics to Monitor

### Health Indicators
- **pg_up**: Database connectivity (should be 1)
- **Active Connections**: Current database connections
- **Buffer Hit Ratio**: Should be > 95% for good performance
- **Connection Utilization**: Should be < 80% of max_connections

### Performance Indicators
- **Transaction Rate**: Transactions per second
- **Query Response Time**: Average query execution time
- **I/O Operations**: Disk read/write activity
- **Lock Waits**: Database lock contention

### Capacity Indicators
- **Database Size**: Total database size in MB/GB
- **Table Growth**: Individual table size trends
- **Index Usage**: Index scan vs sequential scan ratios
- **Temporary Files**: Temporary file creation rate

## Troubleshooting

### If Database Metrics Are Not Showing
1. Check PostgreSQL exporter status:
   ```bash
   docker compose -f services/orchestration/docker-compose.apm.yml ps postgres-exporter
   ```

2. Verify metrics endpoint:
   ```bash
   curl http://localhost:9187/metrics | grep pg_up
   ```

3. Check OpenTelemetry collector logs:
   ```bash
   docker compose -f services/orchestration/docker-compose.apm.yml logs otel-collector-skywalking
   ```

### If SkyWalking UI Is Not Accessible
1. Check SkyWalking services:
   ```bash
   docker compose -f services/orchestration/docker-compose.apm.yml ps skywalking-ui skywalking-oap
   ```

2. Verify Traefik routing:
   ```bash
   curl -I http://skywalking.beauty-crm.localhost/
   ```

## Advanced Features

### Custom Queries
The PostgreSQL exporter includes custom queries for:
- Slow query analysis
- Lock monitoring
- Replication lag (if applicable)
- Table and index statistics

### Alerting
You can set up alerts in SkyWalking for:
- High connection usage
- Low buffer hit ratio
- Long-running queries
- Database size growth

### Historical Analysis
SkyWalking stores metrics for configurable retention periods:
- Real-time: Last 30 minutes
- Hourly aggregation: Last 7 days
- Daily aggregation: Last 30 days

## Database Schema Information

**Monitored Databases:**
- beauty_crm_appointment 
- beauty_crm_planner 
- beauty_crm_salon
- beauty_crm_staff

**Key Tables Being Monitored:**
- All user tables in the public schema
- System tables for performance metrics
- Index usage statistics

## Security Notes

- Database credentials are configured in Docker Compose environment variables
- Metrics are exposed only within the Docker network
- External access is through Traefik proxy with proper routing rules

## Support

For issues with database monitoring:
1. Check the logs of all monitoring services
2. Verify PostgreSQL extensions are enabled (pg_stat_statements)
3. Ensure network connectivity between services
4. Review SkyWalking configuration files for any errors
