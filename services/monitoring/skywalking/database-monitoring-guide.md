# Beauty CRM PostgreSQL Database Monitoring with SkyWalking

## Overview
This guide explains how to access and use the comprehensive PostgreSQL database monitoring setup in SkyWalking for the Beauty CRM project.

## Access Points

### 1. SkyWalking UI
- **URL**: http://skywalking.beauty-crm.localhost/
- **Description**: Main SkyWalking dashboard with database monitoring capabilities

### 2. PostgreSQL Metrics Exporter
- **URL**: http://postgres-metrics.beauty-crm.localhost/metrics
- **Description**: Raw PostgreSQL metrics in Prometheus format
- **Direct Port**: http://localhost:9187/metrics

### 3. OpenTelemetry Collector
- **URL**: http://otel-skywalking.beauty-crm.localhost/
- **Description**: Metrics collection and processing endpoint

## Database Monitoring Features

### 1. Database Overview Dashboard
Navigate to: **SkyWalking UI > Database > PostgreSQL Overview**

**Key Metrics Available:**
- Database connection count and utilization
- Database size and growth trends
- Buffer cache hit ratio
- Transaction rates (commits/rollbacks)
- Query performance metrics

### 2. Performance Monitoring
Navigate to: **SkyWalking UI > Database > Performance**

**Available Metrics:**
- Query execution rates
- I/O operations (blocks read/written)
- Data modification rates (inserts/updates/deletes)
- Error rates and deadlocks
- Temporary file usage

### 3. Table-Level Statistics
Navigate to: **SkyWalking UI > Database > Tables**

**Table Metrics:**
- Table sizes and growth
- Index sizes and usage
- Sequential vs index scan ratios
- Vacuum and analyze activity
- Row modification statistics

### 4. System Resources
Navigate to: **SkyWalking UI > Database > System**

**Resource Metrics:**
- Database uptime
- Connection utilization percentage
- Background writer activity
- Checkpoint operations
- WAL (Write-Ahead Log) statistics

## Database Services in Topology

The following database services are monitored:

1. **beauty-crm-postgres**: Main database metrics service
2. **beauty-crm-postgres-instance**: Database instance health monitoring
3. **beauty-crm-postgres-tables**: Table-level statistics
4. **beauty-crm-postgres-indexes**: Index performance monitoring
5. **beauty-crm-postgres-queries**: Query performance analysis

## Navigation Guide

### Step 1: Access SkyWalking UI
1. Open your browser and go to: http://skywalking.beauty-crm.localhost/
2. You should see the main SkyWalking dashboard

### Step 2: Navigate to Database Monitoring
1. Look for the **Services** or **Topology** section in the main navigation
2. Find the database services (they will be labeled with "beauty-crm-postgres" prefix)
3. Click on any database service to view detailed metrics

### Step 3: View Database Dashboards
1. In the service view, look for dashboard or metrics tabs
2. You can switch between different time ranges (last hour, day, week)
3. Use the dashboard templates we've configured for comprehensive views

### Step 4: Monitor Real-Time Metrics
1. Database metrics are updated every 30 seconds
2. Look for alerts or anomalies in the metrics
3. Use the topology view to see database relationships with other services

## Key Metrics to Monitor

### Health Indicators
- **pg_up**: Database connectivity (should be 1)
- **Active Connections**: Current database connections
- **Buffer Hit Ratio**: Should be > 95% for good performance
- **Connection Utilization**: Should be < 80% of max_connections

### Performance Indicators
- **Transaction Rate**: Transactions per second
- **Query Response Time**: Average query execution time
- **I/O Operations**: Disk read/write activity
- **Lock Waits**: Database lock contention

### Capacity Indicators
- **Database Size**: Total database size in MB/GB
- **Table Growth**: Individual table size trends
- **Index Usage**: Index scan vs sequential scan ratios
- **Temporary Files**: Temporary file creation rate

## Troubleshooting

### If Database Metrics Are Not Showing
1. Check PostgreSQL exporter status:
   ```bash
   docker compose -f services/orchestration/docker-compose.apm.yml ps postgres-exporter
   ```

2. Verify metrics endpoint:
   ```bash
   curl http://localhost:9187/metrics | grep pg_up
   ```

3. Check OpenTelemetry collector logs:
   ```bash
   docker compose -f services/orchestration/docker-compose.apm.yml logs otel-collector-skywalking
   ```

### If SkyWalking UI Is Not Accessible
1. Check SkyWalking services:
   ```bash
   docker compose -f services/orchestration/docker-compose.apm.yml ps skywalking-ui skywalking-oap
   ```

2. Verify Traefik routing:
   ```bash
   curl -I http://skywalking.beauty-crm.localhost/
   ```

## Advanced Features

### Custom Queries
The PostgreSQL exporter includes custom queries for:
- Slow query analysis
- Lock monitoring
- Replication lag (if applicable)
- Table and index statistics

### Alerting
You can set up alerts in SkyWalking for:
- High connection usage
- Low buffer hit ratio
- Long-running queries
- Database size growth

### Historical Analysis
SkyWalking stores metrics for configurable retention periods:
- Real-time: Last 30 minutes
- Hourly aggregation: Last 7 days
- Daily aggregation: Last 30 days

## Database Schema Information

**Monitored Databases:**
- beauty_crm_appointment 
- beauty_crm_planner 
- beauty_crm_salon
- beauty_crm_staff

**Key Tables Being Monitored:**
- All user tables in the public schema
- System tables for performance metrics
- Index usage statistics

## Security Notes

- Database credentials are configured in Docker Compose environment variables
- Metrics are exposed only within the Docker network
- External access is through Traefik proxy with proper routing rules

## Support

For issues with database monitoring:
1. Check the logs of all monitoring services
2. Verify PostgreSQL extensions are enabled (pg_stat_statements)
3. Ensure network connectivity between services
4. Review SkyWalking configuration files for any errors


## Tasks

### Extracted Tasks

- [ ] **URL**: http://skywalking.beauty-crm.localhost/ - M1
- [ ] **Description**: Main SkyWalking dashboard with database monitoring capabilities - M2
- [ ] **URL**: http://postgres-metrics.beauty-crm.localhost/metrics - M3
- [ ] **Description**: Raw PostgreSQL metrics in Prometheus format - M4
- [ ] **Direct Port**: http://localhost:9187/metrics - M5
- [ ] **URL**: http://otel-skywalking.beauty-crm.localhost/ - M6
- [ ] **Description**: Metrics collection and processing endpoint - M7
- [ ] Database connection count and utilization - M8
- [ ] Database size and growth trends - M9
- [ ] Buffer cache hit ratio - M10
- [ ] Transaction rates (commits/rollbacks) - M11
- [ ] Query performance metrics - M12
- [ ] Query execution rates - M13
- [ ] I/O operations (blocks read/written) - M14
- [ ] Data modification rates (inserts/updates/deletes) - M15
- [ ] Error rates and deadlocks - M16
- [ ] Temporary file usage - M17
- [ ] Table sizes and growth - M18
- [ ] Index sizes and usage - M19
- [ ] Sequential vs index scan ratios - M20
- [ ] Vacuum and analyze activity - M21
- [ ] Row modification statistics - M22
- [ ] Database uptime - M23
- [ ] Connection utilization percentage - M24
- [ ] Background writer activity - M25
- [ ] Checkpoint operations - M26
- [ ] WAL (Write-Ahead Log) statistics - M27
- [ ] **pg_up**: Database connectivity (should be 1) - M28
- [ ] **Active Connections**: Current database connections - M29
- [ ] **Buffer Hit Ratio**: Should be > 95% for good performance - M30
- [ ] **Connection Utilization**: Should be < 80% of max_connections - M31
- [ ] **Transaction Rate**: Transactions per second - M32
- [ ] **Query Response Time**: Average query execution time - M33
- [ ] **I/O Operations**: Disk read/write activity - M34
- [ ] **Lock Waits**: Database lock contention - M35
- [ ] **Database Size**: Total database size in MB/GB - M36
- [ ] **Table Growth**: Individual table size trends - M37
- [ ] **Index Usage**: Index scan vs sequential scan ratios - M38
- [ ] **Temporary Files**: Temporary file creation rate - M39
- [ ] Slow query analysis - M40
- [ ] Lock monitoring - M41
- [ ] Replication lag (if applicable) - M42
- [ ] Table and index statistics - M43
- [ ] High connection usage - M44
- [ ] Low buffer hit ratio - M45
- [ ] Long-running queries - M46
- [ ] Database size growth - M47
- [ ] Real-time: Last 30 minutes - M48
- [ ] Hourly aggregation: Last 7 days - M49
- [ ] Daily aggregation: Last 30 days - M50
- [ ] beauty_crm_appointment - M51
- [ ] beauty_crm_planner - M52
- [ ] beauty_crm_salon - M53
- [ ] beauty_crm_staff - M54
- [ ] All user tables in the public schema - M55
- [ ] System tables for performance metrics - M56
- [ ] Index usage statistics - M57
- [ ] Database credentials are configured in Docker Compose environment variables - M58
- [ ] Metrics are exposed only within the Docker network - M59
- [ ] External access is through Traefik proxy with proper routing rules - M60

### Frontend Tasks

- [ ] Implement UI components - M1
- [ ] Add form validation - M2
- [ ] Add error handling - M3
- [ ] Add loading states - M4
- [ ] Add unit tests - M5
- [ ] Add accessibility features - M6

