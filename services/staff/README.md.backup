# Staff Management Service

A comprehensive staff catalog management service for beauty salon CRM, built with Domain-Driven Design (DDD) architecture.

## 🏗️ Architecture

### DDD Layers
```
src/
├── domain/                    # Business Logic Layer
│   ├── models/               # Domain entities
│   ├── repositories/         # Domain contracts
│   └── events/              # Domain events
├── application/              # Application Layer
│   └── services/            # Use cases & orchestration
├── infrastructure/          # Infrastructure Layer
│   ├── repositories/        # Database implementations
│   └── clients/            # External integrations
└── controllers/             # Presentation Layer
```

### Key Features
- ✅ **REST API** for management UI operations
- ✅ **NATS Events** for real-time integration
- ✅ **Rich Domain Model** with business logic
- ✅ **Event-driven Architecture** for loose coupling
- ✅ **Multi-tenant Support** via salon ID
- ✅ **Comprehensive Validation** with Zod schemas

## 🚀 Quick Start

### Development Mode
```bash
# Start development environment
./scripts/start-dev.sh

# Or manually:
cd staff-management-backend
bun install
bun run db:generate
bun run dev
```

### Production Mode
```bash
# Start production environment
./scripts/start-prod.sh

# Or manually:
docker compose -f docker-compose.app.yml up -d --build
```

## 📡 API Endpoints

### Staff Management
```http
GET    /api/staff?salonId=salon1           # List all staff
GET    /api/staff/active?salonId=salon1    # Active staff only
GET    /api/staff/search?query=facial      # Search staff
GET    /api/staff/:id                      # Get specific staff
POST   /api/staff                          # Create staff
PUT    /api/staff/:id                      # Update staff
PATCH  /api/staff/:id/activate             # Activate staff
PATCH  /api/staff/:id/deactivate           # Deactivate staff
DELETE /api/staff/:id                      # Delete staff
POST   /api/staff/bulk/toggle-active       # Bulk activate/deactivate
```

### Health & Info
```http
GET    /health                                  # Health check
GET    /api                                     # API information
```

## 🔗 Service URLs

### Direct Access
- **Service**: http://localhost:4000
- **Health**: http://localhost:4000/health
- **API**: http://localhost:4000/api

### Via Traefik (Production)
- **Service**: http://staff-management.localhost
- **Staff**: http://staff.localhost
- **API**: http://staff-management.localhost/api

## 📊 Database

### Connection
```
Database: beauty_crm_staff
URL: postgresql://beauty_crm:beauty_crm_password@localhost:5432/beauty_crm_staff
```

### Schema
- **staff**: Core staff catalog
- **staff_media**: Images, videos, icons
- **staff_pricing**: Complex pricing rules
- **staff_tags**: Flexible labeling
- **staff_availability**: Time-based constraints
- **staff_restrictions**: Business rules
- **staff_add_ons**: Optional extras
- **staff_analytics**: Performance metrics

## 📨 NATS Integration

### Published Events
```typescript
// Domain events for internal processing
'staff.events' → Full domain events

// Simplified events for external services
'staff.updated' → {
  salonId: string,
  staff: Array<{id, name, duration, price}>,
  eventType: 'created' | 'updated' | 'deleted',
  timestamp: string
}
```

### Event Consumers
- **Appointment Service**: Updates staff cache
- **Analytics Service**: Tracks metrics
- **Management UI**: Real-time updates

## 🐳 Docker Configuration

### Networks
- `network_backend`: Service communication
- `network_database`: Database access
- `beauty_crm_traefik-public`: Traefik routing

### Dependencies
- **PostgreSQL**: Database storage
- **NATS**: Event messaging
- **Traefik**: Reverse proxy

### Labels
```yaml
app.service: staff-management
app.type: management
app.environment: production
```

## 🔧 Development

### Prerequisites
- Bun runtime
- Docker & Docker Compose
- PostgreSQL (via Docker)
- NATS (via Docker)

### Environment Variables
```bash
DATABASE_URL=postgresql://beauty_crm:beauty_crm_password@localhost:5432/beauty_crm_staff
NATS_URL=nats://localhost:4222
PORT=4000
HOST=0.0.0.0
SERVICE_NAME=staff-management-backend
```

### Scripts
```bash
bun run dev          # Development server
bun run start        # Production server
bun run build        # Build application
bun run db:generate  # Generate Prisma client
bun run db:push      # Push schema changes
bun run db:migrate   # Run migrations
bun run db:studio    # Open Prisma Studio
```

## 🧪 Testing

### API Testing
```bash
# Health check
curl http://localhost:4000/health

# List staff
curl "http://localhost:4000/api/staff?salonId=salon1"

# Create staff
curl -X POST http://localhost:4000/api/staff \
  -H "Content-Type: application/json" \
  -d '{
    "salonId": "salon1",
    "name": "Deep Cleansing Facial",
    "description": "Comprehensive facial staff",
    "duration": 60,
    "basePrice": 85.00
  }'
```

### Via Traefik
```bash
# Health check
curl http://staff-management.localhost/health

# List staff
curl "http://staff-management.localhost/api/staff?salonId=salon1"
```

## 🔄 Integration

### With Appointment Service
1. **NATS Subscription**: Appointment service subscribes to `staff.updated`
2. **Local Cache**: Maintains staff cache for fast booking
3. **Validation**: Validates staff IDs during appointment creation

### With Management UI
1. **REST API**: Direct API calls for CRUD operations
2. **Real-time Updates**: WebSocket or polling for live updates
3. **Bulk Operations**: Efficient management of multiple staff

### With Planner UI
1. **Staff Display**: Shows available staff from cache
2. **Real-time Updates**: Reflects staff changes immediately
3. **Booking Integration**: Uses staff info for appointment creation

## 📈 Monitoring

### Health Checks
- **Application**: `/health` endpoint
- **Database**: Connection validation
- **NATS**: Connection status

### Metrics
- **Staff Operations**: CRUD statistics
- **Event Publishing**: NATS message counts
- **API Performance**: Response times
- **Cache Hit Rates**: Staff lookup efficiency

## 🚨 Troubleshooting

### Common Issues
1. **Database Connection**: Check PostgreSQL container status
2. **NATS Connection**: Verify NATS container and network
3. **Traefik Routing**: Check service labels and network configuration
4. **Port Conflicts**: Ensure port 4000 is available

### Logs
```bash
# Application logs
docker compose -f docker-compose.app.yml logs -f staff-management-backend

# Database logs
docker compose -f ../appointment/docker-compose.databases.yml logs -f postgres

# NATS logs
docker compose -f ../appointment/docker-compose.databases.yml logs -f nats
```

## 🔮 Future Enhancements

### Planned Features
- **Categories**: Hierarchical Staff organization
- **Media Management**: Image and video uploads
- **Complex Pricing**: Time-based and seasonal pricing
- **Prerequisites**: Staff dependencies
- **Analytics**: Advanced reporting and insights
- **Multi-language**: Internationalization support

### Scalability
- **Horizontal Scaling**: Multiple service instances
- **Database Sharding**: Salon-based partitioning
- **Caching Layer**: Redis for frequently accessed data
- **CDN Integration**: Media asset delivery
