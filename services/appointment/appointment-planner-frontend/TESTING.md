# Testing Guide for Appointment Planner Frontend

This document explains how to run the tests for the Appointment Planner Frontend and troubleshoot common issues.

## Available Test Commands

The following test commands are available:

### Unit Tests
- `npm test` - Run all unit tests with Vitest
- `npm run test:watch` - Run unit tests in watch mode
- `npm run test:coverage` - Run tests with coverage report

### E2E Tests (Cucumber)

#### Mocked Tests (Recommended for Development)
- `npm run test:cucumber:appointment:mocked` - Run mocked appointment tests without a browser

#### Real E2E Tests
- `npm run test:e2e:with-server` - Start dev server and run E2E tests
- `npm run test:e2e:nogui` - Start dev server and run E2E tests with headless browser
- `npm run test:cucumber:appointment:e2e` - Run only E2E tests against a running server

#### Expired Date Tests
- `bun --bun cucumber-js --config cucumber.expired-date.js` - Run expired date tests using dedicated config
- `CUCUMBER_TAGS="@expiredDate" bun --bun cucumber-js` - Run expired date tests using tags
- `npm run test:cucumber:expired` - Run expired date tests using npm script

## Common Issues and Solutions

### 1. localStorage Access Security Error

**Issue:** When running the tests, you get a security error about localStorage access.

```
SecurityError: Failed to read the 'localStorage' property from 'Window': Access is denied for this document.
```

**Solution:** 
- Use the mocked tests instead of the E2E tests for quick development: `npm run test:cucumber:appointment:mocked`
- Or use the nogui config which disables security restrictions: `npm run test:e2e:nogui`

### 2. Port Already in Use

**Issue:** When trying to start the server, you get an error about the port being in use.

```
Error: Port 5016 is already in use
```

**Solution:**
```bash
# Check what's using the port
lsof -i :5016 | grep LISTEN

# Kill the process
kill -9 <PID>
```

### 3. Timeout Issues with Time Selection

**Issue:** Tests fail when selecting appointment times with error:

```
Error: function timed out, ensure the promise resolves within 5000 milliseconds
```

**Solutions:**
- Increase the step timeout in cucumber.e2e.js (current timeout: 120000ms)
- Check if time slot elements are correctly rendered with data-testid="time-slot-XX:XX" format
- Use the retry mechanism implemented in the appointment.steps.ts file

### 4. Missing World File for Expired Date Tests

**Issue:** When running expired date tests, you encounter errors about missing world.ts:

```
error: ENOENT reading "src/tests/features/expired-date/step_definitions/utils/world.ts"
```

**Solution:**
```bash
# Create the necessary directory and file structure
mkdir -p src/tests/features/expired-date/step_definitions/utils
echo "export { CustomWorld } from '../../../world';" > src/tests/features/expired-date/step_definitions/utils/world.ts
```

## Testing Strategies

1. **Development Testing:** Use mocked tests (`test:cucumber:appointment:mocked`) for quick feedback during development
2. **CI Testing:** Use E2E tests for integration testing in CI environments
3. **Visual Testing:** Use non-headless mode to see what's happening during tests
4. **Date Handling Testing:** Use expired date tests to verify correct handling of date-related edge cases

## Debugging Tips

- Check the screenshots saved by the E2E tests in the project root when failures occur
- Look for "time-slots-debug-X.png" files which are saved during retry attempts
- If localStorage errors persist, consider modifying the test code to avoid using localStorage
- For date-related tests, check the date mocking implementation in expired-date.steps.ts

## Docker Testing (Not Currently Working)

We have Docker configuration for testing, but it's currently not functioning correctly:

- `npm run test:docker:build` - Build Docker testing images
- `npm run test:docker:run` - Run tests in Docker containers
- `npm run test:docker:clean` - Clean up Docker resources

## Further Documentation

For more detailed information on E2E testing, see the [README-E2E-TESTS.md](./README-E2E-TESTS.md) file. 

## Tasks

### Extracted Tasks

- [ ] `npm test` - Run all unit tests with Vitest - M1
- [ ] `npm run test:watch` - Run unit tests in watch mode - M2
- [ ] `npm run test:coverage` - Run tests with coverage report - M3
- [ ] `npm run test:cucumber:appointment:mocked` - Run mocked appointment tests without a browser - M4
- [ ] `npm run test:e2e:with-server` - Start dev server and run E2E tests - M5
- [ ] `npm run test:e2e:nogui` - Start dev server and run E2E tests with headless browser - M6
- [ ] `npm run test:cucumber:appointment:e2e` - Run only E2E tests against a running server - M7
- [ ] `bun --bun cucumber-js --config cucumber.expired-date.js` - Run expired date tests using dedicated config - M8
- [ ] `CUCUMBER_TAGS="@expiredDate" bun --bun cucumber-js` - Run expired date tests using tags - M9
- [ ] `npm run test:cucumber:expired` - Run expired date tests using npm script - M10
- [ ] Use the mocked tests instead of the E2E tests for quick development: `npm run test:cucumber:appointment:mocked` - M11
- [ ] Or use the nogui config which disables security restrictions: `npm run test:e2e:nogui` - M12
- [ ] Increase the step timeout in cucumber.e2e.js (current timeout: 120000ms) - M13
- [ ] Check if time slot elements are correctly rendered with data-testid="time-slot-XX:XX" format - M14
- [ ] Use the retry mechanism implemented in the appointment.steps.ts file - M15
- [ ] Check the screenshots saved by the E2E tests in the project root when failures occur - M16
- [ ] Look for "time-slots-debug-X.png" files which are saved during retry attempts - M17
- [ ] If localStorage errors persist, consider modifying the test code to avoid using localStorage - M18
- [ ] For date-related tests, check the date mocking implementation in expired-date.steps.ts - M19
- [ ] `npm run test:docker:build` - Build Docker testing images - M20
- [ ] `npm run test:docker:run` - Run tests in Docker containers - M21
- [ ] `npm run test:docker:clean` - Clean up Docker resources - M22

### Frontend Tasks

- [ ] Implement UI components - M1
- [ ] Add form validation - M2
- [ ] Add error handling - M3
- [ ] Add loading states - M4
- [ ] Add unit tests - M5
- [ ] Add accessibility features - M6

