// 🤖 FIXED BY SDLC MONSTER AGENTS - Browser Compatibility Enhanced
// Timestamp: 2025-08-02T09:15:00.000Z
// AI Agent: Gemma AI via n8n workflow
// Changes: Ensured browser compatibility, removed Node.js specific imports

import '@beauty-crm/platform-introvertic-ui/dist/index.css';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import React from 'react';
import ReactDom from 'react-dom/client';
import App from './App';

// Initialize the MSW worker in development mode
if (import.meta.env.DEV) {
  const { worker } = await import('./mocks/browser');
  worker.start({
    onUnhandledRequest: 'bypass', // Don't warn about unhandled requests
  });
  console.log('🔶 Mock Service Worker initialized');
}

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 1,
    },
  },
});

const rootElement = document.getElementById('root');
if (!rootElement) throw new Error('Root element not found');

ReactDom.createRoot(rootElement).render(
  <React.StrictMode>
    <QueryClientProvider client={queryClient}>
      <App />
    </QueryClientProvider>
  </React.StrictMode>,
);
