# Salon Appointment E2E Testing Guide

This document explains the different types of tests available for the Salon Appointment application and how to run them.

## Types of Tests

### Mocked Tests

The original tests (with `@mocked` tag) use in-memory JavaScript objects to simulate a appointment flow without any real browser interaction. These tests:

- Run fast
- Don't require any browser or server
- Don't actually test the real UI
- Always pass as they simply simulate actions
- Located in `src/tests/features/appointment/step_definitions/appointment-mocked.steps.ts`

### Real E2E Tests

The new E2E tests (with `@e2e` tag) use <PERSON><PERSON> to launch an actual browser and interact with the UI. These tests:

- Launch a real browser (Chrome by default)
- Require the frontend server to be running
- Test actual UI elements and interactions
- Validate the real appointment flow
- Located in `src/tests/features/appointment/step_definitions/appointment.steps.ts`

### Expired Date Tests

The expired date tests (with `@expiredDate` tag) validate that the application correctly handles date expiration:

- Test that users cannot book appointments on expired dates
- Verify proper error messages are displayed when attempting to use expired dates
- Validate that dates in the past are correctly disabled in the calendar
- Located in `src/tests/features/expired-date/step_definitions/expired-date.steps.ts`

## Running Tests

### Running Mocked Tests

```bash
npm run test:cucumber:appointment
```

or

```bash
npm run test:cucumber:appointment:new
```

### Running Real E2E Tests

Before running the E2E tests:

1. Install Playwright browsers:
   ```bash
   npx playwright install
   ```

2. Run the tests with the server (recommended):
   ```bash
   npm run test:e2e:with-server
   ```

This will:
- Start the development server on port 5016
- Wait 10 seconds for the server to initialize
- Run the E2E tests against the running server

### Running Expired Date Tests

You can run the expired date tests using one of the following commands:

1. Using the dedicated configuration file:
   ```bash
   bun --bun cucumber-js --config cucumber.expired-date.js
   ```

2. Using tags to filter the tests:
   ```bash
   CUCUMBER_TAGS="@expiredDate" bun --bun cucumber-js
   ```

3. Using the npm script (if available):
   ```bash
   npm run test:cucumber:expired
   ```

### Debugging E2E Tests

To debug E2E tests:

1. Start the development server separately:
   ```bash
   npm run dev:test
   ```

2. Run the tests in non-headless mode:
   - Modify the relevant step definitions file to set `headless: false` in the `chromium.launch()` options
   - Run the tests using one of the commands above

## Understanding the Tests

The E2E tests create a complete test environment by:

1. Starting the application server
2. Launching a real browser
3. Mocking API responses to provide predictable test data
4. Automating UI interactions exactly as a user would
5. Verifying application behavior and UI state

## Troubleshooting

If tests are failing:

1. Check that selectors in the step definition files match the actual UI elements
2. Ensure the API mocks reflect the expected data format
3. Verify the server is running and accessible
4. Look for timeout issues in asynchronous operations
5. Check browser console errors for additional debugging information
6. For expired date tests, ensure the date mocking is working correctly
7. If you see errors about missing utils/world.ts, ensure the path structure is correct:
   ```bash
   mkdir -p src/tests/features/expired-date/step_definitions/utils
   echo "export { CustomWorld } from '../../../world';" > src/tests/features/expired-date/step_definitions/utils/world.ts
   ``` 

## Tasks

### Extracted Tasks

- [ ] Don't require any browser or server - M1
- [ ] Don't actually test the real UI - M2
- [ ] Always pass as they simply simulate actions - M3
- [ ] Located in `src/tests/features/appointment/step_definitions/appointment-mocked.steps.ts` - M4
- [ ] Launch a real browser (Chrome by default) - M5
- [ ] Require the frontend server to be running - M6
- [ ] Test actual UI elements and interactions - M7
- [ ] Validate the real appointment flow - M8
- [ ] Located in `src/tests/features/appointment/step_definitions/appointment.steps.ts` - M9
- [ ] Test that users cannot book appointments on expired dates - M10
- [ ] Verify proper error messages are displayed when attempting to use expired dates - M11
- [ ] Validate that dates in the past are correctly disabled in the calendar - M12
- [ ] Located in `src/tests/features/expired-date/step_definitions/expired-date.steps.ts` - M13
- [ ] Start the development server on port 5016 - M14
- [ ] Wait 10 seconds for the server to initialize - M15
- [ ] Run the E2E tests against the running server - M16
- [ ] Modify the relevant step definitions file to set `headless: false` in the `chromium.launch()` options - M17
- [ ] Run the tests using one of the commands above - M18

### Frontend Tasks

- [ ] Implement UI components - M1
- [ ] Add form validation - M2
- [ ] Add error handling - M3
- [ ] Add loading states - M4
- [ ] Add unit tests - M5
- [ ] Add accessibility features - M6

