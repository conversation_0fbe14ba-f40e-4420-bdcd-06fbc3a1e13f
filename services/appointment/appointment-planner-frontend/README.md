# Appointment Planner Frontend

A customizable salon appointment widget that integrates with any website.

## Features

- Clean, responsive appointment interface
- Date and time slot selection
- Service selection
- Customer information collection
- Seamless integration with any website

## Getting Started

1. Install dependencies:
   ```
   npm install
   ```

2. Start the development server:
   ```
   npm run dev
   ```

3. Build for production:
   ```
   npm run build
   ```

## Environment Configuration

Create a `.env` file with the following:
```
VITE_API_URL=http://localhost:3000
```

## Integration

To integrate this appointment widget into any website:

1. Build the application
2. Deploy the static files to your hosting provider
3. Add the following script to any page where you want the appointment widget:

```html
<div id="salon-appointment"></div>
<script src="https://your-deployment-url/appointment-widget.js"></script>
<script>
  initSalonAppointment({
    elementId: 'salon-appointment',
    salonId: 'your-salon-id',
    salonName: 'Your Salon Name',
    primaryColor: '#4A90E2'
  });
</script>
```

## Customization

The appointment widget can be customized with the following options:

- `salonId`: Your salon's unique identifier
- `salonName`: Your salon's name
- `logo`: URL to your salon's logo
- `primaryColor`: Primary color for buttons and highlights
- `secondaryColor`: Secondary color for accents
- `services`: Array of service objects (id, name, price, duration)

## Technology Stack

- React
- TypeScript
- Vite
- TanStack Query (React Query)
- TailwindCSS
- date-fns 

## Tasks

### Extracted Tasks

- [ ] Clean, responsive appointment interface - M1
- [ ] Date and time slot selection - M2
- [ ] Service selection - M3
- [ ] Customer information collection - M4
- [ ] Seamless integration with any website - M5
- [ ] `salonId`: Your salon's unique identifier - M6
- [ ] `salonName`: Your salon's name - M7
- [ ] `logo`: URL to your salon's logo - M8
- [ ] `primaryColor`: Primary color for buttons and highlights - M9
- [ ] `secondaryColor`: Secondary color for accents - M10
- [ ] `services`: Array of service objects (id, name, price, duration) - M11
- [ ] TanStack Query (React Query) - M12
- [ ] TailwindCSS - M13

### Frontend Tasks

- [ ] Implement UI components - M1
- [ ] Add form validation - M2
- [ ] Add error handling - M3
- [ ] Add loading states - M4
- [ ] Add unit tests - M5
- [ ] Add accessibility features - M6

