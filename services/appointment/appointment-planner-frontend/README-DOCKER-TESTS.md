# Docker-based E2E Testing

This guide explains how to run the E2E tests using Docker, which ensures that both the frontend and backend services are properly set up and isolated.

## Prerequisites

- <PERSON><PERSON> and Docker Compose installed on your system
- Git repository cloned locally

## Running Tests with Docker

### 1. Build the Docker images

```bash
npm run test:docker:build
```

This command builds the Docker images for both the frontend service and the test runner.

### 2. Run the tests

```bash
npm run test:docker:run
```

This command:
1. Starts the frontend service container
2. Waits for the frontend to be healthy (accessible via HTTP)
3. Runs the E2E tests against the running frontend

### 3. Clean up after tests

```bash
npm run test:docker:clean
```

This command stops all containers and removes associated volumes.

## Test Results

Test results and screenshots will be available in the `test-results` directory after running the tests.

## Environment Variables

The following environment variables can be configured:

- `FRONTEND_URL`: URL of the frontend service (default: `http://frontend:5016` in Docker)
- `BACKEND_URL`: URL of the backend service (default: `http://mock-backend:3000` in Docker)
- `CUCUMBER_TAGS`: Tags to filter which Cucumber scenarios to run (default: `@e2e`)

## Debugging Failed Tests

If tests fail, you can examine:

1. The test output in the terminal
2. Screenshots captured during test failures in the `test-results` directory
3. Browser logs which are also saved to the `test-results` directory

## Running Tests Locally (without Docker)

If you prefer to run tests locally, you can use:

```bash
# Start the frontend server and run the E2E tests
npm run test:e2e:with-server

# Or start the server separately and run just the tests
npm run dev:test
npm run test:cucumber:appointment:e2e
```

## Troubleshooting

- **Tests time out**: Increase the timeout values in `cucumber.e2e.js`
- **Network errors**: Check if the frontend service is running and accessible
- **Missing elements**: Inspect the screenshots to see what's being rendered 

## Tasks

### Extracted Tasks

- [ ] Docker and Docker Compose installed on your system - M1
- [ ] Git repository cloned locally - M2
- [ ] `FRONTEND_URL`: URL of the frontend service (default: `http://frontend:5016` in Docker) - M3
- [ ] `BACKEND_URL`: URL of the backend service (default: `http://mock-backend:3000` in Docker) - M4
- [ ] `CUCUMBER_TAGS`: Tags to filter which Cucumber scenarios to run (default: `@e2e`) - M5
- [ ] **Tests time out**: Increase the timeout values in `cucumber.e2e.js` - M6
- [ ] **Network errors**: Check if the frontend service is running and accessible - M7
- [ ] **Missing elements**: Inspect the screenshots to see what's being rendered - M8

### Frontend Tasks

- [ ] Implement UI components - M1
- [ ] Add form validation - M2
- [ ] Add error handling - M3
- [ ] Add loading states - M4
- [ ] Add unit tests - M5
- [ ] Add accessibility features - M6

