# Appointment Planner Frontend

A customizable salon appointment widget that integrates with any website.

## Features

- Clean, responsive appointment interface
- Date and time slot selection
- Service selection
- Customer information collection
- Seamless integration with any website

## Getting Started

1. Install dependencies:
   ```
   npm install
   ```

2. Start the development server:
   ```
   npm run dev
   ```

3. Build for production:
   ```
   npm run build
   ```

## Environment Configuration

Create a `.env` file with the following:
```
VITE_API_URL=http://localhost:3000
```

## Integration

To integrate this appointment widget into any website:

1. Build the application
2. Deploy the static files to your hosting provider
3. Add the following script to any page where you want the appointment widget:

```html
<div id="salon-appointment"></div>
<script src="https://your-deployment-url/appointment-widget.js"></script>
<script>
  initSalonAppointment({
    elementId: 'salon-appointment',
    salonId: 'your-salon-id',
    salonName: 'Your Salon Name',
    primaryColor: '#4A90E2'
  });
</script>
```

## Customization

The appointment widget can be customized with the following options:

- `salonId`: Your salon's unique identifier
- `salonName`: Your salon's name
- `logo`: URL to your salon's logo
- `primaryColor`: Primary color for buttons and highlights
- `secondaryColor`: Secondary color for accents
- `services`: Array of service objects (id, name, price, duration)

## Technology Stack

- React
- TypeScript
- Vite
- TanStack Query (React Query)
- TailwindCSS
- date-fns 