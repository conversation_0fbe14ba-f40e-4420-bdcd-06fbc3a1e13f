# Introvertic UI Integration for Sprint 1
*Prepared by <PERSON>, Tech Lead*

## Overview

For our Sprint 1 Mobile Experience Overhaul, we'll leverage our existing Introvertic UI tool to accelerate development and ensure consistency across our mobile components. This document outlines how to integrate Introvertic UI with our implementation plan.

## What is Introvertic UI?

Introvertic UI is our internal component library built on top of Mantine, providing:

1. Pre-built responsive components optimized for both desktop and mobile
2. Standardized design tokens (colors, spacing, typography)
3. Mobile-specific interaction patterns
4. Accessibility-compliant components
5. Performance-optimized rendering

## Integration Strategy

### 1. Setup and Configuration

Add Introvertic UI to the project:

```bash
# Install from our private registry
bun add @beauty-crm/introvertic-ui

# Add peer dependencies if not already installed
bun add @mantine/hooks @mantine/core @emotion/react
```

Configure the theme in our application:

```typescript
// src/theme/index.ts
import { createTheme } from '@beauty-crm/introvertic-ui';

export const theme = createTheme({
  // Override with our specific brand colors if needed
  colors: {
    primary: ['#F0F8FF', '#CCE4FF', '#99C1FF', '#66A3FF', '#3385FF', '#0066FF', '#0052CC', '#003D99', '#002966', '#001433'],
  },
  // Use the standardized breakpoints
  breakpoints: {
    xs: '320px',
    sm: '576px',
    md: '768px',
    lg: '992px',
    xl: '1200px',
  },
});
```

### 2. Mobile-Specific Components

Introvertic UI provides several mobile-optimized components we can use directly:

#### Mobile Navigation

```typescript
// src/components/navigation/MobileNavigation.tsx
import { MobileNavBar, MobileNavItem } from '@beauty-crm/introvertic-ui';
import { IconCalendar, IconPlus, IconList, IconSettings } from '@tabler/icons-react';

export const MobileNavigation: React.FC = () => {
  return (
    <MobileNavBar>
      <MobileNavItem icon={<IconCalendar size={24} />} label="Calendar" href="/calendar" />
      <MobileNavItem icon={<IconPlus size={24} />} label="New" onClick={() => openAppointmentModal()} />
      <MobileNavItem icon={<IconList size={24} />} label="List" href="/list" />
      <MobileNavItem icon={<IconSettings size={24} />} label="Settings" href="/settings" />
    </MobileNavBar>
  );
};
```

#### Touch-Optimized Calendar

```typescript
// src/components/calendar/MobileCalendarView.tsx
import { MobileCalendar, useSwipeNavigation } from '@beauty-crm/introvertic-ui';
import { addDays } from 'date-fns';

export const MobileCalendarView: React.FC = () => {
  const { selectedDate, setSelectedDate, appointments } = useAppointmentContext();
  
  // Use the built-in swipe navigation hook
  const swipeHandlers = useSwipeNavigation({
    onSwipeLeft: () => setSelectedDate(addDays(selectedDate, 1)),
    onSwipeRight: () => setSelectedDate(addDays(selectedDate, -1)),
  });
  
  return (
    <MobileCalendar
      date={selectedDate}
      events={appointments.map(appt => ({
        id: appt.id,
        title: appt.customerName,
        start: new Date(appt.startTime),
        end: new Date(appt.endTime),
        color: getAppointmentColor(appt.type),
      }))}
      onDateChange={setSelectedDate}
      onEventClick={(eventId) => openAppointmentDetails(eventId)}
      {...swipeHandlers}
    />
  );
};
```

#### Mobile-Optimized Forms

```typescript
// src/components/appointments/MobileAppointmentForm.tsx
import { 
  MobileForm, 
  MobileFormField, 
  MobileDateTimePicker,
  MobileSelect
} from '@beauty-crm/introvertic-ui';

export const MobileAppointmentForm: React.FC<MobileAppointmentFormProps> = ({
  initialValues,
  onSubmit,
  onCancel,
}) => {
  return (
    <MobileForm
      initialValues={initialValues}
      onSubmit={onSubmit}
      submitLabel="Save Appointment"
      cancelLabel="Cancel"
      onCancel={onCancel}
    >
      <MobileFormField name="customerName" label="Customer Name" required />
      <MobileFormField name="treatmentName" label="Service" required />
      <MobileDateTimePicker name="startTime" label="Start Time" required />
      <MobileDateTimePicker name="endTime" label="End Time" required />
      <MobileSelect
        name="status"
        label="Status"
        data={[
          { value: 'confirmed', label: 'Confirmed' },
          { value: 'pending', label: 'Pending' },
          { value: 'cancelled', label: 'Cancelled' },
        ]}
      />
      <MobileFormField name="notes" label="Notes" multiline rows={3} />
    </MobileForm>
  );
};
```

### 3. Responsive Utilities

Introvertic UI provides responsive utilities we can use throughout our application:

```typescript
// src/hooks/useResponsive.ts
import { useResponsive } from '@beauty-crm/introvertic-ui';

// In components
const MyComponent: React.FC = () => {
  const { isMobile, isTablet, isDesktop } = useResponsive();
  
  return (
    <div>
      {isMobile && <MobileView />}
      {isTablet && <TabletView />}
      {isDesktop && <DesktopView />}
    </div>
  );
};
```

### 4. Performance Optimizations

Introvertic UI includes performance-optimized components:

```typescript
// src/components/appointments/AppointmentList.tsx
import { VirtualizedList } from '@beauty-crm/introvertic-ui';

export const AppointmentList: React.FC<AppointmentListProps> = ({ appointments }) => {
  return (
    <VirtualizedList
      data={appointments}
      height={500}
      itemSize={70}
      renderItem={(appointment) => (
        <AppointmentCard appointment={appointment} />
      )}
    />
  );
};
```

## Mapping Sprint 1 Tasks to Introvertic UI

| Sprint 1 Task | Introvertic UI Components/Utilities |
|---------------|-------------------------------------|
| Implement Consistent Breakpoint System | `createTheme`, `useResponsive` |
| Develop Mobile Navigation Component | `MobileNavBar`, `MobileNavItem` |
| Create Mobile-First Layout Components | `Container`, `Grid`, `Stack`, `Responsive` components |
| Develop Mobile Calendar View | `MobileCalendar`, `useSwipeNavigation` |
| Optimize Appointment Viewing for Mobile | `MobileCard`, `MobileModal`, `ActionMenu` |
| Enhance Visual Hierarchy for Mobile | Typography components, `StatusBadge`, theme tokens |
| Implement Performance Optimizations | `VirtualizedList`, `LazyLoad`, `ImageOptimizer` |

## Accessibility Benefits

Introvertic UI components are built with accessibility in mind:

1. All components have proper ARIA attributes
2. Focus management is handled automatically
3. Color contrast meets WCAG AA standards
4. Touch targets are sized appropriately (minimum 44×44px)
5. Keyboard navigation is supported

## Error Handling Integration

Introvertic UI provides error handling components that align with our Sprint 1 goals:

```typescript
// src/components/common/ErrorBoundary.tsx
import { ErrorBoundary, ErrorFallback } from '@beauty-crm/introvertic-ui';

export const AppErrorBoundary: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <ErrorBoundary
      fallback={({ error, resetErrorBoundary }) => (
        <ErrorFallback
          title="Something went wrong"
          message={error.message}
          actionLabel="Try again"
          onAction={resetErrorBoundary}
        />
      )}
    >
      {children}
    </ErrorBoundary>
  );
};
```

## Testing with Introvertic UI

Introvertic UI provides testing utilities that work with our Vitest setup:

```typescript
// src/tests/test-utils.tsx
import { createTestingLibrary } from '@beauty-crm/introvertic-ui/testing';

export const { render, screen, userEvent } = createTestingLibrary({
  // Provide any global providers needed for tests
  wrapper: ({ children }) => (
    <ThemeProvider>
      <AppointmentProvider>
        {children}
      </AppointmentProvider>
    </ThemeProvider>
  ),
});
```

## Implementation Timeline

| Day | Introvertic UI Integration Task |
|-----|--------------------------------|
| 1 | Set up Introvertic UI, configure theme |
| 2 | Implement mobile navigation using Introvertic components |
| 3-4 | Implement mobile calendar view with Introvertic components |
| 5-6 | Implement appointment forms and details views |
| 7-8 | Implement performance optimizations |
| 9-10 | Testing and refinement |

## Conclusion

By leveraging our existing Introvertic UI tool, we can significantly accelerate our Sprint 1 implementation while ensuring consistency, accessibility, and performance. The pre-built mobile components will allow us to focus on business logic and user experience rather than rebuilding UI components from scratch.

The integration will help us achieve our Sprint 1 goals of improving the mobile experience and increasing our UMUX scores, particularly in the areas of mobile responsiveness and visual hierarchy.

---

*Note: This document assumes Introvertic UI is already available in our private registry. If you encounter any issues accessing the package, please contact the Platform Team.* 