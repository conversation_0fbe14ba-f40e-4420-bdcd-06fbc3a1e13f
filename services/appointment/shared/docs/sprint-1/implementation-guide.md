# Sprint 1 Implementation Guide
*Prepared by <PERSON>, Tech Lead*

## Getting Started

This guide provides practical instructions for developers to implement the Mobile Experience Overhaul for Sprint 1. Let's start coding!

## Setup

1. Clone the repository if you haven't already:
   ```bash
   <NAME_EMAIL>:beauty-crm/appointment-management-frontend.git
   cd appointment-management-frontend
   ```

2. Install dependencies:
   ```bash
   bun install
   ```

3. Add Introvertic UI:
   ```bash
   bun add @beauty-crm/introvertic-ui
   ```

4. Create a new branch for your work:
   ```bash
   git checkout -b sprint-1/mobile-experience
   ```

## Project Structure

We'll follow this structure for our mobile implementation:

```
src/
├── components/
│   ├── common/           # Shared components
│   ├── layout/           # Layout components
│   ├── navigation/       # Navigation components
│   ├── calendar/         # Calendar components
│   └── appointments/     # Appointment components
├── hooks/                # Custom hooks
├── context/              # Context providers
├── theme/                # Theme configuration
├── utils/                # Utility functions
└── tests/                # Test utilities
```

## Implementation Tasks

### Task 1: Fix Technical Debt Issues

First, let's fix the duplicate imports in `AppointmentCalendar.tsx`:

```typescript
// Before
import { Box } from '@mantine/core';
import { LoadingOverlay } from '@mantine/core';
import { Tabs } from '@mantine/core';
import { Button, Group } from '@mantine/core';

// After
import { Box, LoadingOverlay, Tabs, Button, Group } from '@mantine/core';
```

Add proper effect cleanup:

```typescript
// Before
useEffect(() => {
  const interval = setInterval(() => {
    fetchAppointments();
  }, 30000);
}, []);

// After
useEffect(() => {
  const interval = setInterval(() => {
    fetchAppointments();
  }, 30000);
  
  return () => {
    clearInterval(interval);
  };
}, [fetchAppointments]);
```

### Task 2: Set Up Theme Configuration

Create a theme configuration file:

```typescript
// src/theme/index.ts
import { createTheme } from '@beauty-crm/introvertic-ui';

export const theme = createTheme({
  colors: {
    primary: ['#F0F8FF', '#CCE4FF', '#99C1FF', '#66A3FF', '#3385FF', '#0066FF', '#0052CC', '#003D99', '#002966', '#001433'],
  },
  breakpoints: {
    xs: '320px',
    sm: '576px',
    md: '768px',
    lg: '992px',
    xl: '1200px',
  },
});
```

Apply the theme in your application:

```typescript
// src/App.tsx
import { MantineProvider } from '@mantine/core';
import { theme } from './theme';

export const App: React.FC = () => {
  return (
    <MantineProvider theme={theme} withGlobalStyles withNormalizeCSS>
      <AppRoutes />
    </MantineProvider>
  );
};
```

### Task 3: Create Responsive Utilities

Create a responsive hook:

```typescript
// src/hooks/useResponsive.ts
import { useMediaQuery } from '@mantine/hooks';
import { theme } from '../theme';

export const useResponsive = () => {
  const isMobile = useMediaQuery(`(max-width: ${theme.breakpoints.md})`);
  const isTablet = useMediaQuery(`(min-width: ${theme.breakpoints.md}) and (max-width: ${theme.breakpoints.lg})`);
  const isDesktop = useMediaQuery(`(min-width: ${theme.breakpoints.lg})`);
  
  return { isMobile, isTablet, isDesktop };
};
```

### Task 4: Implement Mobile Navigation

Create a mobile navigation component:

```typescript
// src/components/navigation/MobileNavigation.tsx
import { MobileNavBar, MobileNavItem } from '@beauty-crm/introvertic-ui';
import { IconCalendar, IconPlus, IconList, IconSettings } from '@tabler/icons-react';
import { useAppointmentContext } from '../../context/AppointmentContext';

export const MobileNavigation: React.FC = () => {
  const { openAppointmentModal } = useAppointmentContext();
  
  return (
    <MobileNavBar>
      <MobileNavItem icon={<IconCalendar size={24} />} label="Calendar" href="/calendar" />
      <MobileNavItem icon={<IconPlus size={24} />} label="New" onClick={() => openAppointmentModal()} />
      <MobileNavItem icon={<IconList size={24} />} label="List" href="/list" />
      <MobileNavItem icon={<IconSettings size={24} />} label="Settings" href="/settings" />
    </MobileNavBar>
  );
};
```

### Task 5: Implement Mobile Calendar View

Create a mobile calendar view:

```typescript
// src/components/calendar/MobileCalendarView.tsx
import { MobileCalendar, useSwipeNavigation } from '@beauty-crm/introvertic-ui';
import { addDays } from 'date-fns';
import { useAppointmentContext } from '../../context/AppointmentContext';
import { getAppointmentColor } from '../../utils/appointments';

export const MobileCalendarView: React.FC = () => {
  const { selectedDate, setSelectedDate, appointments, openAppointmentDetails } = useAppointmentContext();
  
  const swipeHandlers = useSwipeNavigation({
    onSwipeLeft: () => setSelectedDate(addDays(selectedDate, 1)),
    onSwipeRight: () => setSelectedDate(addDays(selectedDate, -1)),
  });
  
  return (
    <MobileCalendar
      date={selectedDate}
      events={appointments.map(appt => ({
        id: appt.id,
        title: appt.customerName,
        start: new Date(appt.startTime),
        end: new Date(appt.endTime),
        color: getAppointmentColor(appt.type),
      }))}
      onDateChange={setSelectedDate}
      onEventClick={(eventId) => openAppointmentDetails(eventId)}
      {...swipeHandlers}
    />
  );
};
```

### Task 6: Create Responsive Layout Components

Create responsive container components:

```typescript
// src/components/layout/ResponsiveContainer.tsx
import { Container, ContainerProps } from '@mantine/core';
import { useResponsive } from '../../hooks/useResponsive';

export interface ResponsiveContainerProps extends ContainerProps {
  spacing?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
}

export const ResponsiveContainer: React.FC<ResponsiveContainerProps> = ({ 
  children,
  spacing = 'md',
  ...props
}) => {
  const { isMobile } = useResponsive();
  
  return (
    <Container
      px={isMobile ? 'xs' : 'md'}
      py={isMobile ? 'xs' : spacing}
      sx={theme => ({
        maxWidth: '100%',
        width: '100%',
        [theme.fn.largerThan('lg')]: {
          maxWidth: '1200px',
        },
      })}
      {...props}
    >
      {children}
    </Container>
  );
};
```

### Task 7: Implement Mobile Appointment Form

Create a mobile-optimized appointment form:

```typescript
// src/components/appointments/MobileAppointmentForm.tsx
import { 
  MobileForm, 
  MobileFormField, 
  MobileDateTimePicker,
  MobileSelect
} from '@beauty-crm/introvertic-ui';
import { Appointment } from '../../types';

interface MobileAppointmentFormProps {
  initialValues: Partial<Appointment>;
  onSubmit: (values: Appointment) => void;
  onCancel: () => void;
}

export const MobileAppointmentForm: React.FC<MobileAppointmentFormProps> = ({
  initialValues,
  onSubmit,
  onCancel,
}) => {
  return (
    <MobileForm
      initialValues={initialValues}
      onSubmit={onSubmit}
      submitLabel="Save Appointment"
      cancelLabel="Cancel"
      onCancel={onCancel}
    >
      <MobileFormField name="customerName" label="Customer Name" required />
      <MobileFormField name="treatmentName" label="treatmentName" required />
      <MobileDateTimePicker name="startTime" label="Start Time" required />
      <MobileDateTimePicker name="endTime" label="End Time" required />
      <MobileSelect
        name="status"
        label="Status"
        data={[
          { value: 'CONFIRMED', label: 'Confirmed' },
          { value: 'PENDING', label: 'Pending' },
          { value: 'CANCELLED', label: 'Cancelled' },
        ]}
      />
      <MobileFormField name="notes" label="Notes" multiline rows={3} />
    </MobileForm>
  );
};
```

### Task 8: Implement Performance Optimizations

Create a virtualized appointment list:

```typescript
// src/components/appointments/VirtualizedAppointmentList.tsx
import { VirtualizedList } from '@beauty-crm/introvertic-ui';
import { Appointment } from '../../types';
import { AppointmentCard } from './AppointmentCard';

interface VirtualizedAppointmentListProps {
  appointments: Appointment[];
  onAppointmentClick: (appointment: Appointment) => void;
}

export const VirtualizedAppointmentList: React.FC<VirtualizedAppointmentListProps> = ({
  appointments,
  onAppointmentClick,
}) => {
  return (
    <VirtualizedList
      data={appointments}
      height={500}
      itemSize={70}
      renderItem={(appointment) => (
        <AppointmentCard 
          appointment={appointment} 
          onClick={() => onAppointmentClick(appointment)}
        />
      )}
    />
  );
};
```

### Task 9: Implement Error Handling

Create an error boundary component:

```typescript
// src/components/common/ErrorBoundary.tsx
import { ErrorBoundary, ErrorFallback } from '@beauty-crm/introvertic-ui';

export const AppErrorBoundary: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <ErrorBoundary
      fallback={({ error, resetErrorBoundary }) => (
        <ErrorFallback
          title="Something went wrong"
          message={error.message}
          actionLabel="Try again"
          onAction={resetErrorBoundary}
        />
      )}
    >
      {children}
    </ErrorBoundary>
  );
};
```

### Task 10: Update Main Component

Update the main appointment component to use our new mobile components:

```typescript
// src/components/AppointmentCalendar.tsx
import { Box, LoadingOverlay, Tabs, Button, Group } from '@mantine/core';
import { useResponsive } from '../hooks/useResponsive';
import { MobileNavigation } from './navigation/MobileNavigation';
import { MobileCalendarView } from './calendar/MobileCalendarView';
import { DesktopCalendarView } from './calendar/DesktopCalendarView';
import { AppErrorBoundary } from './common/ErrorBoundary';
import { useAppointmentContext } from '../context/AppointmentContext';

export const AppointmentCalendar: React.FC = () => {
  const { isMobile } = useResponsive();
  const { isLoading } = useAppointmentContext();
  
  return (
    <AppErrorBoundary>
      <Box sx={{ position: 'relative', minHeight: '100vh' }}>
        <LoadingOverlay visible={isLoading} />
        
        {isMobile ? (
          <>
            <MobileCalendarView />
            <MobileNavigation />
          </>
        ) : (
          <DesktopCalendarView />
        )}
      </Box>
    </AppErrorBoundary>
  );
};
```

## Testing Your Implementation

1. Run the development server:
   ```bash
   bun run dev
   ```

2. Test on mobile devices:
   - Use Chrome DevTools device emulation
   - Test on real devices using ngrok or similar tool
   - Verify touch interactions work correctly

3. Run automated tests:
   ```bash
   bun run test
   ```

## Performance Monitoring

Add performance monitoring to critical components:

```typescript
// src/utils/performance.ts
export const withPerformanceTracking = <P extends object>(
  Component: React.ComponentType<P>,
  componentName: string
): React.FC<P> => {
  const WithPerformanceTracking: React.FC<P> = (props) => {
    const renderStart = performance.now();
    
    useEffect(() => {
      const renderTime = performance.now() - renderStart;
      if (renderTime > 16) { // 60fps threshold
        console.warn(`Slow render detected in ${componentName}: ${renderTime.toFixed(2)}ms`);
        // Log to monitoring system
      }
    });
    
    return <Component {...props} />;
  };
  
  return WithPerformanceTracking;
};
```

## Committing Your Changes

1. Commit your changes with descriptive messages:
   ```bash
   git add .
   git commit -m "feat(mobile): implement mobile navigation component"
   ```

2. Push your branch:
   ```bash
   git push origin sprint-1/mobile-experience
   ```

3. Create a pull request for review

## Troubleshooting Common Issues

### Issue: Component not rendering on mobile
- Check that `useResponsive` hook is working correctly
- Verify breakpoints in theme configuration
- Check CSS media queries

### Issue: Touch events not working
- Ensure touch events are not being blocked by parent elements
- Check `touchAction` CSS property
- Verify event handlers are properly attached

### Issue: Performance issues
- Use React DevTools Profiler to identify slow components
- Check for unnecessary re-renders
- Implement memoization with `React.memo` and `useMemo`

## Resources

- [Introvertic UI Documentation](https://beauty-crm.github.io/introvertic-ui)
- [Mantine Documentation](https://mantine.dev)
- [React Performance Optimization](https://reactjs.org/docs/optimizing-performance.html)
- [Mobile Web Best Practices](https://developer.mozilla.org/en-US/docs/Web/Guide/Mobile)

---

Happy coding! If you have any questions or run into issues, please reach out to Sarah Chen (Tech Lead) or Rajiv Patel (Principal Engineer). 

## Tasks

### Extracted Tasks

- [ ] Use Chrome DevTools device emulation - M1
- [ ] Test on real devices using ngrok or similar tool - M2
- [ ] Verify touch interactions work correctly - M3
- [ ] Check that `useResponsive` hook is working correctly - M4
- [ ] Verify breakpoints in theme configuration - M5
- [ ] Check CSS media queries - M6
- [ ] Ensure touch events are not being blocked by parent elements - M7
- [ ] Check `touchAction` CSS property - M8
- [ ] Verify event handlers are properly attached - M9
- [ ] Use React DevTools Profiler to identify slow components - M10
- [ ] Check for unnecessary re-renders - M11
- [ ] Implement memoization with `React.memo` and `useMemo` - M12
- [ ] [Introvertic UI Documentation](https://beauty-crm.github.io/introvertic-ui) - M13
- [ ] [Mantine Documentation](https://mantine.dev) - M14
- [ ] [React Performance Optimization](https://reactjs.org/docs/optimizing-performance.html) - M15
- [ ] [Mobile Web Best Practices](https://developer.mozilla.org/en-US/docs/Web/Guide/Mobile) - M16

### Frontend Tasks

- [ ] Implement UI components - M1
- [ ] Add form validation - M2
- [ ] Add error handling - M3
- [ ] Add loading states - M4
- [ ] Add unit tests - M5
- [ ] Add accessibility features - M6

