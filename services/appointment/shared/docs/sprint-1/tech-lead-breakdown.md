# Sprint 1 Technical Breakdown: Mobile Experience Overhaul
*Prepared by <PERSON>, Tech Lead*

## Technical Approach Overview

Based on the UMUX assessments and our Sprint 1 plan focusing on mobile experience, I've broken down the implementation approach into clear technical tasks with architecture decisions and implementation patterns.

## Architecture Decisions

### 1. Responsive Framework Approach

We'll implement a **mobile-first** approach using <PERSON><PERSON>'s responsive capabilities with these key decisions:

```typescript
// Theme configuration with standardized breakpoints
const theme = {
  breakpoints: {
    xs: '320px',    // Small phones
    sm: '576px',    // Large phones
    md: '768px',    // Tablets
    lg: '992px',    // Desktops
    xl: '1200px',   // Large desktops
  },
  // Other theme configurations
};
```

**Rationale:** Standardizing breakpoints in the theme ensures consistency across components and simplifies responsive logic. The mobile-first approach means we'll design for mobile first, then enhance for larger screens.

### 2. Component Architecture

We'll implement a **composition-based** component architecture with responsive variants:

```typescript
// Example of component with responsive variants
interface CalendarViewProps {
  variant?: 'mobile' | 'desktop';
  // Other props
}

const CalendarView: React.FC<CalendarViewProps> = ({ 
  variant = 'desktop',
  ...props 
}) => {
  // Render appropriate variant
  return variant === 'mobile' 
    ? <MobileCalendarView {...props} /> 
    : <DesktopCalendarView {...props} />;
};

// Usage with automatic detection
const AppointmentCalendar: React.FC = () => {
  const isMobile = useMediaQuery('(max-width: 768px)');
  
  return <CalendarView variant={isMobile ? 'mobile' : 'desktop'} />;
};
```

**Rationale:** This approach allows us to maintain separate implementations for mobile and desktop while sharing common logic and types. It also enables easier testing of each variant independently.

### 3. State Management Approach

We'll use a combination of **React Context** for global state and **component-local state** for UI-specific state:

```typescript
// Global appointment context
interface AppointmentContextType {
  appointments: Appointment[];
  selectedDate: Date;
  isLoading: boolean;
  // Actions
  setSelectedDate: (date: Date) => void;
  createAppointment: (appointment: AppointmentInput) => Promise<void>;
  // Other actions
}

// Component-local state for UI interactions
const MobileCalendarView: React.FC = () => {
  const { appointments, selectedDate, setSelectedDate } = useAppointmentContext();
  const [isDetailVisible, setIsDetailVisible] = useState(false);
  const [selectedAppointment, setSelectedAppointment] = useState<Appointment | null>(null);
  
  // Component logic
};
```

**Rationale:** This separation ensures that business logic remains consistent across variants while allowing UI-specific state to be managed locally, reducing unnecessary re-renders.

## Technical Tasks Breakdown

### Theme 1: Mobile Navigation & Layout

#### Task 1.1: Implement Consistent Breakpoint System

```typescript
// src/theme/breakpoints.ts
export const breakpoints = {
  xs: '320px',
  sm: '576px',
  md: '768px',
  lg: '992px',
  xl: '1200px',
};

// src/hooks/useResponsive.ts
export const useResponsive = () => {
  const isMobile = useMediaQuery(`(max-width: ${breakpoints.md})`);
  const isTablet = useMediaQuery(`(min-width: ${breakpoints.md}) and (max-width: ${breakpoints.lg})`);
  const isDesktop = useMediaQuery(`(min-width: ${breakpoints.lg})`);
  
  return { isMobile, isTablet, isDesktop };
};
```

**Implementation Notes:**
- Create a centralized breakpoint system in the theme
- Develop responsive utility hooks for component-level breakpoints
- Update existing components to use the new breakpoint system
- Add unit tests for responsive utilities

#### Task 1.2: Develop Mobile Navigation Component

```typescript
// src/components/navigation/MobileNavigation.tsx
export const MobileNavigation: React.FC = () => {
  return (
    <Box 
      sx={{ 
        position: 'fixed', 
        bottom: 0, 
        left: 0, 
        right: 0,
        height: '60px',
        display: 'flex',
        justifyContent: 'space-around',
        alignItems: 'center',
        backgroundColor: 'white',
        boxShadow: '0 -2px 10px rgba(0, 0, 0, 0.1)',
        zIndex: 100,
        paddingBottom: 'env(safe-area-inset-bottom)',
      }}
    >
      <NavButton icon={<CalendarIcon />} label="Calendar" />
      <NavButton icon={<PlusIcon />} label="New" />
      <NavButton icon={<ListIcon />} label="List" />
      <NavButton icon={<SettingsIcon />} label="Settings" />
    </Box>
  );
};

// NavButton component with proper touch target size
const NavButton: React.FC<NavButtonProps> = ({ icon, label, onClick }) => (
  <Button
    variant="subtle"
    sx={{ 
      minWidth: '44px', 
      minHeight: '44px',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
    }}
    onClick={onClick}
    aria-label={label}
  >
    {icon}
    <Text size="xs">{label}</Text>
  </Button>
);
```

**Implementation Notes:**
- Create bottom navigation component for mobile devices
- Ensure touch targets are at least 44×44px
- Add proper ARIA attributes for accessibility
- Handle safe area insets for modern mobile devices
- Add unit and integration tests

#### Task 1.3: Create Mobile-First Layout Components

```typescript
// src/components/layout/ResponsiveContainer.tsx
export const ResponsiveContainer: React.FC<ResponsiveContainerProps> = ({ 
  children,
  spacing = 'md',
  ...props
}) => {
  const { isMobile } = useResponsive();
  
  return (
    <Container
      px={isMobile ? 'xs' : 'md'}
      py={isMobile ? 'xs' : spacing}
      sx={theme => ({
        maxWidth: '100%',
        width: '100%',
        [theme.fn.largerThan('lg')]: {
          maxWidth: '1200px',
        },
      })}
      {...props}
    >
      {children}
    </Container>
  );
};

// src/components/layout/ResponsiveGrid.tsx
export const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  children,
  columns = { base: 1, sm: 2, md: 3, lg: 4 },
  spacing = 'md',
  ...props
}) => {
  return (
    <SimpleGrid
      cols={1}
      breakpoints={[
        { minWidth: breakpoints.sm, cols: columns.sm || 1 },
        { minWidth: breakpoints.md, cols: columns.md || 2 },
        { minWidth: breakpoints.lg, cols: columns.lg || 3 },
      ]}
      spacing={spacing}
      {...props}
    >
      {children}
    </SimpleGrid>
  );
};
```

**Implementation Notes:**
- Develop responsive container components
- Create adaptive spacing system based on viewport size
- Implement mobile-specific grid system
- Fix layout shifts during resizing
- Add unit tests for responsive behavior

### Theme 2: Mobile Calendar Experience

#### Task 2.1: Develop Mobile Calendar View

```typescript
// src/components/calendar/MobileCalendarView.tsx
export const MobileCalendarView: React.FC = () => {
  const { selectedDate, setSelectedDate, appointments } = useAppointmentContext();
  const [currentView, setCurrentView] = useState<'day' | 'week'>('day');
  
  // Swipe handlers for day navigation
  const handlers = useSwipe({
    onSwipeLeft: () => {
      setSelectedDate(addDays(selectedDate, 1));
    },
    onSwipeRight: () => {
      setSelectedDate(addDays(selectedDate, -1));
    },
  });
  
  return (
    <Box {...handlers}>
      <MobileCalendarHeader 
        date={selectedDate}
        view={currentView}
        onViewChange={setCurrentView}
        onDateChange={setSelectedDate}
      />
      
      {currentView === 'day' ? (
        <MobileDayView 
          date={selectedDate}
          appointments={filterAppointmentsByDate(appointments, selectedDate)}
        />
      ) : (
        <MobileWeekView 
          startDate={startOfWeek(selectedDate)}
          appointments={filterAppointmentsByWeek(appointments, selectedDate)}
        />
      )}
    </Box>
  );
};

// Custom hook for swipe gestures
const useSwipe = ({ onSwipeLeft, onSwipeRight, threshold = 50 }) => {
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);
  
  const onTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };
  
  const onTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };
  
  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) return;
    
    const distance = touchStart - touchEnd;
    if (distance > threshold) {
      onSwipeLeft?.();
    } else if (distance < -threshold) {
      onSwipeRight?.();
    }
  };
  
  return {
    onTouchStart,
    onTouchMove,
    onTouchEnd,
  };
};
```

**Implementation Notes:**
- Create simplified daily view optimized for mobile
- Implement touch-friendly appointment blocks
- Add swipe gestures for day navigation
- Ensure proper spacing for touch interactions
- Add unit and integration tests

#### Task 2.2: Optimize Appointment Viewing for Mobile

```typescript
// src/components/appointments/MobileAppointmentCard.tsx
export const MobileAppointmentCard: React.FC<MobileAppointmentCardProps> = ({
  appointment,
  onEdit,
  onDelete,
  onViewDetails,
}) => {
  return (
    <Card
      p="sm"
      radius="md"
      withBorder
      sx={{
        position: 'relative',
        backgroundColor: getAppointmentColor(appointment.type),
        minHeight: '60px',
        touchAction: 'manipulation',
      }}
      onClick={onViewDetails}
    >
      <Group position="apart" noWrap>
        <Box>
          <Text weight={600} size="sm" lineClamp={1}>
            {appointment.customerName}
          </Text>
          <Text size="xs" color="dimmed">
            {format(new Date(appointment.startTime), 'h:mm a')} - 
            {format(new Date(appointment.endTime), 'h:mm a')}
          </Text>
        </Box>
        
        <ActionMenu
          actions={[
            { label: 'Edit', icon: <EditIcon />, onClick: onEdit },
            { label: 'Delete', icon: <TrashIcon />, onClick: onDelete },
          ]}
        />
      </Group>
    </Card>
  );
};

// Mobile appointment details modal
export const MobileAppointmentDetails: React.FC<MobileAppointmentDetailsProps> = ({
  appointment,
  isOpen,
  onClose,
  onEdit,
  onDelete,
}) => {
  if (!appointment) return null;
  
  return (
    <Modal
      opened={isOpen}
      onClose={onClose}
      fullScreen
      transitionProps={{ transition: 'slide-up' }}
      styles={{
        body: {
          paddingBottom: 'calc(60px + env(safe-area-inset-bottom))',
        },
      }}
    >
      <Box p="md">
        <Group position="apart" mb="md">
          <Text size="xl" weight={700}>
            Appointment Details
          </Text>
          <CloseButton onClick={onClose} />
        </Group>
        
        {/* Appointment details content */}
        <Stack spacing="md">
          <InfoItem label="Customer" value={appointment.customerName} />
          <InfoItem label="treatmentName" value={appointment.treatmentName} />
          <InfoItem 
            label="Time" 
            value={`${format(new Date(appointment.startTime), 'h:mm a')} - 
                   ${format(new Date(appointment.endTime), 'h:mm a')}`} 
          />
          <InfoItem label="Notes" value={appointment.notes || 'No notes'} />
        </Stack>
        
        <Group position="apart" mt="xl">
          <Button variant="outline" color="red" onClick={onDelete}>
            Delete
          </Button>
          <Button onClick={onEdit}>Edit</Button>
        </Group>
      </Box>
    </Modal>
  );
};
```

**Implementation Notes:**
- Create mobile-optimized appointment details view
- Implement quick actions for common tasks
- Ensure proper information hierarchy
- Add loading states for network operations
- Add unit and integration tests

### Theme 3: Visual & Performance Improvements

#### Task 3.1: Enhance Visual Hierarchy for Mobile

```typescript
// src/theme/typography.ts
export const typography = {
  fontSizes: {
    xs: 12,
    sm: 14,
    md: 16,
    lg: 18,
    xl: 20,
  },
  fontWeights: {
    regular: 400,
    medium: 500,
    semibold: 600,
    bold: 700,
  },
  lineHeights: {
    xs: 1.2,
    sm: 1.4,
    md: 1.5,
    lg: 1.6,
    xl: 1.8,
  },
};

// src/components/common/StatusBadge.tsx
export const StatusBadge: React.FC<StatusBadgeProps> = ({ status }) => {
  const getStatusConfig = (status: AppointmentStatus) => {
    switch (status) {
      case 'CONFIRMED':
        return { color: 'green', icon: <CheckIcon size={12} /> };
      case 'PENDING':
        return { color: 'yellow', icon: <ClockIcon size={12} /> };
      case 'CANCELLED':
        return { color: 'red', icon: <XIcon size={12} /> };
      default:
        return { color: 'gray', icon: null };
    }
  };
  
  const { color, icon } = getStatusConfig(status);
  
  return (
    <Badge
      color={color}
      variant="filled"
      leftSection={icon}
      radius="sm"
      sx={{ textTransform: 'capitalize' }}
    >
      {status}
    </Badge>
  );
};
```

**Implementation Notes:**
- Improve contrast for better readability
- Create consistent typography system
- Implement clear visual indicators for status
- Optimize information density for mobile screens
- Add unit tests for visual components

#### Task 3.2: Implement Performance Optimizations

```typescript
// src/components/calendar/LazyCalendarView.tsx
const MobileCalendarView = lazy(() => import('./MobileCalendarView'));
const DesktopCalendarView = lazy(() => import('./DesktopCalendarView'));

export const LazyCalendarView: React.FC = () => {
  const { isMobile } = useResponsive();
  
  return (
    <Suspense fallback={<CalendarSkeleton />}>
      {isMobile ? <MobileCalendarView /> : <DesktopCalendarView />}
    </Suspense>
  );
};

// src/components/appointments/VirtualizedAppointmentList.tsx
export const VirtualizedAppointmentList: React.FC<VirtualizedAppointmentListProps> = ({
  appointments,
  onAppointmentClick,
}) => {
  return (
    <List
      height={500}
      itemCount={appointments.length}
      itemSize={70}
      width="100%"
    >
      {({ index, style }) => (
        <div style={style}>
          <MobileAppointmentCard
            appointment={appointments[index]}
            onViewDetails={() => onAppointmentClick(appointments[index])}
          />
        </div>
      )}
    </List>
  );
};
```

**Implementation Notes:**
- Add code splitting for mobile components
- Implement virtualization for long lists
- Optimize bundle size for mobile networks
- Add proper loading states
- Add performance tests

## Technical Debt & Foundation Work

### Task 4.1: Fix Duplicate Import Issues

```typescript
// Before: src/components/AppointmentCalendar.tsx
import { Box } from '@mantine/core';
import { LoadingOverlay } from '@mantine/core';
import { Tabs } from '@mantine/core';
import { Button, Group } from '@mantine/core';

// After: src/components/AppointmentCalendar.tsx
import { Box, LoadingOverlay, Tabs, Button, Group } from '@mantine/core';
```

**Implementation Notes:**
- Consolidate imports in AppointmentCalendar.tsx and related components
- Add ESLint rule to prevent duplicate imports
- Document import best practices

### Task 4.2: Implement Proper Effect Cleanup

```typescript
// Before: Problematic effect without cleanup
useEffect(() => {
  const interval = setInterval(() => {
    fetchAppointments();
  }, 30000);
}, []);

// After: Effect with proper cleanup
useEffect(() => {
  const interval = setInterval(() => {
    fetchAppointments();
  }, 30000);
  
  return () => {
    clearInterval(interval);
  };
}, [fetchAppointments]);
```

**Implementation Notes:**
- Add cleanup functions to all useEffect hooks
- Fix memory leaks in modal components
- Document effect cleanup patterns

## Testing Strategy

### Unit Tests

```typescript
// Example unit test for responsive utility
describe('useResponsive hook', () => {
  it('should return isMobile=true when viewport width is below md breakpoint', () => {
    // Mock window.matchMedia
    window.matchMedia = jest.fn().mockImplementation((query) => ({
      matches: query.includes('(max-width: 768px)'),
      media: query,
      onchange: null,
      addListener: jest.fn(),
      removeListener: jest.fn(),
    }));
    
    const { result } = renderHook(() => useResponsive());
    
    expect(result.current.isMobile).toBe(true);
    expect(result.current.isDesktop).toBe(false);
  });
});
```

### Integration Tests

```typescript
// Example integration test for mobile navigation
describe('MobileNavigation', () => {
  it('should navigate to calendar view when calendar button is clicked', async () => {
    render(
      <AppointmentProvider>
        <MobileNavigation />
      </AppointmentProvider>
    );
    
    const calendarButton = screen.getByRole('button', { name: /calendar/i });
    await userEvent.click(calendarButton);
    
    expect(mockNavigate).toHaveBeenCalledWith('/calendar');
  });
});
```

### Responsive Tests

```typescript
// Example responsive test
describe('CalendarView', () => {
  it('should render MobileCalendarView on mobile devices', () => {
    // Mock mobile viewport
    window.matchMedia = jest.fn().mockImplementation((query) => ({
      matches: query.includes('(max-width: 768px)'),
      media: query,
      onchange: null,
      addListener: jest.fn(),
      removeListener: jest.fn(),
    }));
    
    render(<CalendarView />);
    
    expect(screen.getByTestId('mobile-calendar-view')).toBeInTheDocument();
    expect(screen.queryByTestId('desktop-calendar-view')).not.toBeInTheDocument();
  });
});
```

## Conclusion

This technical breakdown provides a clear path for implementing the mobile experience overhaul in Sprint 1. By focusing on a mobile-first approach with responsive components, we'll address the critical usability issues identified in the UMUX assessments while laying a solid foundation for future improvements.

The implementation follows modern React patterns with TypeScript, leveraging Mantine's responsive capabilities and ensuring proper performance optimizations. Each task is designed to be testable and maintainable, with clear separation of concerns.

I recommend we start with the foundation work (fixing duplicate imports and effect cleanup) before moving on to the responsive framework implementation, as this will provide a solid base for the new mobile components. 

## Tasks

### Extracted Tasks

- [ ] Create a centralized breakpoint system in the theme - M1
- [ ] Develop responsive utility hooks for component-level breakpoints - M2
- [ ] Update existing components to use the new breakpoint system - M3
- [ ] Add unit tests for responsive utilities - M4
- [ ] Create bottom navigation component for mobile devices - M5
- [ ] Ensure touch targets are at least 44×44px - M6
- [ ] Add proper ARIA attributes for accessibility - M7
- [ ] Handle safe area insets for modern mobile devices - M8
- [ ] Add unit and integration tests - M9
- [ ] Develop responsive container components - M10
- [ ] Create adaptive spacing system based on viewport size - M11
- [ ] Implement mobile-specific grid system - M12
- [ ] Fix layout shifts during resizing - M13
- [ ] Add unit tests for responsive behavior - M14
- [ ] Create simplified daily view optimized for mobile - M15
- [ ] Implement touch-friendly appointment blocks - M16
- [ ] Add swipe gestures for day navigation - M17
- [ ] Ensure proper spacing for touch interactions - M18
- [ ] Add unit and integration tests - M19
- [ ] Create mobile-optimized appointment details view - M20
- [ ] Implement quick actions for common tasks - M21
- [ ] Ensure proper information hierarchy - M22
- [ ] Add loading states for network operations - M23
- [ ] Add unit and integration tests - M24
- [ ] Improve contrast for better readability - M25
- [ ] Create consistent typography system - M26
- [ ] Implement clear visual indicators for status - M27
- [ ] Optimize information density for mobile screens - M28
- [ ] Add unit tests for visual components - M29
- [ ] Add code splitting for mobile components - M30
- [ ] Implement virtualization for long lists - M31
- [ ] Optimize bundle size for mobile networks - M32
- [ ] Add proper loading states - M33
- [ ] Add performance tests - M34
- [ ] Consolidate imports in AppointmentCalendar.tsx and related components - M35
- [ ] Add ESLint rule to prevent duplicate imports - M36
- [ ] Document import best practices - M37
- [ ] Add cleanup functions to all useEffect hooks - M38
- [ ] Fix memory leaks in modal components - M39
- [ ] Document effect cleanup patterns - M40

### Documentation Tasks

- [ ] Update content accuracy - M1
- [ ] Add code examples - M2
- [ ] Add diagrams/screenshots - M3
- [ ] Review and proofread - M4
- [ ] Add cross-references - M5

