# User Persona: <PERSON>

## Basic Information
- **Name**: <PERSON>
- **Age**: 38
- **Occupation**: Beauty Salon Owner & Part-time Stylist
- **Location**: Urban area, mid-sized city
- **Education**: Associate's Degree in Cosmetology, Business Management Certificate

## Professional Background
<PERSON> opened "Elegance Beauty Studio" 7 years ago after working as a stylist for 10 years at various salons. She started with just herself and two chairs, and has since expanded to a team of 8 stylists, 2 nail technicians, and 1 esthetician. She still takes clients 2-3 days per week, but spends most of her time managing the business.

## Technical Proficiency
- **Device Usage**: Uses a desktop computer in her office, a tablet at the front desk, and a smartphone throughout the day
- **Technical Comfort Level**: Moderate (7/10)
- **Software Experience**: Familiar with basic business software, social media platforms, and previous salon management systems
- **Learning Style**: Prefers visual demonstrations followed by hands-on practice

## Goals and Motivations

### Primary Goals
1. **Efficient Business Management**: Needs to oversee all aspects of salon operations while minimizing time spent on administrative tasks
2. **Staff Coordination**: Must manage schedules for 11 team members with different specialties and availability
3. **Client Satisfaction**: Wants to ensure clients can easily book appointments and receive timely service
4. **Business Growth**: Aims to increase revenue by optimizing appointment and reducing gaps between appointments

### Secondary Goals
1. **Work-Life Balance**: Wants to reduce administrative overhead to spend more time with family
2. **Professional Development**: Seeks time to stay current with beauty trends and techniques
3. **Financial Oversight**: Needs visibility into appointment patterns to make business decisions

## Pain Points and Frustrations

### Current Challenges
1. **Schedule Conflicts**: Frequently deals with double-appointments or resource conflicts (station availability)
2. **Last-Minute Changes**: Struggles to efficiently handle cancellations and reschedules
3. **Staff Communication**: Difficulty ensuring all staff are aware of schedule changes
4. **Multi-Role Complexity**: Needs to switch between owner and stylist views quickly
5. **Mobile Limitations**: Current system is difficult to use on mobile devices when away from the desk

### Specific Frustrations
- "I waste too much time fixing appointment mistakes that could have been prevented"
- "When I'm with a client, I need to quickly check the day's schedule without disrupting service"
- "I need to see everyone's schedule at once to make good business decisions"
- "Staff members sometimes don't know about changes to their schedule until they arrive"

## Appointment Behaviors

### Typical Usage Patterns
- **Morning Review**: Checks full salon schedule for the day (6:30-7:00 AM)
- **Between Clients**: Quick schedule checks and adjustments (5-10 times per day)
- **End of Day**: Reviews next day's appointments and makes final adjustments (5:30-6:00 PM)
- **Weekly Planning**: Comprehensive schedule review and staff allocation (Sunday evenings)

### Decision-Making Factors
- Prioritizes even distribution of work among staff
- Considers stylist specialties when assigning new appointments
- Balances walk-in availability with scheduled appointments
- Makes decisions based on revenue optimization and client preferences

## Feature Preferences

### Must-Have Features
- **Multi-Staff View**: Ability to see all staff schedules simultaneously
- **Resource Management**: Tools to manage station/room assignments
- **Quick Editing**: Fast appointment creation and modification
- **Conflict Detection**: Automatic identification of appointment conflicts
- **Client History**: Access to appointment history and preferences

### Nice-to-Have Features
- **Revenue Insights**: Visual indicators of schedule profitability
- **Automated Reminders**: System-generated notifications for staff and clients
- **Template Appointments**: Saved templates for common service combinations
- **Drag-and-Drop Reappointment**: Visual rearrangement of appointments
- **Custom Views**: Personalized dashboard based on role (owner vs. stylist)

## Communication Preferences
- Prefers concise, visual information displays
- Values clear status indicators and color-coding
- Appreciates proactive notifications about potential issues
- Wants both detailed views (for planning) and simplified views (for quick checks)

## Quotes
- "I need to see the big picture and the details at the same time."
- "Every minute I spend fixing the schedule is a minute I'm not growing my business."
- "My staff relies on me to create a workable schedule that maximizes their time and earnings."
- "I'm constantly switching between being a manager and being a stylist, and my tools need to support both roles."

## A Day in the Life

**6:30 AM**: Sarah wakes up and immediately checks the day's schedule on her phone, looking for any overnight cancellations or online appointments.

**7:45 AM**: Arrives at the salon 45 minutes before opening to prepare. Reviews the full schedule on the desktop computer, making note of any potential issues.

**8:30 AM**: Salon opens. Sarah greets the first clients and ensures all stylists have arrived and are aware of their schedules.

**9:00 AM - 12:00 PM**: Takes her own clients while periodically checking the front desk tablet for walk-ins or schedule changes.

**12:30 PM**: During lunch break, handles several appointment adjustments for later in the week and resolves a double-appointment issue.

**1:30 PM - 4:30 PM**: Returns to styling clients, using her smartphone between appointments to confirm next day's schedule.

**5:00 PM**: Transitions to manager role, reviewing the schedule for the following day and making staff adjustments based on expected demand.

**6:00 PM**: Before leaving, sends schedule confirmations to staff and handles any last-minute appointment requests.

**8:30 PM**: At home, spends 20 minutes reviewing the week ahead and identifying opportunities to optimize the schedule.

## UMUX Testing Priorities
When testing the appointment system, Sarah will prioritize:

1. **Efficiency**: Can she complete appointment tasks quickly while multitasking?
2. **Visibility**: Can she easily see all relevant information at different levels of detail?
3. **Conflict Management**: Does the system help prevent and resolve appointment conflicts?
4. **Multi-Device Experience**: Is the experience consistent across her desktop, tablet, and smartphone?
5. **Business Intelligence**: Does the system provide insights that help her make better business decisions? 