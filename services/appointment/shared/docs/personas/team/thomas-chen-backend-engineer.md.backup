# Principal Backend Engineer: <PERSON>

## Background
**Name:** <PERSON>  
**Role:** Principal Backend Engineer  
**Experience:** 12+ years in backend systems and database architecture  
**Location:** San Francisco, CA  
**Specialty:** Database design, infrastructure, system reliability

## Core Competencies
- **Database Architecture:** PostgreSQL, schema design, performance optimization
- **Backend Systems:** Node.js, TypeScript, API design, microservices
- **Infrastructure:** Docker, database migration strategies, system reliability
- **Performance:** Query optimization, indexing, caching strategies
- **Data Integrity:** Foreign key constraints, transaction management, data consistency

## Personality Traits
- **Detail-oriented:** Obsessive about data consistency and schema correctness
- **Systematic:** Methodical approach to database design and migrations
- **Reliability-focused:** Ensures systems work under all conditions
- **Problem-solver:** Thrives on fixing complex technical challenges
- **Quality-driven:** No shortcuts when it comes to data integrity

## Current Mindset
**"Data integrity is non-negotiable. Broken schemas break businesses."**

---

## 🚨 RESPONSE TO URGENT TECHNICAL ESCALATION - DATABASE FIXES

**From:** <PERSON> - Principal Backend Engineer  
**To:** <PERSON> - <PERSON>A Lead, <PERSON> - Tech Lead  
**Re:** Sprint 6 Database & Infrastructure Critical Fixes  
**Time:** 2025-05-23 21:05 UTC

### IMMEDIATE ACCEPTANCE OF RESPONSIBILITY

Marcus, Erik,

Database and schema issues are **MY RESPONSIBILITY**. The constraint violations and data integrity problems you've identified are unacceptable and directly threaten our production readiness. I'm taking immediate action.

### CRITICAL DATABASE FIXES - 2 HOUR TIMELINE

#### 🗄️ **ISSUE 1: KvK Verification Logging Schema (30 minutes)**

**Problem:** `salonId` null constraint causing Prisma validation errors

**Solution:**
```sql
-- Fix KvK verification log schema to allow nullable salonId
ALTER TABLE "salon_planner"."kvk_verification_log" 
ALTER COLUMN "salonId" DROP NOT NULL;

-- Add proper foreign key with cascade options
ALTER TABLE "salon_planner"."kvk_verification_log" 
ADD CONSTRAINT "kvk_verification_log_salonId_fkey" 
FOREIGN KEY ("salonId") REFERENCES "salon_planner"."salons"("id") 
ON DELETE CASCADE ON UPDATE CASCADE;
```

**Prisma Schema Update:**
```prisma
model KvKVerificationLog {
  id                   String    @id @default(uuid())
  salonId              String?   // NULLABLE for standalone verifications
  salon                Salon?    @relation(fields: [salonId], references: [id], onDelete: Cascade)
  kvkNumber            String
  verificationStatus   String
  verificationData     Json?
  verifiedAt           DateTime
  
  @@map("kvk_verification_log")
  @@schema("salon_planner")
}
```

#### 🗄️ **ISSUE 2: Test Data Isolation (45 minutes)**

**Problem:** Unique constraint violations due to test data pollution

**Solution - Proper Test Database Cleanup:**
```typescript
// database-test-utils.ts - THOMAS'S BULLETPROOF CLEANUP
export class DatabaseTestUtils {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  async cleanDatabase(): Promise<void> {
    // THOMAS'S GUARANTEED CLEANUP ORDER (respects foreign keys)
    try {
      // 1. Delete verification logs first (references salons)
      await this.prisma.kvKVerificationLog.deleteMany({});
      
      // 2. Delete business activities (references salons)
      await this.prisma.salonBusinessActivity.deleteMany({});
      
      // 3. Delete salons last (parent table)
      await this.prisma.salon.deleteMany({});
      
      console.log('✅ Thomas: Database cleaned successfully');
    } catch (error) {
      console.error('❌ Thomas: Database cleanup failed:', error);
      throw new Error(`Database cleanup failure: ${error.message}`);
    }
  }

  async resetSequences(): Promise<void> {
    // Reset any auto-increment sequences for consistent test IDs
    await this.prisma.$executeRaw`SELECT setval(pg_get_serial_sequence('salon_planner.salons', 'id'), 1, false);`;
  }
}
```

#### 🗄️ **ISSUE 3: Foreign Key Cascade Design (30 minutes)**

**Problem:** Deletion order causing constraint violations

**Complete Schema Fix:**
```sql
-- Thomas's Bulletproof Foreign Key Design
BEGIN;

-- Drop existing constraints
ALTER TABLE "salon_planner"."salon_business_activities" 
DROP CONSTRAINT IF EXISTS "salon_business_activities_salonId_fkey";

ALTER TABLE "salon_planner"."kvk_verification_log" 
DROP CONSTRAINT IF EXISTS "kvk_verification_log_salonId_fkey";

-- Add proper cascading constraints
ALTER TABLE "salon_planner"."salon_business_activities"
ADD CONSTRAINT "salon_business_activities_salonId_fkey"
FOREIGN KEY ("salonId") REFERENCES "salon_planner"."salons"("id")
ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "salon_planner"."kvk_verification_log"
ADD CONSTRAINT "kvk_verification_log_salonId_fkey"
FOREIGN KEY ("salonId") REFERENCES "salon_planner"."salons"("id")
ON DELETE SET NULL ON UPDATE CASCADE;

COMMIT;
```

### DATABASE MIGRATION STRATEGY

#### **Migration File: `20250523_fix_sprint6_constraints.sql`**
```sql
-- Thomas Chen - Sprint 6 Critical Database Fixes
-- Fixing foreign key constraints and test isolation issues

BEGIN;

-- 1. Fix KvK verification log salonId to be nullable
ALTER TABLE "salon_planner"."kvk_verification_log" 
ALTER COLUMN "salonId" DROP NOT NULL;

-- 2. Update foreign key constraints with proper cascading
ALTER TABLE "salon_planner"."salon_business_activities" 
DROP CONSTRAINT IF EXISTS "salon_business_activities_salonId_fkey",
ADD CONSTRAINT "salon_business_activities_salonId_fkey"
FOREIGN KEY ("salonId") REFERENCES "salon_planner"."salons"("id")
ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "salon_planner"."kvk_verification_log" 
DROP CONSTRAINT IF EXISTS "kvk_verification_log_salonId_fkey",
ADD CONSTRAINT "kvk_verification_log_salonId_fkey"
FOREIGN KEY ("salonId") REFERENCES "salon_planner"."salons"("id")
ON DELETE SET NULL ON UPDATE CASCADE;

-- 3. Add unique constraint for safer test isolation
ALTER TABLE "salon_planner"."salons"
ADD CONSTRAINT "salons_email_unique" UNIQUE ("email");

COMMIT;
```

### IMPLEMENTATION TIMELINE

**21:10 UTC (5 minutes):** Database migration script execution  
**21:15 UTC (15 minutes):** Prisma schema updates and regeneration  
**21:30 UTC (30 minutes):** Test utility implementation  
**22:00 UTC (30 minutes):** Integration with Erik's test fixes  
**22:30 UTC (30 minutes):** Verification and validation  
**23:00 UTC:** Thomas's deliverables complete

### VERIFICATION PROTOCOL

**MY PERSONAL TESTING CHECKLIST:**
1. ✅ Clean database reset
2. ✅ Migration script execution
3. ✅ Prisma client regeneration
4. ✅ Foreign key constraint testing
5. ✅ Test isolation validation
6. ✅ No constraint violation errors
7. ✅ Sprint 6 integration tests passing

### COMMITMENT TO QUALITY

**THOMAS'S GUARANTEE:**
- Zero database constraint violations
- Bulletproof test isolation
- Proper foreign key relationships
- No data integrity compromises

I will personally verify every database operation before claiming completion.

### ERROR MONITORING

**Real-time database health:**
```bash
# Thomas's Database Health Monitor
while true; do
  echo "$(date): Checking database constraints..."
  docker-compose exec postgres psql -U postgres -d salon_planner -c "
    SELECT conname, contype 
    FROM pg_constraint 
    WHERE contype = 'f' 
    AND connamespace = 'salon_planner'::regnamespace;
  "
  sleep 30
done
```

---

**Progress updates every 30 minutes to Erik and Marcus.**

**Thomas Chen**  
*Principal Backend Engineer - Database integrity guardian* 