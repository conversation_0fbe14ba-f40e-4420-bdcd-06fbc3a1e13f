# Sprint 6 Technical Specifications
## Netherlands Market Foundation - Implementation Details

### Epic 1: iDEAL Payment Integration

#### Technical Architecture

```typescript
// Payment Service Interface
interface PaymentGateway {
  provider: 'mollie' | 'adyen' | 'stripe';
  methods: PaymentMethod[];
  webhooks: WebhookHandler[];
  compliance: ComplianceRequirement[];
}

interface MolliePaymentIntegration extends PaymentGateway {
  provider: 'mollie';
  apiKey: string;
  webhookSecret: string;
  
  methods: {
    ideal: {
      banks: IDealBank[];
      redirectUrl: string;
      webhookUrl: string;
    };
    bancontact: {
      redirectUrl: string;
      webhookUrl: string;
    };
    creditcard: {
      supportedBrands: ['visa', 'mastercard', 'amex'];
    };
  };
  
  btw: {
    rate: 0.21; // 21% Netherlands VAT
    calculation: 'inclusive' | 'exclusive';
    reporting: BTWReportingConfig;
  };
}

// Payment Flow Implementation
class AppointmentPaymentService {
  private mollie: MollieAPI;
  
  async createPayment(appointment: Appointment, customer: Customer): Promise<PaymentResponse> {
    const amount = this.calculateTotalWithBTW(appointment.treatments);
    
    const payment = await this.mollie.payments.create({
      amount: {
        currency: 'EUR',
        value: amount.toFixed(2)
      },
      description: `Afspraak bij ${appointment.salon.name}`,
      redirectUrl: `${process.env.FRONTEND_URL}/payment/return`,
      webhookUrl: `${process.env.API_URL}/webhooks/mollie/payment`,
      metadata: {
        appointmentId: appointment.id,
        customerId: customer.id,
        btwAmount: (amount * 0.21).toFixed(2)
      },
      method: 'ideal'
    });
    
    return {
      paymentId: payment.id,
      checkoutUrl: payment.getCheckoutUrl(),
      status: payment.status
    };
  }
  
  private calculateTotalWithBTW(services: Service[]): number {
    const subtotal = services.reduce((sum, service) => sum + service.price, 0);
    return subtotal * 1.21; // Include 21% BTW
  }
}
```

#### Database Schema Updates

```sql
-- Payment tracking table
CREATE TABLE payments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    appointment_id UUID NOT NULL REFERENCES appointments(id),
    mollie_payment_id VARCHAR(255) NOT NULL UNIQUE,
    amount_cents INTEGER NOT NULL,
    btw_amount_cents INTEGER NOT NULL,
    currency VARCHAR(3) DEFAULT 'EUR',
    status VARCHAR(50) NOT NULL,
    payment_method VARCHAR(50),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- BTW reporting table for Dutch tax compliance
CREATE TABLE btw_transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    payment_id UUID NOT NULL REFERENCES payments(id),
    salon_id UUID NOT NULL REFERENCES salons(id),
    btw_rate DECIMAL(5,4) NOT NULL DEFAULT 0.21,
    net_amount_cents INTEGER NOT NULL,
    btw_amount_cents INTEGER NOT NULL,
    gross_amount_cents INTEGER NOT NULL,
    transaction_date DATE NOT NULL,
    quarter INTEGER NOT NULL,
    year INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_payments_appointment_id ON payments(appointment_id);
CREATE INDEX idx_payments_mollie_id ON payments(mollie_payment_id);
CREATE INDEX idx_btw_transactions_quarter_year ON btw_transactions(quarter, year);
```
#### API Endpoints
```typescript
// Payment endpoints
router.post('/api/appointments/:id/payment', async (c) => {
  const appointmentId = c.req.param('id');
  const appointment = await appointmentService.findById(appointmentId);
  
  if (!appointment) {
    return c.json({ error: 'Afspraak niet gevonden' }, 404);
  }
  
  const payment = await paymentService.createPayment(appointment, c.user);
  return c.json(payment);
});

// Mollie webhook handler
router.post('/webhooks/mollie/payment', async (c) => {
  const signature = c.req.header('mollie-signature');
  const body = await c.req.text();
  
  if (!mollieService.verifyWebhook(signature, body)) {
    return c.json({ error: 'Invalid signature' }, 401);
  }
  
  const paymentId = JSON.parse(body).id;
  const payment = await mollieService.getPayment(paymentId);
  
  await paymentService.updatePaymentStatus(payment);
  
  if (payment.status === 'paid') {
    await appointmentService.confirmAppointment(payment.metadata.appointmentId);
    await emailService.sendConfirmation(payment.metadata.customerId, 'nl');
  }
  
  return c.json({ received: true });
});
```
### Epic 2: Dutch Localization Engine

#### Localization Framework
```typescript
// Localization configuration
interface LocaleConfig {
  code: string;
  name: string;
  direction: 'ltr' | 'rtl';
  dateFormat: string;
  timeFormat: string;
  currency: CurrencyConfig;
  holidays: Holiday[];
}

const NetherlandsLocale: LocaleConfig = {
  code: 'nl-NL',
  name: 'Nederlands',
  direction: 'ltr',
  dateFormat: 'DD-MM-YYYY',
  timeFormat: 'HH:mm',
  currency: {
    code: 'EUR',
    symbol: '€',
    position: 'before'
  },
  holidays: [
    { date: '2025-01-01', name: 'Nieuwjaarsdag' },
    { date: '2025-04-21', name: 'Paasmaandag' },
    { date: '2025-04-27', name: 'Koningsdag' },
    { date: '2025-05-05', name: 'Bevrijdingsdag' },
    { date: '2025-05-29', name: 'Hemelvaartsdag' },
    { date: '2025-06-09', name: 'Pinkstermaandag' },
    { date: '2025-12-25', name: 'Eerste Kerstdag' },
    { date: '2025-12-26', name: 'Tweede Kerstdag' }
  ]
};

// Translation service
class TranslationService {
  private translations: Map<string, Map<string, string>> = new Map();
  
  constructor() {
    this.loadTranslations();
  }
  
  t(key: string, locale: string = 'nl-NL', params?: Record<string, string>): string {
    const localeTranslations = this.translations.get(locale);
    if (!localeTranslations) return key;
    
    let translation = localeTranslations.get(key) || key;
    
    // Parameter substitution
    if (params) {
      Object.entries(params).forEach(([param, value]) => {
        translation = translation.replace(`{{${param}}}`, value);
      });
    }
    
    return translation;
  }
  
  private loadTranslations() {
    // Dutch translations
    this.translations.set('nl-NL', new Map([
      ['appointment.book', 'Afspraak maken'],
      ['appointment.confirm', 'Afspraak bevestigen'],
      ['appointment.cancel', 'Afspraak annuleren'],
      ['service.haircut', 'Knippen'],
      ['service.coloring', 'Kleuren'],
      ['service.treatment', 'Behandeling'],
      ['time.available', 'Beschikbaar'],
      ['time.unavailable', 'Niet beschikbaar'],
      ['payment.total', 'Totaal (inclusief 21% BTW)'],
      ['payment.processing', 'Betaling wordt verwerkt...'],
      ['payment.success', 'Betaling succesvol! Uw afspraak is bevestigd.'],
      ['payment.failed', 'Betaling mislukt. Probeer het opnieuw.'],
      ['error.appointment_not_found', 'Afspraak niet gevonden'],
      ['error.time_slot_unavailable', 'Dit tijdslot is niet meer beschikbaar'],
      ['email.subject.confirmation', 'Bevestiging van uw afspraak bij {{salonName}}'],
      ['email.greeting', 'Beste {{customerName}},'],
      ['email.confirmation_text', 'Uw afspraak is bevestigd voor {{date}} om {{time}}.']
    ]));
  }
}
```
#### Beauty Service Terminology Database
```sql
-- Dutch beauty service categories
CREATE TABLE service_categories_nl (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    category_key VARCHAR(100) NOT NULL,
    dutch_name VARCHAR(255) NOT NULL,
    english_name VARCHAR(255) NOT NULL,
    description_nl TEXT,
    description_en TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

INSERT INTO service_categories_nl (category_key, dutch_name, english_name, description_nl, description_en) VALUES
('hair_cut', 'Knippen', 'Hair Cut', 'Professioneel haar knippen en styling', 'Professional hair cutting and styling'),
('hair_color', 'Kleuren', 'Hair Coloring', 'Haar kleuren en highlights', 'Hair coloring and highlights'),
('hair_treatment', 'Haarbehandeling', 'Hair Treatment', 'Verzorgende haarbehandelingen', 'Nourishing hair treatments'),
('manicure', 'Manicure', 'Manicure', 'Nagelverzorging en manicure', 'Nail care and manicure'),
('pedicure', 'Pedicure', 'Pedicure', 'Voetverzorging en pedicure', 'Foot care and pedicure'),
('facial', 'Gezichtsbehandeling', 'Facial Treatment', 'Professionele gezichtsverzorging', 'Professional facial care'),
('massage', 'Massage', 'Massage', 'Ontspannende massage therapie', 'Relaxing massage therapy'),
('eyebrow', 'Wenkbrauwen', 'Eyebrow Treatment', 'Wenkbrauw styling en verzorging', 'Eyebrow styling and care'),
('makeup', 'Make-up', 'Makeup', 'Professionele make-up applicatie', 'Professional makeup application');

-- Common Dutch beauty service durations
CREATE TABLE service_durations_nl (
    service_category VARCHAR(100) REFERENCES service_categories_nl(category_key),
    typical_duration_minutes INTEGER NOT NULL,
    minimum_duration_minutes INTEGER NOT NULL,
    maximum_duration_minutes INTEGER NOT NULL
);

INSERT INTO service_durations_nl VALUES
('hair_cut', 45, 30, 90),
('hair_color', 120, 90, 180),
('hair_treatment', 60, 45, 90),
('manicure', 45, 30, 60),
('pedicure', 60, 45, 90),
('facial', 75, 60, 120),
('massage', 60, 30, 120),
('eyebrow', 30, 15, 45),
('makeup', 45, 30, 90);
```
### Epic 3: KvK Business Verification

#### KvK API Integration
```typescript
// KvK (Chamber of Commerce) integration
interface KvKBusinessInfo {
  kvkNumber: string;
  businessName: string;
  tradeName?: string;
  legalForm: string;
  status: 'active' | 'inactive' | 'dissolved';
  address: {
    street: string;
    houseNumber: string;
    postalCode: string;
    city: string;
    country: string;
  };
  activities: BusinessActivity[];
  establishmentNumber?: string;
}

class KvKVerificationService {
  private apiKey: string;
  private baseUrl = 'https://api.kvk.nl/api/v1';
  
  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }
  
  async verifyBusiness(kvkNumber: string): Promise<KvKBusinessInfo | null> {
    try {
      const response = await fetch(`${this.baseUrl}/companies/${kvkNumber}`, {
        headers: {
          'apikey': this.apiKey,
          'Accept': 'application/json'
        }
      });
      
      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('KvK nummer niet gevonden');
        }
        throw new Error('Fout bij het verifiëren van KvK nummer');
      }
      
      const data = await response.json();
      
      return {
        kvkNumber: data.kvkNumber,
        businessName: data.businessName,
        tradeName: data.tradeName,
        legalForm: data.legalForm,
        status: data.status,
        address: {
          street: data.addresses[0].street,
          houseNumber: data.addresses[0].houseNumber,
          postalCode: data.addresses[0].postalCode,
          city: data.addresses[0].city,
          country: data.addresses[0].country
        },
        activities: data.businessActivities,
        establishmentNumber: data.establishmentNumber
      };
    } catch (error) {
      console.error('KvK verification error:', error);
      return null;
    }
  }
  
  isBeautyBusiness(activities: BusinessActivity[]): boolean {
    const beautySBI = [
      '96021', // Hairdressing and other beauty treatment
      '96022', // Beauty parlours
      '47750', // Retail sale of cosmetic and toilet articles
      '85598', // Other education n.e.c. (including beauty schools)
    ];
    
    return activities.some(activity => 
      beautySBI.includes(activity.sbiCode)
    );
  }
}
```
#### Business Verification Workflow
```typescript
// Salon registration with KvK verification
router.post('/api/salons/register', async (c) => {
  const { kvkNumber, email, password, ...salonData } = await c.req.json();
  
  // Verify KvK number
  const businessInfo = await kvkService.verifyBusiness(kvkNumber);
  if (!businessInfo) {
    return c.json({ 
      error: 'KvK nummer niet gevonden of ongeldig' 
    }, 400);
  }
  
  if (businessInfo.status !== 'active') {
    return c.json({ 
      error: 'Bedrijf is niet actief volgens KvK register' 
    }, 400);
  }
  
  // Check if it's a beauty business
  if (!kvkService.isBeautyBusiness(businessInfo.activities)) {
    return c.json({ 
      error: 'Dit bedrijf is niet geregistreerd als schoonheidssalon' 
    }, 400);
  }
  
  // Create salon with verified business info
  const salon = await salonService.create({
    ...salonData,
    kvkNumber: businessInfo.kvkNumber,
    businessName: businessInfo.businessName,
    tradeName: businessInfo.tradeName,
    legalForm: businessInfo.legalForm,
    address: businessInfo.address,
    verified: true,
    verifiedAt: new Date()
  });
  
  return c.json({ salon, message: 'Salon succesvol geregistreerd en geverifieerd' });
});
```
#### Database Schema for Business Verification
```sql
-- Enhanced salon table with KvK verification
ALTER TABLE salons ADD COLUMN kvk_number VARCHAR(8);
ALTER TABLE salons ADD COLUMN business_name VARCHAR(255);
ALTER TABLE salons ADD COLUMN trade_name VARCHAR(255);
ALTER TABLE salons ADD COLUMN legal_form VARCHAR(100);
ALTER TABLE salons ADD COLUMN verified BOOLEAN DEFAULT FALSE;
ALTER TABLE salons ADD COLUMN verified_at TIMESTAMP;
ALTER TABLE salons ADD COLUMN kvk_last_checked TIMESTAMP;

-- Business activities table
CREATE TABLE salon_business_activities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    salon_id UUID NOT NULL REFERENCES salons(id),
    sbi_code VARCHAR(10) NOT NULL,
    description VARCHAR(255) NOT NULL,
    is_main_activity BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Verification audit trail
CREATE TABLE kvk_verification_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    salon_id UUID NOT NULL REFERENCES salons(id),
    kvk_number VARCHAR(8) NOT NULL,
    verification_status VARCHAR(50) NOT NULL,
    verification_data JSONB,
    verified_at TIMESTAMP DEFAULT NOW()
);
 NOW()
);

-- Indexes
CREATE INDEX idx_salons_kvk_number ON salons(kvk_number);
CREATE INDEX idx_verification_log_salon_id ON kvk_verification_log(salon_id);
```

### Frontend Implementation

#### Dutch Localized Booking Widget

```typescript
// React component for Dutch appointment booking
import { useTranslation } from './hooks/useTranslation';
import { useDateFormatter } from './hooks/useDateFormatter';

interface BookingWidgetProps {
  salonId: string;
  locale: string;
}

export const BookingWidget: React.FC<BookingWidgetProps> = ({ salonId, locale }) => {
  const { t } = useTranslation(locale);
  const { formatDate, formatTime } = useDateFormatter(locale);
  const [selectedService, setSelectedService] = useState<Service | null>(null);
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [selectedTime, setSelectedTime] = useState<string | null>(null);
  
  return (
    <div className="booking-widget">
      <h2>{t('appointment.book')}</h2>
      
      {/* Service Selection */}
      <div className="service-selection">
        <h3>{t('service.select')}</h3>
        <ServiceList 
          salonId={salonId}
          locale={locale}
          onSelect={setSelectedService}
        />
      </div>
      
      {/* Date Selection */}
      {selectedService && (
        <div className="date-selection">
          <h3>{t('date.select')}</h3>
          <DatePicker
            locale={locale}
            excludeHolidays={true}
            minDate={new Date()}
            maxDate={addMonths(new Date(), 2)}
            onChange={setSelectedDate}
            formatDate={formatDate}
          />
        </div>
      )}
      
      {/* Time Selection */}
      {selectedDate && (
        <div className="time-selection">
          <h3>{t('time.select')}</h3>
          <TimeSlots
            salonId={salonId}
            treatmentId={selectedService.id}
            date={selectedDate}
            locale={locale}
            onSelect={setSelectedTime}
          />
        </div>
      )}
      
      {/* Payment */}
      {selectedTime && (
        <div className="payment-section">
          <AppointmentSummary
            service={selectedService}
            date={selectedDate}
            time={selectedTime}
            locale={locale}
          />
          <PaymentForm
            locale={locale}
            defaultMethod="ideal"
            onSuccess={handlePaymentSuccess}
          />
        </div>
      )}
    </div>
  );
};

// Service list component with Dutch terminology
const ServiceList: React.FC<{
  salonId: string;
  locale: string;
  onSelect: (service: Service) => void;
}> = ({ salonId, locale, onSelect }) => {
  const { t } = useTranslation(locale);
  const { data: services } = useServices(salonId, locale);
  
  return (
    <div className="service-list">
      {services?.map(service => (
        <div 
          key={service.id} 
          className="service-item"
          onClick={() => onSelect(service)}
        >
          <h4>{service.name}</h4>
          <p>{service.description}</p>
          <div className="service-details">
            <span className="duration">
              {service.duration} {t('time.minutes')}
            </span>
            <span className="price">
              €{service.price.toFixed(2)} {t('price.including_btw')}
            </span>
          </div>
        </div>
      ))}
    </div>
  );
};
```

#### iDEAL Payment Component

```typescript
// iDEAL payment form component
export const IDealPaymentForm: React.FC<{
  amount: number;
  appointmentId: string;
  locale: string;
  onSuccess: () => void;
}> = ({ amount, appointmentId, locale, onSuccess }) => {
  const { t } = useTranslation(locale);
  const [selectedBank, setSelectedBank] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState(false);
  
  const idealBanks = [
    { id: 'ideal_ABNANL2A', name: 'ABN AMRO' },
    { id: 'ideal_ASNBNL21', name: 'ASN Bank' },
    { id: 'ideal_BUNQNL2A', name: 'Bunq' },
    { id: 'ideal_INGBNL2A', name: 'ING' },
    { id: 'ideal_KNABNL2H', name: 'Knab' },
    { id: 'ideal_MOYONL21', name: 'Moneyou' },
    { id: 'ideal_NNBANL2G', name: 'Nationale-Nederlanden' },
    { id: 'ideal_RABONL2U', name: 'Rabobank' },
    { id: 'ideal_RBRBNL21', name: 'RegioBank' },
    { id: 'ideal_SNSBNL2A', name: 'SNS' },
    { id: 'ideal_TRIONL2U', name: 'Triodos Bank' },
    { id: 'ideal_FVLBNL22', name: 'van Lanschot' }
  ];
  
  const handlePayment = async () => {
    if (!selectedBank) return;
    
    setIsProcessing(true);
    
    try {
      const response = await fetch(`/api/appointments/${appointmentId}/payment`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          method: 'ideal',
          issuer: selectedBank,
          amount: amount
        })
      });
      
      const { checkoutUrl } = await response.json();
      
      // Redirect to iDEAL bank portal
      window.location.href = checkoutUrl;
    } catch (error) {
      setIsProcessing(false);
      alert(t('payment.error'));
    }
  };
  
  return (
    <div className="ideal-payment-form">
      <h3>{t('payment.method.ideal')}</h3>
      
      <div className="bank-selection">
        <label>{t('payment.select_bank')}</label>
        <select 
          value={selectedBank} 
          onChange={e => setSelectedBank(e.target.value)}
          disabled={isProcessing}
        >
          <option value="">{t('payment.choose_bank')}</option>
          {idealBanks.map(bank => (
            <option key={bank.id} value={bank.id}>
              {bank.name}
            </option>
          ))}
        </select>
      </div>
      
      <div className="payment-summary">
        <div className="amount-line">
          <span>{t('payment.subtotal')}</span>
          <span>€{(amount / 1.21).toFixed(2)}</span>
        </div>
        <div className="amount-line">
          <span>{t('payment.btw')} (21%)</span>
          <span>€{(amount * 0.21 / 1.21).toFixed(2)}</span>
        </div>
        <div className="amount-line total">
          <span>{t('payment.total')}</span>
          <span>€{amount.toFixed(2)}</span>
        </div>
      </div>
      
      <button 
        onClick={handlePayment}
        disabled={!selectedBank || isProcessing}
        className="pay-button"
      >
        {isProcessing ? t('payment.processing') : t('payment.pay_now')}
      </button>
    </div>
  );
};
```

### Testing Requirements

#### Unit Tests for Payment Flow

```typescript
// Test suite for iDEAL payment integration
describe('iDEAL Payment Integration', () => {
  let paymentService: AppointmentPaymentService;
  let mockMollie: jest.Mocked<MollieAPI>;
  
  beforeEach(() => {
    mockMollie = {
      payments: {
        create: jest.fn(),
        get: jest.fn()
      }
    } as any;
    
    paymentService = new AppointmentPaymentService(mockMollie);
  });
  
  test('creates iDEAL payment with correct BTW calculation', async () => {
    const appointment = {
      id: 'apt-123',
      services: [{ price: 50 }], // €50 service
      salon: { name: 'Test Salon' }
    };
    
    const customer = { id: 'cust-123' };
    
    mockMollie.payments.create.mockResolvedValue({
      id: 'tr_123',
      getCheckoutUrl: () => 'https://checkout.mollie.com/123',
      status: 'open'
    });
    
    const result = await paymentService.createPayment(appointment, customer);
    
    expect(mockMollie.payments.create).toHaveBeenCalledWith({
      amount: {
        currency: 'EUR',
        value: '60.50' // €50 + 21% BTW
      },
      description: 'Afspraak bij Test Salon',
      method: 'ideal',
      metadata: {
        appointmentId: 'apt-123',
        customerId: 'cust-123',
        btwAmount: '10.50'
      }
    });
    
    expect(result.paymentId).toBe('tr_123');
  });
  
  test('handles BTW calculation correctly for multiple services', () => {
    const services = [
      { price: 50 }, // Haircut
      { price: 30 }  // Treatment
    ];
    
    const total = paymentService.calculateTotalWithBTW(services);
    expect(total).toBe(96.80); // (50 + 30) * 1.21
  });
});
```

#### Integration Tests for KvK Verification

```typescript
describe('KvK Business Verification', () => {
  let kvkService: KvKVerificationService;
  
  beforeEach(() => {
    kvkService = new KvKVerificationService(process.env.KVK_API_KEY!);
  });
  
  test('verifies valid beauty salon KvK number', async () => {
    const result = await kvkService.verifyBusiness('12345678');
    
    expect(result).toMatchObject({
      kvkNumber: '12345678',
      businessName: expect.any(String),
      status: 'active'
    });
  });
  
  test('identifies beauty business correctly', () => {
    const activities = [
      { sbiCode: '96021', description: 'Hairdressing and other beauty treatment' }
    ];
    
    expect(kvkService.isBeautyBusiness(activities)).toBe(true);
  });
  
  test('rejects non-beauty business', () => {
    const activities = [
      { sbiCode: '62010', description: 'Computer programming activities' }
    ];
    
    expect(kvkService.isBeautyBusiness(activities)).toBe(false);
  });
});
```

### Deployment Configuration

#### Netherlands-specific Environment Variables

```bash
# .env.production.netherlands
NODE_ENV=production
DATABASE_URL=postgresql://username:<EMAIL>/beautycrm
REDIS_URL=redis://eu-central-1.elasticache.amazonaws.com:6379

# Payment Configuration
MOLLIE_API_KEY=live_xxx
MOLLIE_WEBHOOK_SECRET=whsec_xxx

# KvK API
KVK_API_KEY=xxx
KVK_API_URL=https://api.kvk.nl/api/v1

# Localization
DEFAULT_LOCALE=nl-NL
SUPPORTED_LOCALES=nl-NL,en-US
TIMEZONE=Europe/Amsterdam

# Email Configuration
SMTP_HOST=smtp.eu-west-1.amazonaws.com
EMAIL_FROM=<EMAIL>
EMAIL_TEMPLATES_PATH=./templates/nl

# Data Residency (GDPR Compliance)
AWS_REGION=eu-central-1
S3_BUCKET=beautycrm-netherlands-eu
DATA_RESIDENCY=EU-ONLY

# Netherlands Holiday API
HOLIDAY_API_URL=https://openholidaysapi.org/PublicHolidays?countryIsoCode=NL
```

#### Docker Configuration for EU Deployment

```dockerfile
# Dockerfile.netherlands
FROM node:18-alpine

# Set Netherlands timezone
RUN apk add --no-cache tzdata
ENV TZ=Europe/Amsterdam

WORKDIR /app

# Copy package files
COPY package*.json ./
RUN npm ci --only=production

# Copy application code
COPY . .

# Build with Netherlands locale
ENV NEXT_LOCALE=nl-NL
RUN npm run build:netherlands

# Health check in Dutch
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/api/health || exit 1

EXPOSE 3000

CMD ["npm", "start"]
```

### Performance Monitoring

#### Netherlands-Specific Metrics

```typescript
// Performance monitoring for Dutch market
interface NetherlandsMetrics {
  payments: {
    idealSuccessRate: number;
    averagePaymentTime: number;
    btwCalculationAccuracy: number;
  };
  
  localization: {
    translationCoverage: number;
    dutchContentQuality: number;
    culturalAdaptationScore: number;
  };
  
  business: {
    kvkVerificationSuccessRate: number;
    salonOnboardingTime: number;
    dutchCustomerSatisfaction: number;
  };
  
  compliance: {
    gdprComplianceScore: number;
    dataResidencyCompliance: number;
    btwReportingAccuracy: number;
  };
}

class NetherlandsPerformanceMonitor {
  async collectMetrics(): Promise<NetherlandsMetrics> {
    const [paymentMetrics, localizationMetrics, businessMetrics, complianceMetrics] = 
      await Promise.all([
        this.getPaymentMetrics(),
        this.getLocalizationMetrics(),
        this.getBusinessMetrics(),
        this.getComplianceMetrics()
      ]);
    
    return {
      payments: paymentMetrics,
      localization: localizationMetrics,
      business: businessMetrics,
      compliance: complianceMetrics
    };
  }
  
  private async getPaymentMetrics() {
    const payments = await db.query(`
      SELECT 
        COUNT(*) FILTER (WHERE status = 'paid' AND payment_method = 'ideal') * 100.0 / 
        COUNT(*) FILTER (WHERE payment_method = 'ideal') as ideal_success_rate,
        AVG(EXTRACT(EPOCH FROM (updated_at - created_at))) as avg_payment_time
      FROM payments 
      WHERE created_at >= NOW() - INTERVAL '24 hours'
    `);
    
    return {
      idealSuccessRate: payments.rows[0].ideal_success_rate || 0,
      averagePaymentTime: payments.rows[0].avg_payment_time || 0,
      btwCalculationAccuracy: await this.calculateBTWAccuracy()
    };
  }
}
```

This technical specification provides the complete implementation details for Sprint 6, ensuring the Netherlands market foundation is built with proper localization, payment processing, and business verification capabilities. 

## Tasks

### Documentation Tasks

- [ ] Update content accuracy - M1
- [ ] Add code examples - M2
- [ ] Add diagrams/screenshots - M3
- [ ] Review and proofread - M4
- [ ] Add cross-references - M5

