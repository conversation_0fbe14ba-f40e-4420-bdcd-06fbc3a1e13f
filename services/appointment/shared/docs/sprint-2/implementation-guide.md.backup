# Sprint 2 Implementation Guide
*Prepared by <PERSON>, Tech Lead*

## Overview

This guide outlines the implementation tasks for Sprint 2, focusing on completing the remaining Mobile Experience Overhaul tasks from Sprint 1 and introducing new features. We'll be enhancing the mobile experience with advanced interactions, offline capabilities, and performance optimizations.

## Setup

1. Update your local repository:
   ```bash
   git checkout main
   git pull origin main
   ```

2. Create a new branch for Sprint 2:
   ```bash
   git checkout -b sprint-2/mobile-experience-completion
   ```

3. Update dependencies:
   ```bash
   bun install
   bun update @beauty-crm/introvertic-ui
   ```

## Remaining Tasks from Sprint 1

### Task 1: Complete Mobile Appointment Details View

The mobile appointment details view was partially implemented in Sprint 1 but needs to be completed with additional features:

```typescript
// src/components/appointments/MobileAppointmentDetails.tsx
import { 
  Card, 
  Text, 
  Badge, 
  Group, 
  ActionIcon, 
  Stack,
  Divider
} from '@mantine/core';
import { 
  IconEdit, 
  IconTrash, 
  IconMessage, 
  IconCalendarEvent 
} from '@tabler/icons-react';
import { format } from 'date-fns';
import { Appointment } from '../../types';
import { useAppointmentContext } from '../../context/AppointmentContext';
import { SwipeableCard } from '@beauty-crm/introvertic-ui';

interface MobileAppointmentDetailsProps {
  appointment: Appointment;
  onClose: () => void;
}

export const MobileAppointmentDetails: React.FC<MobileAppointmentDetailsProps> = ({
  appointment,
  onClose,
}) => {
  const { editAppointment, deleteAppointment, sendReminderToClient } = useAppointmentContext();
  
  const handleSwipeLeft = () => {
    // Show quick actions
  };
  
  return (
    <SwipeableCard
      onSwipeLeft={handleSwipeLeft}
      leftSwipeThreshold={0.3}
      leftSwipeContent={
        <Group spacing="xs">
          <ActionIcon color="blue" variant="filled" onClick={() => editAppointment(appointment.id)}>
            <IconEdit size={18} />
          </ActionIcon>
          <ActionIcon color="red" variant="filled" onClick={() => deleteAppointment(appointment.id)}>
            <IconTrash size={18} />
          </ActionIcon>
          <ActionIcon color="green" variant="filled" onClick={() => sendReminderToClient(appointment.id)}>
            <IconMessage size={18} />
          </ActionIcon>
        </Group>
      }
    >
      <Card p="md" radius="md" withBorder>
        <Stack spacing="xs">
          <Group position="apart">
            <Text weight={700} size="lg">{appointment.customerName}</Text>
            <Badge color={appointment.status === 'confirmed' ? 'green' : appointment.status === 'pending' ? 'yellow' : 'red'}>
              {appointment.status}
            </Badge>
          </Group>
          
          <Text size="sm" color="dimmed">{appointment.treatmentName}</Text>
          
          <Divider my="xs" />
          
          <Group spacing="xs">
            <IconCalendarEvent size={16} />
            <Text size="sm">
              {format(new Date(appointment.startTime), 'PPP')} at {format(new Date(appointment.startTime), 'p')} - {format(new Date(appointment.endTime), 'p')}
            </Text>
          </Group>
          
          {appointment.notes && (
            <>
              <Divider my="xs" />
              <Text size="sm" weight={500}>Notes</Text>
              <Text size="sm">{appointment.notes}</Text>
            </>
          )}
          
          <Group position="right" mt="md">
            <ActionIcon color="blue" onClick={() => editAppointment(appointment.id)}>
              <IconEdit size={18} />
            </ActionIcon>
            <ActionIcon color="red" onClick={() => deleteAppointment(appointment.id)}>
              <IconTrash size={18} />
            </ActionIcon>
          </Group>
        </Stack>
      </Card>
    </SwipeableCard>
  );
};
```

### Task 2: Implement Offline Support

Add offline capabilities to allow basic functionality when network is unavailable:

```typescript
// src/hooks/useOfflineSupport.ts
import { useState, useEffect } from 'react';
import { useLocalStorage } from '@mantine/hooks';
import { Appointment } from '../types';

export const useOfflineSupport = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [pendingChanges, setPendingChanges] = useLocalStorage<{
    create: Appointment[];
    update: Appointment[];
    delete: string[];
  }>({
    key: 'appointment-pending-changes',
    defaultValue: { create: [], update: [], delete: [] },
  });
  
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);
    
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);
  
  // Function to add a pending change
  const addPendingChange = (type: 'create' | 'update' | 'delete', data: Appointment | string) => {
    if (type === 'delete' && typeof data === 'string') {
      setPendingChanges({
        ...pendingChanges,
        delete: [...pendingChanges.delete, data],
      });
    } else if ((type === 'create' || type === 'update') && typeof data !== 'string') {
      setPendingChanges({
        ...pendingChanges,
        [type]: [...pendingChanges[type], data],
      });
    }
  };
  
  // Function to sync pending changes when back online
  const syncPendingChanges = async (api: any) => {
    if (!isOnline) return;
    
    try {
      // Process creates
      for (const appointment of pendingChanges.create) {
        await api.createAppointment(appointment);
      }
      
      // Process updates
      for (const appointment of pendingChanges.update) {
        await api.updateAppointment(appointment.id, appointment);
      }
      
      // Process deletes
      for (const id of pendingChanges.delete) {
        await api.deleteAppointment(id);
      }
      
      // Clear pending changes
      setPendingChanges({ create: [], update: [], delete: [] });
    } catch (error) {
      console.error('Failed to sync pending changes:', error);
    }
  };
  
  return {
    isOnline,
    addPendingChange,
    syncPendingChanges,
    pendingChanges,
  };
};
```

### Task 3: Enhance Mobile Navigation with Transitions

Improve the mobile navigation with smooth transitions:

```typescript
// src/components/navigation/EnhancedMobileNavigation.tsx
import { useState } from 'react';
import { MobileNavBar, MobileNavItem, Transition } from '@beauty-crm/introvertic-ui';
import { IconCalendar, IconPlus, IconList, IconSettings, IconX } from '@tabler/icons-react';
import { useAppointmentContext } from '../../context/AppointmentContext';
import { Drawer, Stack } from '@mantine/core';

export const EnhancedMobileNavigation: React.FC = () => {
  const { openAppointmentModal } = useAppointmentContext();
  const [activeTab, setActiveTab] = useState('calendar');
  const [settingsOpen, setSettingsOpen] = useState(false);
  
  return (
    <>
      <MobileNavBar
        activeItem={activeTab}
        onChange={setActiveTab}
        transitionDuration={200}
        transitionTimingFunction="ease"
      >
        <MobileNavItem 
          icon={<IconCalendar size={24} />} 
          label="Calendar" 
          value="calendar" 
        />
        <MobileNavItem 
          icon={<IconPlus size={24} />} 
          label="New" 
          onClick={() => openAppointmentModal()} 
        />
        <MobileNavItem 
          icon={<IconList size={24} />} 
          label="List" 
          value="list" 
        />
        <MobileNavItem 
          icon={<IconSettings size={24} />} 
          label="Settings" 
          onClick={() => setSettingsOpen(true)} 
        />
      </MobileNavBar>
      
      <Drawer
        opened={settingsOpen}
        onClose={() => setSettingsOpen(false)}
        title="Settings"
        padding="md"
        position="right"
        closeButtonLabel="Close settings"
      >
        {/* Settings content */}
      </Drawer>
    </>
  );
};
```

## New Features for Sprint 2

### Task 4: Implement Pull-to-Refresh

Add pull-to-refresh functionality for mobile users:

```typescript
// src/components/common/PullToRefresh.tsx
import { ReactNode, useState } from 'react';
import { Box, Loader, Center, Text } from '@mantine/core';
import { usePullToRefresh } from '@beauty-crm/introvertic-ui';

interface PullToRefreshProps {
  onRefresh: () => Promise<void>;
  children: ReactNode;
}

export const PullToRefresh: React.FC<PullToRefreshProps> = ({ onRefresh, children }) => {
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  const { pullProps, indicatorProps } = usePullToRefresh({
    onRefresh: async () => {
      setIsRefreshing(true);
      try {
        await onRefresh();
      } finally {
        setIsRefreshing(false);
      }
    },
    threshold: 80,
  });
  
  return (
    <Box {...pullProps} sx={{ position: 'relative', height: '100%', overflow: 'hidden' }}>
      <Box {...indicatorProps} sx={{ position: 'absolute', top: 0, left: 0, right: 0, zIndex: 10 }}>
        <Center p="md">
          {isRefreshing ? (
            <Loader size="sm" />
          ) : (
            <Text size="sm" color="dimmed">Pull down to refresh</Text>
          )}
        </Center>
      </Box>
      
      {children}
    </Box>
  );
};
```

### Task 5: Implement Mobile-Optimized Search

Create a mobile-optimized search experience:

```typescript
// src/components/search/MobileSearch.tsx
import { useState } from 'react';
import { 
  TextInput, 
  ActionIcon, 
  Box, 
  Transition, 
  Paper,
  Text,
  Group,
  Stack
} from '@mantine/core';
import { IconSearch, IconX } from '@tabler/icons-react';
import { useAppointmentContext } from '../../context/AppointmentContext';
import { AppointmentCard } from '../appointments/AppointmentCard';

export const MobileSearch: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const { appointments, openAppointmentDetails } = useAppointmentContext();
  
  const filteredAppointments = appointments.filter(
    appt => 
      appt.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      appt.treatmentName.toLowerCase().includes(searchQuery.toLowerCase())
  );
  
  return (
    <>
      <Box sx={{ position: 'fixed', top: 0, left: 0, right: 0, zIndex: 100 }}>
        <Group p="xs" position="apart">
          {!isSearchOpen && (
            <ActionIcon onClick={() => setIsSearchOpen(true)}>
              <IconSearch size={20} />
            </ActionIcon>
          )}
          
          <Transition mounted={isSearchOpen} transition="slide-down" duration={200}>
            {(styles) => (
              <Box style={styles} sx={{ width: '100%' }}>
                <TextInput
                  placeholder="Search appointments..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.currentTarget.value)}
                  rightSection={
                    <ActionIcon onClick={() => {
                      setIsSearchOpen(false);
                      setSearchQuery('');
                    }}>
                      <IconX size={16} />
                    </ActionIcon>
                  }
                  autoFocus
                />
              </Box>
            )}
          </Transition>
        </Group>
      </Box>
      
      <Transition mounted={isSearchOpen && searchQuery.length > 0} transition="fade" duration={200}>
        {(styles) => (
          <Paper
            style={styles}
            sx={{ 
              position: 'fixed', 
              top: 50, 
              left: 0, 
              right: 0, 
              bottom: 60, 
              zIndex: 99,
              overflow: 'auto'
            }}
            p="md"
          >
            <Stack spacing="md">
              {filteredAppointments.length > 0 ? (
                filteredAppointments.map(appointment => (
                  <AppointmentCard
                    key={appointment.id}
                    appointment={appointment}
                    onClick={() => {
                      openAppointmentDetails(appointment.id);
                      setIsSearchOpen(false);
                      setSearchQuery('');
                    }}
                  />
                ))
              ) : (
                <Text align="center" color="dimmed">No appointments found</Text>
              )}
            </Stack>
          </Paper>
        )}
      </Transition>
    </>
  );
};
```

### Task 6: Implement Appointment Conflict Detection

Add appointment conflict detection to prevent double-appointment:

```typescript
// src/utils/appointmentConflicts.ts
import { Appointment } from '../types';
import { isWithinInterval, areIntervalsOverlapping } from 'date-fns';

export const checkForConflicts = (
  newAppointment: Appointment,
  existingAppointments: Appointment[]
): Appointment[] => {
  const newInterval = {
    start: new Date(newAppointment.startTime),
    end: new Date(newAppointment.endTime)
  };
  
  return existingAppointments.filter(existing => {
    // Skip comparing with itself
    if (existing.id === newAppointment.id) return false;
    
    const existingInterval = {
      start: new Date(existing.startTime),
      end: new Date(existing.endTime)
    };
    
    return areIntervalsOverlapping(newInterval, existingInterval);
  });
};

export const ConflictWarning: React.FC<{ conflicts: Appointment[] }> = ({ conflicts }) => {
  if (conflicts.length === 0) return null;
  
  return (
    <Box p="xs" mb="md" sx={theme => ({ 
      backgroundColor: theme.colors.red[0],
      borderRadius: theme.radius.sm,
      border: `1px solid ${theme.colors.red[3]}`
    })}>
      <Text color="red" size="sm" weight={500}>
        Warning: This appointment conflicts with {conflicts.length} existing appointment{conflicts.length > 1 ? 's' : ''}:
      </Text>
      <List size="sm" spacing="xs" mt="xs">
        {conflicts.map(conflict => (
          <List.Item key={conflict.id}>
            {conflict.customerName} - {format(new Date(conflict.startTime), 'p')} to {format(new Date(conflict.endTime), 'p')}
          </List.Item>
        ))}
      </List>
    </Box>
  );
};
```

### Task 7: Implement Mobile Gesture Controls

Add gesture controls for common actions:

```typescript
// src/hooks/useGestureControls.ts
import { useGesture } from '@use-gesture/react';
import { useViewportSize } from '@mantine/hooks';

export const useGestureControls = ({
  onSwipeLeft,
  onSwipeRight,
  onSwipeUp,
  onSwipeDown,
  threshold = 50,
}: {
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  onSwipeUp?: () => void;
  onSwipeDown?: () => void;
  threshold?: number;
}) => {
  const { width, height } = useViewportSize();
  
  const bind = useGesture({
    onDrag: ({ movement: [mx, my], direction: [dx, dy], last }) => {
      if (!last) return;
      
      const horizontalThreshold = width * (threshold / 100);
      const verticalThreshold = height * (threshold / 100);
      
      if (Math.abs(mx) > horizontalThreshold) {
        if (dx > 0 && onSwipeRight) onSwipeRight();
        else if (dx < 0 && onSwipeLeft) onSwipeLeft();
      }
      
      if (Math.abs(my) > verticalThreshold) {
        if (dy > 0 && onSwipeDown) onSwipeDown();
        else if (dy < 0 && onSwipeUp) onSwipeUp();
      }
    },
  });
  
  return bind;
};
```

### Task 8: Implement Mobile Analytics

Add mobile-specific analytics tracking:

```typescript
// src/utils/analytics.ts
interface AnalyticsEvent {
  category: string;
  action: string;
  label?: string;
  value?: number;
  metadata?: Record<string, any>;
}

export const trackEvent = (event: AnalyticsEvent) => {
  // In a real implementation, this would send to your analytics provider
  console.log('Analytics event:', event);
  
  // Example implementation for Google Analytics
  if (typeof window !== 'undefined' && 'gtag' in window) {
    (window as any).gtag('event', event.action, {
      event_category: event.category,
      event_label: event.label,
      value: event.value,
      ...event.metadata,
    });
  }
};

export const useMobileAnalytics = () => {
  const trackMobileInteraction = (action: string, label?: string, metadata?: Record<string, any>) => {
    trackEvent({
      category: 'Mobile',
      action,
      label,
      metadata: {
        ...metadata,
        viewport: window.innerWidth,
        userAgent: navigator.userAgent,
      },
    });
  };
  
  return { trackMobileInteraction };
};
```

### Task 9: Implement Mobile-Optimized Notifications

Create mobile-optimized notifications:

```typescript
// src/components/notifications/MobileNotifications.tsx
import { useState, useEffect } from 'react';
import { 
  Notification, 
  Stack, 
  Transition, 
  Box 
} from '@mantine/core';
import { IconCheck, IconX, IconAlertCircle } from '@tabler/icons-react';

export interface MobileNotification {
  id: string;
  title: string;
  message?: string;
  type: 'success' | 'error' | 'warning' | 'info';
  autoClose?: boolean;
}

interface MobileNotificationsProps {
  notifications: MobileNotification[];
  onClose: (id: string) => void;
}

export const MobileNotifications: React.FC<MobileNotificationsProps> = ({
  notifications,
  onClose,
}) => {
  return (
    <Box
      sx={{
        position: 'fixed',
        bottom: 70, // Above the navigation bar
        left: 0,
        right: 0,
        zIndex: 1000,
        padding: '0 16px',
      }}
    >
      <Stack spacing="xs">
        {notifications.map((notification) => (
          <Transition key={notification.id} mounted={true} transition="slide-up" duration={200}>
            {(styles) => (
              <Notification
                style={styles}
                title={notification.title}
                color={
                  notification.type === 'success' ? 'green' :
                  notification.type === 'error' ? 'red' :
                  notification.type === 'warning' ? 'yellow' : 'blue'
                }
                icon={
                  notification.type === 'success' ? <IconCheck size={18} /> :
                  notification.type === 'error' ? <IconX size={18} /> :
                  notification.type === 'warning' ? <IconAlertCircle size={18} /> : null
                }
                onClose={() => onClose(notification.id)}
                sx={{ boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)' }}
              >
                {notification.message}
              </Notification>
            )}
          </Transition>
        ))}
      </Stack>
    </Box>
  );
};
```

### Task 10: Implement Mobile Performance Optimizations

Optimize performance for mobile devices:

```typescript
// src/utils/mobileOptimizations.ts
import { useEffect, useState } from 'react';
import { useViewportSize } from '@mantine/hooks';

export const useMobileOptimizations = () => {
  const { width } = useViewportSize();
  const [reducedMotion, setReducedMotion] = useState(false);
  const [lowPowerMode, setLowPowerMode] = useState(false);
  
  // Detect reduced motion preference
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setReducedMotion(mediaQuery.matches);
    
    const handleChange = (e: MediaQueryListEvent) => {
      setReducedMotion(e.matches);
    };
    
    mediaQuery.addEventListener('change', handleChange);
    return () => {
      mediaQuery.removeEventListener('change', handleChange);
    };
  }, []);
  
  // Detect low power mode (battery API)
  useEffect(() => {
    if ('getBattery' in navigator) {
      (navigator as any).getBattery().then((battery: any) => {
        const checkBattery = () => {
          // Consider low power mode if battery level is below 20%
          setLowPowerMode(battery.level < 0.2 && !battery.charging);
        };
        
        checkBattery();
        
        battery.addEventListener('levelchange', checkBattery);
        battery.addEventListener('chargingchange', checkBattery);
        
        return () => {
          battery.removeEventListener('levelchange', checkBattery);
          battery.removeEventListener('chargingchange', checkBattery);
        };
      });
    }
  }, []);
  
  // Determine if we should use simplified views
  const shouldUseSimplifiedViews = lowPowerMode || (width < 360);
  
  // Determine animation duration based on preferences
  const getAnimationDuration = (defaultDuration: number) => {
    if (reducedMotion) return 0;
    if (lowPowerMode) return defaultDuration / 2;
    return defaultDuration;
  };
  
  return {
    reducedMotion,
    lowPowerMode,
    shouldUseSimplifiedViews,
    getAnimationDuration,
  };
};
```

## Integration Tasks

### Task 11: Update Main Component with New Features

Update the main appointment component to integrate all new features:

```typescript
// src/components/AppointmentCalendar.tsx
import { Box, LoadingOverlay } from '@mantine/core';
import { useResponsive } from '../hooks/useResponsive';
import { EnhancedMobileNavigation } from './navigation/EnhancedMobileNavigation';
import { MobileCalendarView } from './calendar/MobileCalendarView';
import { DesktopCalendarView } from './calendar/DesktopCalendarView';
import { AppErrorBoundary } from './common/ErrorBoundary';
import { useAppointmentContext } from '../context/AppointmentContext';
import { PullToRefresh } from './common/PullToRefresh';
import { MobileSearch } from './search/MobileSearch';
import { MobileNotifications } from './notifications/MobileNotifications';
import { useMobileOptimizations } from '../utils/mobileOptimizations';
import { useOfflineSupport } from '../hooks/useOfflineSupport';

export const AppointmentCalendar: React.FC = () => {
  const { isMobile } = useResponsive();
  const { isLoading, fetchAppointments, notifications, closeNotification } = useAppointmentContext();
  const { shouldUseSimplifiedViews } = useMobileOptimizations();
  const { isOnline, syncPendingChanges } = useOfflineSupport();
  
  const handleRefresh = async () => {
    if (isOnline) {
      await syncPendingChanges();
      await fetchAppointments();
    }
  };
  
  return (
    <AppErrorBoundary>
      <Box sx={{ position: 'relative', minHeight: '100vh' }}>
        <LoadingOverlay visible={isLoading} />
        
        {isMobile ? (
          <>
            <MobileSearch />
            
            <PullToRefresh onRefresh={handleRefresh}>
              <MobileCalendarView simplified={shouldUseSimplifiedViews} />
            </PullToRefresh>
            
            <MobileNotifications 
              notifications={notifications}
              onClose={closeNotification}
            />
            
            <EnhancedMobileNavigation />
          </>
        ) : (
          <DesktopCalendarView />
        )}
      </Box>
    </AppErrorBoundary>
  );
};
```

## Testing Your Implementation

1. Run the development server:
   ```bash
   bun run dev
   ```

2. Test on mobile devices:
   - Use Chrome DevTools device emulation with various screen sizes
   - Test on real devices using ngrok or similar tool
   - Test with different network conditions (offline, slow 3G)
   - Test with different battery levels (if possible)

3. Run automated tests:
   ```bash
   bun run test
   ```

4. Run performance tests:
   ```bash
   bun run test:perf
   ```

## Performance Benchmarks

For Sprint 2, we're setting the following performance targets:

1. First Contentful Paint (FCP): < 1.5s on 3G connections
2. Time to Interactive (TTI): < 3s on 3G connections
3. Lighthouse Mobile Score: > 90
4. JavaScript bundle size: < 200KB (gzipped)

Use the following command to measure bundle size:
```bash
bun run analyze-bundle
```

## Committing Your Changes

1. Commit your changes with descriptive messages:
   ```bash
   git add .
   git commit -m "feat(mobile): implement offline support"
   ```

2. Push your branch:
   ```bash
   git push origin sprint-2/mobile-experience-completion
   ```

3. Create a pull request for review

## Troubleshooting Common Issues

### Issue: Offline changes not syncing
- Check that `useOfflineSupport` hook is properly integrated
- Verify localStorage is working correctly
- Check network status detection

### Issue: Gesture controls not working
- Ensure touch events are properly bound
- Check threshold values for different device sizes
- Test on different browsers and devices

### Issue: Performance issues on low-end devices
- Use the React Profiler to identify bottlenecks
- Consider implementing code splitting
- Reduce animation complexity when `shouldUseSimplifiedViews` is true

## Resources

- [Introvertic UI Documentation](https://beauty-crm.github.io/introvertic-ui)
- [Mantine Documentation](https://mantine.dev)
- [React Performance Optimization](https://reactjs.org/docs/optimizing-performance.html)
- [Mobile Web Best Practices](https://developer.mozilla.org/en-US/docs/Web/Guide/Mobile)
- [Offline Web Applications](https://web.dev/offline-cookbook/)
- [Battery Status API](https://developer.mozilla.org/en-US/docs/Web/API/Battery_Status_API)

---

Happy coding! If you have any questions or run into issues, please reach out to Sarah Chen (Tech Lead) or Rajiv Patel (Principal Engineer). 