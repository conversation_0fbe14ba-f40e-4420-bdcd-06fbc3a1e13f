# Sprint 2: Appointment Terminology Unification
*Standardizing Domain Language Across Systems*

## Overview

Sprint 2 focuses on unifying terminology across both the `appointment-planner` and `appointment-management` systems by standardizing on "appointment" nomenclature. This involves refactoring database schemas, APIs, and frontend components to eliminate confusion between "appointment", "appointment", and "appointment" terms.

## Terminology Challenge

**Core Problem:** Inconsistent terminology across systems creates developer confusion and complicates integration:
- External system uses `Appointment` model and `appointments` table  
- Management system uses `Appointment` model and `appointment` schema
- Mixed usage of "appointment", "appointment", and "appointment" throughout codebases

### Current State Analysis

#### External System (Before)
- **Database:** `appointments` table 
- **Schema:** `Appointment` model
- **API:** `/api/appointments`, `/api/book`
- **Types:** `AppointmentStatus`, `AppointmentService`

#### Management System (Before)  
- **Database:** `appointment` schema
- **Schema:** `Appointment` model  
- **API:** `/api/appointment`, `/api/appointments`
- **Types:** `AppointmentStatus`, `AppointmentService`

### Target State (After Refactoring)

#### External System (After)
- **Database:** `appointments` table
- **Schema:** `Appointment` model  
- **API:** `/api/appointments`, `/api/appointment`
- **Types:** `AppointmentStatus`, `AppointmentService`

#### Management System (After)
- **Database:** `appointments` schema  
- **Schema:** `Appointment` model
- **API:** `/api/appointments`
- **Types:** `AppointmentStatus`, `AppointmentService`

## Refactoring Strategy

### Phase 1: Database Schema Unification

```typescript
// Unified Appointment model for both systems
interface Appointment {
  id: string;
  customerId: string;
  treatmentId: string;
  staffId?: string;
  startTime: Date;
  endTime: Date;
  status: AppointmentStatus;
  notes?: string;
  
  // External-specific fields
  customerName?: string;
  customerEmail?: string;
  customerPhone?: string;
  treatmentName?: string;
  treatmentDuration?: number;
  treatmentPrice?: number;
  salonId?: string;
  salonName?: string;
  
  createdAt: Date;
  updatedAt: Date;
}

enum AppointmentStatus {
  PENDING = 'PENDING',
  CONFIRMED = 'CONFIRMED', 
  SCHEDULED = 'SCHEDULED',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  NO_SHOW = 'NO_SHOW',
  RESCHEDULED = 'RESCHEDULED'
}
```

## Technical Implementation Plan

### Phase 1: External System Refactoring

#### Task 1.1: File Renaming (External System - PRIORITY)

```bash
# appointment-planner-backend file renames
mv src/domain/models/appointment.ts src/domain/models/appointment.ts
mv src/domain/repositories/appointmentRepository.ts src/domain/repositories/appointmentRepository.ts
mv src/application/services/appointmentService.ts src/application/services/appointmentService.ts
mv src/infrastructure/repositories/appointmentRepository.ts src/infrastructure/repositories/appointmentRepository.ts
mv src/presentation/controllers/appointmentController.ts src/presentation/controllers/appointmentController.ts

# Update route files
mv src/presentation/routes/appointmentRoutes.ts src/presentation/routes/appointmentRoutes.ts

# Test file renames
mv src/tests/features/double-appointment/ src/tests/features/double-appointment/
mv src/tests/features/double-appointment/double-appointment.steps.ts src/tests/features/double-appointment/double-appointment.steps.ts
mv src/tests/features/double-appointment/double-appointment-bug.steps.ts src/tests/features/double-appointment/double-appointment-bug.steps.ts

# Frontend file renames
# appointment-planner-frontend
mv src/components/AppointmentForm.tsx src/components/AppointmentForm.tsx
mv src/components/AppointmentCalendar.tsx src/components/AppointmentCalendar.tsx
mv src/components/AppointmentSuccess.tsx src/components/AppointmentSuccess.tsx
```

```typescript
// Update all import statements across the external system
// Example automated script for import updates
const updateImports = {
  'appointment.ts': 'appointment.ts',
  'appointmentRepository': 'appointmentRepository', 
  'appointmentService': 'appointmentService',
  'appointmentController': 'appointmentController',
  'AppointmentForm': 'AppointmentForm',
  'AppointmentCalendar': 'AppointmentCalendar',
  'AppointmentSuccess': 'AppointmentSuccess',
};
```

#### Task 1.2: Database Schema Migration (External System)

```sql
-- appointment-planner-backend/prisma/migrations/rename_appointments_to_appointments.sql
-- Rename table
ALTER TABLE "appointments" RENAME TO "appointments";

-- Update enum name
ALTER TYPE "AppointmentStatus" RENAME TO "AppointmentStatus";

-- Update column references in indexes if needed
-- (Prisma will handle most of this automatically)
```

```prisma
// appointment-planner-backend/prisma/schema.prisma (Updated)
model Appointment {
  id              String            @id @default(uuid())
  salonId         String
  salonName       String
  salonLogo       String?
  salonColor      String?
  customerName    String
  customerEmail   String
  customerPhone   String?
  treatmentName     String
  treatmentDuration Int
  treatmentPrice    Float
  specialistId    String?
  startTime       DateTime
  endTime         DateTime
  notes           String?
  status          AppointmentStatus @default(PENDING)
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt

  @@map("appointments")
}

enum AppointmentStatus {
  PENDING
  CONFIRMED
  CANCELLED
  COMPLETED
}
```

#### Task 1.3: Domain Model Refactoring (External System)

```typescript
// appointment-planner-backend/src/domain/models/appointment.ts (Renamed from appointment.ts)
import cuid from 'cuid';

export enum AppointmentStatus {
  PENDING = 'PENDING',
  CONFIRMED = 'CONFIRMED',
  CANCELLED = 'CANCELLED',
  COMPLETED = 'COMPLETED',
}

export interface IAppointment {
  id?: string;
  salonId: string;
  salonName: string;
  salonLogo?: string;
  salonColor?: string;
  customerName: string;
  customerEmail: string;
  customerPhone?: string;
  treatmentName: string;
  treatmentDuration: number;
  treatmentPrice: number;
  specialistId?: string;
  startTime: Date;
  endTime: Date;
  status?: AppointmentStatus;
  notes?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export class Appointment implements IAppointment {
  public readonly id: string;
  public readonly salonId: string;
  public readonly salonName: string;
  public readonly salonLogo?: string;
  public readonly salonColor?: string;
  public readonly customerName: string;
  public readonly customerEmail: string;
  public readonly customerPhone?: string;
  public readonly treatmentName: string;
  public readonly treatmentDuration: number;
  public readonly treatmentPrice: number;
  public readonly specialistId?: string;
  public readonly startTime: Date;
  public readonly endTime: Date;
  public readonly status: AppointmentStatus;
  public readonly notes?: string;
  public readonly createdAt: Date;
  public readonly updatedAt: Date;

  private constructor(props: IAppointment) {
    this.id = props.id || cuid();
    this.salonId = props.salonId;
    this.salonName = props.salonName;
    this.salonLogo = props.salonLogo;
    this.salonColor = props.salonColor;
    this.customerName = props.customerName;
    this.customerEmail = props.customerEmail;
    this.customerPhone = props.customerPhone;
    this.treatmentName = props.treatmentName;
    this.treatmentDuration = props.treatmentDuration;
    this.treatmentPrice = props.streatmentPrice;
    this.specialistId = props.specialistId;
    this.startTime = props.startTime;
    this.endTime = props.endTime;
    this.status = props.status || AppointmentStatus.PENDING;
    this.notes = props.notes;
    this.createdAt = props.createdAt || new Date();
    this.updatedAt = props.updatedAt || new Date();
  }

  static create(props: IAppointment): Appointment {
    return new Appointment(props);
  }

  // ... rest of methods remain the same, just renamed from Appointment to Appointment
}
```

#### Task 1.4: API Routes Refactoring (External System)

```typescript
// appointment-planner-backend/src/presentation/controllers/appointmentController.ts (Renamed from appointmentController.ts)
import { zValidator } from '@hono/zod-validator';
import type { Context } from 'hono';
import { z } from 'zod';
import type { AppointmentService } from '../../application/services/appointmentService';

const createAppointmentSchema = z.object({
  salonId: z.string(),
  salonName: z.string(),
  salonLogo: z.string().optional(),
  salonColor: z.string().optional(),
  customerName: z.string().min(1, 'Name is required'),
  customerEmail: z.string().email('Valid email is required'),
  customerPhone: z.string().optional(),
  treatmentName: z.string(),
  treatmentDuration: z.number().int().positive(),
  treatmentPrice: z.number().positive(),
  startTime: z.string().transform((val) => new Date(val)),
  endTime: z.string().transform((val) => new Date(val)),
  notes: z.string().optional(),
});

export class AppointmentController {
  constructor(private appointmentService: AppointmentService) {}

  // GET /api/appointments (renamed from /api/appointments)
  getAppointmentsBySalon = async (c: Context) => {
    const salonId = c.req.param('salonId');
    const appointments = await this.appointmentService.getAppointmentsByDate(
      salonId,
      new Date(),
    );
    return c.json(appointments);
  };

  // POST /api/appointments (renamed from /api/appointments)
  createAppointmentValidator = zValidator('json', createAppointmentSchema);
  createAppointment = async (c: ValidatedContext<AppointmentInput>) => {
    try {
      const formData = c.req.valid('json');
      const appointment = await this.appointmentService.createAppointment(formData);
      return c.json(appointment, 201);
    } catch (error) {
      if (error instanceof Error && error.message.includes('already booked')) {
        return c.json({ error: error.message }, 409);
      }
      return c.json({ error: 'Failed to create appointment' }, 500);
    }
  };

  // GET /api/appointments/available-slots (renamed from /api/appointments/available-slots)
  getAvailableTimeSlots = async (c: Context) => {
    // Implementation remains same, just endpoint renamed
  };
}
```

### Phase 2: Management System Refactoring

#### Task 2.1: File Renaming (Management System - PRIORITY)

```bash
# appointment-management-backend file renames
# Note: Most appointment-related files already have correct naming, 
# but we need to rename any "appointment" references

mv src/product-features/appointment/ src/product-features/appointments/
mv src/domain/services/AppointmentService.ts src/domain/services/AppointmentService.ts

# Update any remaining appointment-specific files
find src/ -name "*appointment*" -type f | while read file; do
  newname=$(echo "$file" | sed 's/appointment/appointment/g')
  mv "$file" "$newname"
done

# appointment-management-frontend file renames  
mv src/product-features/appointment/ src/product-features/appointments/
mv src/product-features/AppointmentCalendar.tsx src/product-features/AppointmentCalendar.tsx
mv src/product-features/AppointmentMain.tsx src/product-features/AppointmentMain.tsx

# Update service/hook names
mv src/product-features/appointment/useAppointmentContext.ts src/product-features/appointments/useAppointmentContext.ts
mv src/product-features/appointment/AppointmentContext.tsx src/product-features/appointments/AppointmentContext.tsx

# Test file renames
find tests/ -name "*appointment*" -type f | while read file; do
  newname=$(echo "$file" | sed 's/appointment/appointment/g')
  mv "$file" "$newname"
done

# Update CSS class files
mv src/platform-features/styles/calendar.css src/platform-features/styles/appointment-calendar.css
```

```typescript
// Automated import replacement script
const managementSystemReplacements = {
  // Directory updates
  'product-features/appointment': 'product-features/appointments',
  
  // Component updates  
  'AppointmentCalendar': 'AppointmentCalendar',
  'AppointmentMain': 'AppointmentMain',
  'AppointmentContext': 'AppointmentContext',
  'useAppointmentContext': 'useAppointmentContext',
  'useAppointmentData': 'useAppointmentData',
  
  // Service updates
  'AppointmentService': 'AppointmentService',
  'appointmentService': 'appointmentService',
  
  // CSS class updates
  'appointment-calendar': 'appointment-calendar',
  'appointment-main': 'appointment-main',
  'appointment-context': 'appointment-context',
};
```

#### Task 2.2: Schema Rename (Management System)

```sql
-- appointment-management-backend/prisma/migrations/rename_appointment_to_appointments.sql
-- Rename schema
ALTER SCHEMA "appointment" RENAME TO "appointments";

-- Update all references to the old schema name in constraints, indexes etc.
```

```prisma
// appointment-management-backend/prisma/schema.prisma (Updated)
model Appointment {
  id        String            @id @default(cuid())
  customerId  String
  staffId String
  treatmentId String
  startTime DateTime
  endTime   DateTime
  status    AppointmentStatus @default(SCHEDULED)
  notes     String?
  createdAt DateTime          @default(now())
  updatedAt DateTime          @updatedAt
  service   TreatmentOffering   @relation(fields: [treatmentId], references:treatmentId])
  stylist   Stylist           @relation(fields: [stylistId], references: [id])

  @@schema("appointments") // Renamed from "appointment"
}

model Stylist {
  id           String        @id @default(cuid())
  userId       String        @unique
  name         String
  email        String        @unique
  phone        String?
  specialties  String[]
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
  workHours    WorkHour[]
  appointments Appointment[]
  services     TreatmentOffering[]

  @@schema("appointments") // Renamed from "appointment"
}

// ... other models updated with new schema name
```

#### Task 2.3: API Routes Update (Management System)

```typescript
// appointment-management-backend/src/presentation/controllers/appointmentController.ts
// Update all route handlers to use consistent "appointment" terminology

export class AppointmentController {
  constructor(private appointmentService: AppointmentService) {}

  // GET /api/appointments (standardized)
  getAppointments = async (c: Context) => {
    const { stylistId, date, status } = c.req.query();
    const appointments = await this.appointmentService.findAppointments({
      stylistId,
      date: date ? new Date(date) : undefined,
      status,
    });
    return c.json(appointments);
  };

  // POST /api/appointments (standardized)
  createAppointment = async (c: Context) => {
    const appointmentData = await c.req.json();
    const appointment = await this.appointmentService.createAppointment(appointmentData);
    return c.json(appointment, 201);
  };

  // PUT /api/appointments/:id (standardized)
  updateAppointment = async (c: Context) => {
    const id = c.req.param('id');
    const updateData = await c.req.json();
    const appointment = await this.appointmentService.updateAppointment(id, updateData);
    return c.json(appointment);
  };

  // DELETE /api/appointments/:id (standardized)
  cancelAppointment = async (c: Context) => {
    const id = c.req.param('id');
    await this.appointmentService.cancelAppointment(id);
    return c.json({ success: true });
  };
}
```

### Phase 3: Cross-System Integration

#### Task 3.1: Update Import Statements (Both Systems)

```bash
# Automated script to update all import statements
# appointment-planner-backend
find src/ -name "*.ts" -type f -exec sed -i '' \
  -e 's/import.*appointment/import...appointment/g' \
  -e 's/from.*appointment/from...appointment/g' \
  -e 's/AppointmentService/AppointmentService/g' \
  -e 's/AppointmentRepository/AppointmentRepository/g' \
  -e 's/AppointmentController/AppointmentController/g' \
  {} \;

# appointment-planner-frontend  
find src/ -name "*.tsx" -name "*.ts" -type f -exec sed -i '' \
  -e 's/AppointmentForm/AppointmentForm/g' \
  -e 's/AppointmentCalendar/AppointmentCalendar/g' \
  -e 's/AppointmentSuccess/AppointmentSuccess/g' \
  -e 's/onAppointmentComplete/onAppointmentComplete/g' \
  {} \;

# appointment-management-backend
find src/ -name "*.ts" -type f -exec sed -i '' \
  -e 's/AppointmentService/AppointmentService/g' \
  -e 's/appointmentService/appointmentService/g' \
  -e 's/appointment\//appointments\//g' \
  {} \;

# appointment-management-frontend
find src/ -name "*.tsx" -name "*.ts" -type f -exec sed -i '' \
  -e 's/AppointmentCalendar/AppointmentCalendar/g' \
  -e 's/AppointmentMain/AppointmentMain/g' \
  -e 's/AppointmentContext/AppointmentContext/g' \
  -e 's/useAppointmentContext/useAppointmentContext/g' \
  -e 's/useAppointmentData/useAppointmentData/g' \
  -e 's/appointment\//appointments\//g' \
  {} \;
```

#### Task 3.2: External Frontend Refactoring

```typescript
// appointment-planner-frontend/src/components/AppointmentForm.tsx (Renamed from AppointmentForm.tsx)
export function AppointmentForm({
  salonId,
  salonName,
  startTime,
  onAppointmentComplete, // Renamed from onAppointmentComplete
}: AppointmentFormProps) {
  const [selectedDate, setSelectedDate] = useState<Date>(startTime);
  const [selectedTime, setSelectedTime] = useState<string>('');
  const [selectedTreatment, setSelectedTreatment] = useState<Treatment | null>(null);
  const [selectedSpecialist, setSelectedSpecialist] = useState<string>('any');
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
  });
  const [showSuccess, setShowSuccess] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    if (validateForm()) {
      const endTime = new Date(selectedDate);
      if (selectedTreatment) {
        endTime.setMinutes(endTime.getMinutes() + selectedTreatment.duration);
      }

      try {
        // Updated API endpoint
        const response = await fetch('/api/appointments', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            firstName: formData.firstName,
            lastName: formData.lastName,
            email: formData.email,
            phone: formData.phone,
            treatment: selectedTreatment?.id,
            treatmentName: selectedTreatment?.name,
            treatmentDuration: selectedTreatment?.duration,
            treatmentPrice: selectedTreatment?.price,
            specialist: selectedSpecialist,
            date: selectedDate,
            time: selectedTime,
            endTime: format(endTime, 'h:mm a'),
            salonId,
            salonName,
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Appointment creation failed');
        }

        setShowSuccess(true);
        onAppointmentComplete();

        setTimeout(() => {
          setFormData({ firstName: '', lastName: '', email: '', phone: '' });
          setSelectedTreatment(null);
          setSelectedSpecialist('any');
          setShowSuccess(false);
        }, 2000);
      } catch (error) {
        console.error('Appointment error:', error);
        if (error instanceof Error && error.message.includes('expired')) {
          setErrors({
            date: 'Selected date has expired. Please choose a new date.',
          });
        } else {
          setErrors({
            submit: 'Failed to create appointment. Please try again.',
          });
        }
      } finally {
        setIsSubmitting(false);
      }
    } else {
      setIsSubmitting(false);
    }
  };

  if (showSuccess) {
    return (
      <AppointmentSuccess
        message="Appointment confirmed!"
        autoHideAfter={2}
        data-testid="appointment-success-wrapper"
      />
    );
  }

  // ... rest of component
}
```

#### Task 3.3: Management Frontend Refactoring

```typescript
// appointment-management-frontend/src/components/appointments/AppointmentCalendar.tsx (Renamed from AppointmentCalendar.tsx)
export default function AppointmentCalendar() {
  const { appointments, isLoading } = useAppointmentData(); // Renamed from useAppointmentData
  
  const events = appointments.map(appointment => ({
    id: appointment.id,
    title: `${appointment.service?.name || 'Service'} - ${appointment.client?.name || 'Client'}`,
    start: appointment.startTime,
    end: appointment.endTime,
    backgroundColor: getAppointmentStatusColor(appointment.status),
    extendedProps: { appointment },
  }));

  return (
    <div className="appointment-calendar"> {/* Renamed from appointment-calendar */}
      <FullCalendar
        plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
        headerToolbar={{
          left: 'prev,next today',
          center: 'title',
          right: 'dayGridMonth,timeGridWeek,timeGridDay'
        }}
        initialView="timeGridWeek"
        events={events}
        eventClick={handleAppointmentClick} // Renamed from handleScheduleClick
        dateClick={handleDateClick}
        editable={true}
        selectable={true}
        selectMirror={true}
        dayMaxEvents={true}
        weekends={true}
        height="auto"
      />
    </div>
  );
}

// Updated routing structure
export function AppRoutes() {
  const routes = useRoutes([
    { path: '/', element: <AppointmentCalendar /> }, // Renamed from AppointmentCalendar
    { path: '/appointments', element: <AppointmentList /> },
    { path: '/appointments/new', element: <AppointmentForm /> },
    { path: '/appointments/:id', element: <AppointmentForm /> },
  ]);

  return routes;
}
```

### Phase 4: Testing & Validation

#### Task 4.1: Update Test Files and Test Data

```bash
# Update test file contents
# appointment-planner-backend tests
find src/tests/ -name "*.ts" -type f -exec sed -i '' \
  -e 's/appointment/appointment/g' \
  -e 's/Appointment/Appointment/g' \
  -e 's/AppointmentService/AppointmentService/g' \
  -e 's/double-appointment/double-appointment/g' \
  {} \;

# Update feature files
find src/tests/features/ -name "*.feature" -type f -exec sed -i '' \
  -e 's/appointment/appointment/g' \
  -e 's/Appointment/Appointment/g' \
  {} \;

# appointment-planner-frontend tests  
find tests/ -name "*.ts" -name "*.tsx" -type f -exec sed -i '' \
  -e 's/appointment/appointment/g' \
  -e 's/Appointment/Appointment/g' \
  -e 's/AppointmentForm/AppointmentForm/g' \
  -e 's/AppointmentCalendar/AppointmentCalendar/g' \
  {} \;

# appointment-management-frontend tests
find tests/ -name "*.ts" -name "*.tsx" -type f -exec sed -i '' \
  -e 's/appointment/appointment/g' \
  -e 's/Appointment/Appointment/g' \
  -e 's/AppointmentCalendar/AppointmentCalendar/g' \
  {} \;

# Update test data and mock files
find . -name "*mock*" -name "*test*" -type f -exec sed -i '' \
  -e 's/appointment/appointment/g' \
  -e 's/Appointment/Appointment/g' \
  -e 's/appointment/appointment/g' \
  -e 's/Appointment/Appointment/g' \
  {} \;
```

#### Task 4.2: Configuration and Environment Updates

```bash
# Update API endpoint configurations
# appointment-planner-frontend vite.config.ts, package.json
sed -i '' 's/\/api\/appointments/\/api\/appointments/g' vite.config.ts
sed -i '' 's/appointment/appointment/g' package.json

# Update documentation files
find . -name "*.md" -type f -exec sed -i '' \
  -e 's/appointment/appointment/g' \
  -e 's/Appointment/Appointment/g' \
  -e 's/\/api\/appointments/\/api\/appointments/g' \
  {} \;

# Update Docker and deployment configs
find . -name "docker-compose*.yml" -name "Dockerfile*" -type f -exec sed -i '' \
  -e 's/appointment/appointment/g' \
  -e 's/Appointment/Appointment/g' \
  {} \;

# Update environment variable names
find . -name ".env*" -type f -exec sed -i '' \
  -e 's/BOOKING_/APPOINTMENT_/g' \
  -e 's/appointment_/appointment_/g' \
  {} \;
```

```typescript
// Post-refactoring validation checklist
const validationChecklist = {
  // 1. Verify all files renamed correctly
  checkFileRenames: () => {
    // Ensure no old file names remain
    // Check import paths are updated
    // Verify test files updated
  },
  
  // 2. Database migrations successful
  checkDatabaseMigrations: () => {
    // Verify table renames completed
    // Check enum updates  
    // Validate schema consistency
  },
  
  // 3. API endpoints functional
  checkAPIEndpoints: () => {
    // Test /api/appointments endpoints
    // Verify external system integration
    // Check management system APIs
  },
  
  // 4. Frontend components working
  checkFrontendComponents: () => {
    // Verify AppointmentForm renders
    // Test AppointmentCalendar functionality
    // Check navigation routes
  },
  
  // 5. Tests passing
  checkTestSuite: () => {
    // Run unit tests
    // Execute integration tests
    // Verify E2E tests pass
  }
};

// Automated verification script
const runValidation = async () => {
  console.log('🔍 Running post-refactoring validation...');
  
  // Check for any remaining old terminology
  const oldTerms = ['appointment', 'Appointment', 'appointment', 'Appointment'];
  const foundIssues = await scanForOldTerminology(oldTerms);
  
  if (foundIssues.length > 0) {
    console.error('❌ Found remaining old terminology:', foundIssues);
    return false;
  }
  
  console.log('✅ Terminology unification complete!');
  return true;
};
```

## Testing Strategy

### Unit Tests
- Event publisher/consumer logic
- Data transformation functions
- Conflict detection algorithms
- Frontend component rendering with external appointments

### Integration Tests
- End-to-end appointment flow from external to management system
- Real-time synchronization
- Conflict resolution scenarios
- API gateway patterns

### E2E Tests
```typescript
// External appointment integration test
describe('External Appointment Integration', () => {
  it('should show external appointment in management calendar', async () => {
    // 1. Create appointment via external frontend
    await externalAppointmentPage.selectTimeSlot('2024-06-15', '14:00');
    await externalAppointmentPage.fillClientDetails({
      name: 'Jane Doe',
      email: '<EMAIL>',
    });
    await externalAppointmentPage.submitAppointment();

    // 2. Wait for synchronization
    await waitForSync();

    // 3. Verify appointment appears in management system
    await managementPage.navigateToCalendar();
    const appointment = await managementPage.findAppointmentByClient('Jane Doe');
    
    expect(appointment).toBeVisible();
    expect(appointment).toHaveClass('appointment-external');
  });
});
```

## Deployment Strategy

### Environment Configuration
```yaml
# External Backend
ENABLE_EVENT_PUBLISHING: true
EVENT_BUS_URL: "redis://event-bus:6379"
MANAGEMENT_WEBHOOK_URL: "https://management-api/webhooks/external-appointments"

# Management Backend  
ENABLE_EXTERNAL_INTEGRATION: true
EVENT_BUS_URL: "redis://event-bus:6379"
EXTERNAL_BOOKING_HANDLER_CONCURRENCY: 5

# Management Frontend
ENABLE_EXTERNAL_APPOINTMENTS: true
WEBSOCKET_URL: "wss://management-api/ws"
```

### Migration Plan
1. **Week 1:** File renaming and import updates (All Systems)
2. **Week 2:** Database schema migrations (Both backends)
3. **Week 3:** API endpoint updates and service refactoring
4. **Week 4:** Frontend component updates and routing
5. **Week 5:** Test updates and comprehensive validation
6. **Week 6:** Documentation updates and deployment

## Risk Mitigation

### Technical Risks
1. **Data inconsistency:** Implement eventual consistency with compensation patterns
2. **Event ordering:** Use event versioning and idempotent handlers
3. **Performance impact:** Implement caching and background processing
4. **Schema evolution:** Use event schema versioning

### Business Risks
1. **Double appointment:** Implement distributed locking mechanisms
2. **Customer confusion:** Clear UI indicators for external appointments
3. **Staff workflow disruption:** Comprehensive staff training and documentation

## Success Metrics
- 100% terminology consistency across both systems
- Zero broken imports or file references after refactoring
- All tests passing with updated terminology
- API endpoints responding correctly with new naming
- Documentation fully updated with appointment terminology

## Sprint 2 Deliverables

### Week 1: File Renaming (Foundation)
- [ ] External system file renames (appointment → appointment)
- [ ] Management system file renames (appointment → appointment)
- [ ] Import statement updates across all systems
- [ ] Directory structure reorganization

### Week 2: Database & Schema Updates
- [ ] Database table renames (appointments → appointments)
- [ ] Schema namespace updates (appointment → appointments)
- [ ] Enum and type definition updates
- [ ] Migration scripts and rollback procedures

### Week 3-4: Code & API Refactoring
- [ ] Service class renaming and refactoring
- [ ] API endpoint path updates (/appointments → /appointments)
- [ ] Frontend component updates
- [ ] Route and navigation updates

### Week 5: Testing & Validation
- [ ] Test file updates and terminology fixes
- [ ] Configuration and environment variable updates
- [ ] Comprehensive validation and cleanup
- [ ] End-to-end testing with new terminology

### Week 6: Documentation & Deployment
- [ ] Documentation updates (README, API docs, etc.)
- [ ] Deployment configuration updates
- [ ] Team training on new terminology
- [ ] Production deployment and monitoring

This Sprint 2 plan standardizes terminology across the entire system by systematically renaming files, updating code, and ensuring consistency between the external appointment system and internal management system, creating a unified domain language around "appointments". 