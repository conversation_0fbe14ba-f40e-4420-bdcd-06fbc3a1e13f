# 🚨 URGENT - <PERSON><PERSON> PERSONAS TEST SPRINT 6 NOW!

**RELEASE TODAY - NEED YOUR HELP IMMEDIATELY!**

---

## 🇳🇱 **MARIEKE VAN DER BERG** - Test as Dutch Salon Owner

**Task 1: Register Your Salon (5 mins)**
1. Use these details to test salon registration:
   - KvK Number: `12345678`
   - Salon Name: "Bella Beauty Amsterdam"
   - Email: "<EMAIL>"
   - Phone: "+31 20 123 4567"

**What to Check:**
- ✅ Everything in DUTCH language?
- ✅ KvK verification works?
- ✅ Business details auto-fill?
- ✅ You get "Geverifieerd bedrijf" badge?

**Task 2: Book Appointment as Dutch Customer (3 mins)**
1. Book "Knippen" (haircut) for tomorrow at 14:30
2. Use customer name: "<PERSON>"
3. Check email confirmation

**What to Check:**
- ✅ Date format DD-MM-YYYY?
- ✅ Time in 24-hour format?
- ✅ All Dutch beauty terms correct?
- ✅ Email in professional Dutch?

---

## 📊 **ELENA RODRIGUEZ** - Test Business Logic

**Task 1: Test KvK Validation (3 mins)**
Try these KvK numbers:
- ✅ `12345678` → Should work (beauty salon)
- ❌ `123456` → Should fail (wrong format)
- ❌ `87654321` → Should fail (tech company, not beauty)

**Task 2: Test Netherlands Holidays (2 mins)**
- Try to book on April 27, 2025 (Koningsdag) → Should be blocked
- Try to book on April 28, 2025 → Should work normally

**What to Report:**
- Error messages helpful?
- Performance good?
- User experience smooth?

---

## 💻 **DEV TEAM** - Run Tests Immediately

```bash
# Test Sprint 6 features
npm run test:cucumber:sprint6

# Check integration tests
cd services/appointment/appointment-planner-backend
npm test src/tests/sprint6/sprint6-integration.test.ts
```

**Check Database:**
```bash
# Start database
npm run prisma:studio

# Verify Dutch holidays loaded
# Verify KvK verification logs
```

---

## 🎨 **UX TEAM** - User Experience Check

**Quick UX Validation:**
1. **Language**: 100% Dutch text everywhere?
2. **Mobile**: Works on phone?
3. **Design**: Looks professional for Dutch market?
4. **Flow**: Easy to book appointment?

---

## 🚨 **REPORT BUGS IMMEDIATELY**

**Found a problem? Report in Slack:**
- Channel: `#urgent-sprint6-testing`
- Format: `🐛 BUG: [description] | SEVERITY: [Critical/High/Low]`

**Critical Bugs (STOP RELEASE):**
- English text on Dutch pages
- KvK verification broken
- Cannot book appointments
- System crashes

---

## ⏰ **TESTING DEADLINE: 2 HOURS**

**We launch TODAY - your testing is CRITICAL!**

**Status Check:**
- [ ] Marieke: Dutch salon owner experience ✅/❌
- [ ] Elena: Business validation ✅/❌  
- [ ] Dev Team: Technical tests ✅/❌
- [ ] UX Team: User experience ✅/❌

---

**START TESTING NOW! 🚀**

**— Dr. Sarah Mitchell, QA Lead** 

## Tasks

### Extracted Tasks

- [ ] KvK Number: `12345678` - M1
- [ ] Salon Name: "Bella Beauty Amsterdam" - M2
- [ ] Email: "<EMAIL>" - M3
- [ ] Phone: "+31 20 123 4567" - M4
- [ ] ✅ Everything in DUTCH language? - M5
- [ ] ✅ KvK verification works? - M6
- [ ] ✅ Business details auto-fill? - M7
- [ ] ✅ You get "Geverifieerd bedrijf" badge? - M8
- [ ] ✅ Date format DD-MM-YYYY? - M9
- [ ] ✅ Time in 24-hour format? - M10
- [ ] ✅ All Dutch beauty terms correct? - M11
- [ ] ✅ Email in professional Dutch? - M12
- [ ] ✅ `12345678` → Should work (beauty salon) - M13
- [ ] ❌ `123456` → Should fail (wrong format) - M14
- [ ] ❌ `87654321` → Should fail (tech company, not beauty) - M15
- [ ] Try to book on April 27, 2025 (Koningsdag) → Should be blocked - M16
- [ ] Try to book on April 28, 2025 → Should work normally - M17
- [ ] Error messages helpful? - M18
- [ ] Performance good? - M19
- [ ] User experience smooth? - M20
- [ ] Channel: `#urgent-sprint6-testing` - M21
- [ ] Format: `🐛 BUG: [description] | SEVERITY: [Critical/High/Low]` - M22
- [ ] English text on Dutch pages - M23
- [ ] KvK verification broken - M24
- [ ] Cannot book appointments - M25
- [ ] System crashes - M26
- [ ] Marieke: Dutch salon owner experience ✅/❌ - M27
- [ ] [ ] Marieke: Dutch salon owner experience ✅/❌ - M28
- [ ] Elena: Business validation ✅/❌ - M29
- [ ] [ ] Elena: Business validation ✅/❌ - M30
- [ ] Dev Team: Technical tests ✅/❌ - M31
- [ ] [ ] Dev Team: Technical tests ✅/❌ - M32
- [ ] UX Team: User experience ✅/❌ - M33
- [ ] [ ] UX Team: User experience ✅/❌ - M34

### Documentation Tasks

- [ ] Update content accuracy - M1
- [ ] Add code examples - M2
- [ ] Add diagrams/screenshots - M3
- [ ] Review and proofread - M4
- [ ] Add cross-references - M5

