# 🚨 URGENT - <PERSON><PERSON> PERSONAS TEST SPRINT 6 NOW!

**RELEASE TODAY - NEED YOUR HELP IMMEDIATELY!**

---

## 🇳🇱 **MARIEKE VAN DER BERG** - Test as Dutch Salon Owner

**Task 1: Register Your Salon (5 mins)**
1. Use these details to test salon registration:
   - KvK Number: `12345678`
   - Salon Name: "Bella Beauty Amsterdam"
   - Email: "<EMAIL>"
   - Phone: "+31 20 123 4567"

**What to Check:**
- ✅ Everything in DUTCH language?
- ✅ KvK verification works?
- ✅ Business details auto-fill?
- ✅ You get "Geverifieerd bedrijf" badge?

**Task 2: Book Appointment as Dutch Customer (3 mins)**
1. Book "Knippen" (haircut) for tomorrow at 14:30
2. Use customer name: "<PERSON>"
3. Check email confirmation

**What to Check:**
- ✅ Date format DD-MM-YYYY?
- ✅ Time in 24-hour format?
- ✅ All Dutch beauty terms correct?
- ✅ Email in professional Dutch?

---

## 📊 **ELENA RODRIGUEZ** - Test Business Logic

**Task 1: Test KvK Validation (3 mins)**
Try these KvK numbers:
- ✅ `12345678` → Should work (beauty salon)
- ❌ `123456` → Should fail (wrong format)
- ❌ `87654321` → Should fail (tech company, not beauty)

**Task 2: Test Netherlands Holidays (2 mins)**
- Try to book on April 27, 2025 (Koningsdag) → Should be blocked
- Try to book on April 28, 2025 → Should work normally

**What to Report:**
- Error messages helpful?
- Performance good?
- User experience smooth?

---

## 💻 **DEV TEAM** - Run Tests Immediately

```bash
# Test Sprint 6 features
npm run test:cucumber:sprint6

# Check integration tests
cd services/appointment/appointment-planner-backend
npm test src/tests/sprint6/sprint6-integration.test.ts
```

**Check Database:**
```bash
# Start database
npm run prisma:studio

# Verify Dutch holidays loaded
# Verify KvK verification logs
```

---

## 🎨 **UX TEAM** - User Experience Check

**Quick UX Validation:**
1. **Language**: 100% Dutch text everywhere?
2. **Mobile**: Works on phone?
3. **Design**: Looks professional for Dutch market?
4. **Flow**: Easy to book appointment?

---

## 🚨 **REPORT BUGS IMMEDIATELY**

**Found a problem? Report in Slack:**
- Channel: `#urgent-sprint6-testing`
- Format: `🐛 BUG: [description] | SEVERITY: [Critical/High/Low]`

**Critical Bugs (STOP RELEASE):**
- English text on Dutch pages
- KvK verification broken
- Cannot book appointments
- System crashes

---

## ⏰ **TESTING DEADLINE: 2 HOURS**

**We launch TODAY - your testing is CRITICAL!**

**Status Check:**
- [ ] Marieke: Dutch salon owner experience ✅/❌
- [ ] Elena: Business validation ✅/❌  
- [ ] Dev Team: Technical tests ✅/❌
- [ ] UX Team: User experience ✅/❌

---

**START TESTING NOW! 🚀**

**— Dr. Sarah Mitchell, QA Lead** 