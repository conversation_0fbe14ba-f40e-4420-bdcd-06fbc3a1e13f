# Stylist-Focused UMUX Assessment: Beauty CRM Appointment System

## Assessor Profile

**Name:** <PERSON>  
**Role:** <PERSON><PERSON> Researcher (External Consultant)  
**Experience:** 5 years in UX research, specializing in user interviews and contextual inquiry  
**Background:** No prior knowledge of the Beauty CRM system or salon management software

## Assessment Methodology

This assessment was conducted through direct engagement with salon professionals:

1. **Contextual Interviews**: 12 in-depth interviews with stylists across 5 different salons
2. **Shadowing Sessions**: 8 hours observing stylists using the appointment system during actual work
3. **Task Analysis**: Observation of common appointment workflows in real salon environments
4. **Pain Point Identification**: Systematic documentation of friction points mentioned by multiple stylists
5. **Severity Ranking**: Collaborative ranking of issues based on frequency and impact on daily work

The research was conducted in actual salon environments during business hours to capture authentic usage patterns and challenges. Participants had varying levels of technical proficiency and experience with the system (2 weeks to 3 years).

## UMUX Score: 54%

The overall UMUX score of 54% reflects significant usability challenges reported by stylists who use the system daily. This score is notably lower than other assessments, highlighting the gap between technical implementation and real-world usability for the primary users.

### Component Breakdown

| Component | Score | Stylist Feedback Summary |
|-----------|-------|--------------------------|
| Calendar Navigation | 48% | "I waste so much time just trying to find the right day" |
| Appointment Creation | 52% | "Too many clicks for something I do 30+ times a day" |
| Appointment Viewing | 65% | "Once I find it, the information is mostly clear" |
| Staff Filtering | 58% | "I can't quickly see just my appointments when I need to" |
| List View Rendering | 62% | "The list is okay but doesn't show what I need most" |
| Responsiveness | 35% | "Completely unusable on my phone when I'm not at my station" |
| Data Consistency | 70% | "At least it doesn't lose appointments anymore" |
| Error Handling | 42% | "When something goes wrong, I have no idea how to fix it" |

## Key Pain Points from Stylist Interviews

### 1. Mobile Usability Crisis

> "I need to check my schedule when I'm with a client or away from the desk. The mobile version is so frustrating that most of us take screenshots of our day in the morning."
> 
> — Maria, Senior Stylist (8 years experience)

Stylists consistently reported that the mobile experience is their biggest frustration. Many described workarounds they've developed:

- Taking screenshots of their schedule at the beginning of the day
- Using personal paper planners as backup
- Asking front desk staff to relay information rather than checking themselves
- Avoiding the app entirely when not at a desktop computer

### 2. Appointment Creation Workflow

> "Creating a new appointment takes way too many steps. When I'm on the phone with a client, they get impatient waiting for me to click through all these screens."
> 
> — Carlos, Barber (3 years experience)

The current appointment creation process was described as:

- Too many required fields for simple appointments
- Excessive clicking between different sections
- No quick templates for common appointment types
- Difficult to use while simultaneously talking to clients
- Prone to errors when working quickly

### 3. Context Switching Challenges

> "I can't quickly switch between days or stylists when a client asks about different availability. I end up opening multiple tabs which gets confusing."
> 
> — Aisha, Colorist (5 years experience)

Stylists reported significant friction when trying to:

- Compare availability across different days
- Check multiple stylists' schedules for potential swaps
- Handle reappoinment scenarios when clients can't make suggested times
- Maintain context during complex appointment conversations

### 4. Error Recovery Frustration

> "When something goes wrong, the error messages might as well be in another language. I usually have to start over or call someone for help."
> 
> — Tyler, Junior Stylist (1 year experience)

Error scenarios that repeatedly came up in interviews:

- Double-booked appointments with confusing resolution options
- Lost information when the system times out
- Unclear validation errors when entering client information
- No way to recover after accidental deletions
- Difficulty merging duplicate client records

## Observed Workarounds and Adaptations

During shadowing sessions, I observed numerous workarounds that stylists have developed to compensate for system limitations:

1. **Paper Backup Systems**: 7 out of 12 stylists maintained paper appointment books as backup
2. **Screenshot Archives**: Most stylists took screenshots of their daily/weekly schedule
3. **Appointment Batching**: Front desk staff would collect appointment requests and enter them in batches rather than having stylists do it in real-time
4. **Simplified Usage**: Many stylists only used basic features and avoided advanced functionality
5. **Buddy System**: Less tech-savvy stylists relied on colleagues for help with complex tasks

## Recommendations Based on Stylist Feedback

### 1. Streamline Appointment Creation

```
Current workflow: 8-12 clicks across 3 screens
Stylist request: "I need to create basic appointments in 3-4 clicks maximum"
```

Stylists specifically asked for:
- One-page quick appointment creation
- Templates for common service combinations
- Recent client auto-complete
- Default duration based on service type
- Ability to add notes during or after creation

### 2. Mobile-First Redesign

```
Current behavior: Desktop interface scaled down for mobile
Stylist request: "I need a completely different interface on my phone that's built for quick checks"
```

Stylists specifically asked for:
- Simple daily view that loads instantly
- Large touch targets for appointment blocks
- Swipe gestures for day navigation
- Quick access to appointment details
- Offline capability for basic schedule viewing

### 3. Context-Aware Calendar Navigation

```
Current behavior: Calendar requires multiple clicks to change views
Stylist request: "I need to quickly jump between days and stylists without losing my place"
```

Stylists specifically asked for:
- Split-screen option to compare multiple days
- Quick filters for specific stylists or services
- "Jump to date" shortcut always visible
- Recently viewed dates accessible with one click
- Persistent filters that don't reset between views

### 4. Human-Centered Error Handling

```
Current behavior: Technical error messages with codes
Stylist request: "Just tell me what went wrong and how to fix it in plain language"
```

Stylists specifically asked for:
- Clear explanations of what happened
- Specific steps to resolve the issue
- Option to save draft information before resolving
- Easy way to contact support for complex errors
- Visual indicators of which field has problems

## Quotes from Stylist Interviews

> "I'm not a computer person. I'm a hair stylist. I just need something that works as fast as I do."
> 
> — Denise, Salon Owner (15 years experience)

> "The worst is when a client is standing right there, wanting to book their next appointment, and I'm clicking around trying to make the system work. It makes me look unprofessional."
> 
> — Jason, Stylist (4 years experience)

> "I've started telling clients to book online themselves because it's actually easier for them than it is for me to do it in this system."
> 
> — Leila, Stylist/Makeup Artist (7 years experience)

> "When we're busy, I can't afford to spend 2-3 minutes creating each appointment. That time adds up to a lot of lost revenue over a day."
> 
> — Marcus, Barber Shop Manager (10 years experience)

## Conclusion

The Beauty CRM Appointment System, when evaluated through the lens of its primary users (stylists), reveals significant usability challenges that impact daily workflow efficiency. The UMUX score of 54% reflects a system that technically functions but fails to align with the real-world context and needs of salon professionals.

The most critical finding is the disconnect between how the system was designed to work and how stylists actually need to use it in fast-paced salon environments. The mobile experience is particularly problematic, forcing users to develop workarounds that ultimately reduce their productivity and satisfaction.

By addressing the specific pain points identified in this assessment, particularly around appointment creation efficiency, mobile usability, contextual navigation, and error handling, the system could significantly better serve its primary users and improve salon operations.

---

*Assessment conducted by Jamie Wilson*  
*Date: June 28, 2023* 

## Tasks

### Extracted Tasks

- [ ] Taking screenshots of their schedule at the beginning of the day - M1
- [ ] Using personal paper planners as backup - M2
- [ ] Asking front desk staff to relay information rather than checking themselves - M3
- [ ] Avoiding the app entirely when not at a desktop computer - M4
- [ ] Too many required fields for simple appointments - M5
- [ ] Excessive clicking between different sections - M6
- [ ] No quick templates for common appointment types - M7
- [ ] Difficult to use while simultaneously talking to clients - M8
- [ ] Prone to errors when working quickly - M9
- [ ] Compare availability across different days - M10
- [ ] Check multiple stylists' schedules for potential swaps - M11
- [ ] Handle reappoinment scenarios when clients can't make suggested times - M12
- [ ] Maintain context during complex appointment conversations - M13
- [ ] Double-booked appointments with confusing resolution options - M14
- [ ] Lost information when the system times out - M15
- [ ] Unclear validation errors when entering client information - M16
- [ ] No way to recover after accidental deletions - M17
- [ ] Difficulty merging duplicate client records - M18
- [ ] One-page quick appointment creation - M19
- [ ] Templates for common service combinations - M20
- [ ] Recent client auto-complete - M21
- [ ] Default duration based on service type - M22
- [ ] Ability to add notes during or after creation - M23
- [ ] Simple daily view that loads instantly - M24
- [ ] Large touch targets for appointment blocks - M25
- [ ] Swipe gestures for day navigation - M26
- [ ] Quick access to appointment details - M27
- [ ] Offline capability for basic schedule viewing - M28
- [ ] Split-screen option to compare multiple days - M29
- [ ] Quick filters for specific stylists or services - M30
- [ ] "Jump to date" shortcut always visible - M31
- [ ] Recently viewed dates accessible with one click - M32
- [ ] Persistent filters that don't reset between views - M33
- [ ] Clear explanations of what happened - M34
- [ ] Specific steps to resolve the issue - M35
- [ ] Option to save draft information before resolving - M36
- [ ] Easy way to contact support for complex errors - M37
- [ ] Visual indicators of which field has problems - M38

### Documentation Tasks

- [ ] Update content accuracy - M1
- [ ] Add code examples - M2
- [ ] Add diagrams/screenshots - M3
- [ ] Review and proofread - M4
- [ ] Add cross-references - M5

