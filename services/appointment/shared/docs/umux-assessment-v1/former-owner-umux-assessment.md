# UMUX Assessment by <PERSON> (34-year-old Junior <PERSON><PERSON> Researcher, Former Salon Owner)

## Researcher Background

I'm <PERSON>, a 34-year-old junior UX researcher who owned and operated "Tresses & Textures" salon for 8 years before transitioning to UX research. My unique perspective combines practical salon management experience with formal UX training. I've been asked to evaluate this Beauty CRM appointment system with a focus on how it addresses real-world salon operational challenges.

## Testing Methodology

I conducted a hybrid evaluation combining:
- Task-based usability testing (performing common salon appointment tasks)
- Contextual inquiry (imagining myself back in my salon environment)
- Heuristic evaluation (applying Nielsen's usability heuristics)
- UMUX scoring (quantifying usability across key components)

I tested on desktop (where most salon appointment happens during quiet periods) and mobile (used during busy times or after hours).

## UMUX Score and Component Breakdown

My overall UMUX score for the system is **69%**, which falls short of the 100% target. This score reflects my assessment as someone who has experienced the daily challenges of salon appointment firsthand.

| Component | My Score | Salon Owner Perspective |
|-----------|----------|-------------------------|
| Calendar Navigation | 65% | Critical for quickly finding available slots while on the phone with clients |
| Appointment Creation | 70% | Must be fast and error-proof, especially during busy periods |
| Appointment Viewing | 75% | Needs to show all relevant details at a glance |
| Staff Filtering | 85% | Essential for managing multiple stylists with different specialties |
| List View Rendering | 70% | Important for daily planning and preparation |
| Responsiveness | 45% | Must work seamlessly on all devices, especially when moving around the salon |
| Data Consistency | 80% | Critical for avoiding double-appointments and conflicts |
| Error Handling | 60% | Must prevent common appointment mistakes that cost real money |

## Detailed Findings

### Strengths from a Salon Owner's Perspective

1. **Staff Filtering (85%)**: The ability to filter by staff member is well-implemented and addresses a key need for multi-stylist salons. This was one of my biggest pain points as an owner.

2. **Data Consistency (80%)**: The system maintains consistent information across views, which is crucial for preventing double-appointments that can damage client relationships.

3. **Appointment Viewing (75%)**: The basic display of appointment information is relatively clear, though it could show more service details at a glance.

4. **Color Coding**: The use of color to distinguish appointment types helps with quick visual scanning during busy periods.

### Areas Needing Improvement

#### 1. Responsiveness (45%)
As a salon owner, I was constantly moving between the front desk, styling stations, and back office. The mobile experience is severely lacking:

- The calendar view is nearly unusable on phones, which is problematic when checking availability while with a client
- Touch targets are too small for quick interactions, especially with wet or product-covered hands
- The interface doesn't adapt well to different screen sizes, making it frustrating to use on tablets at styling stations

#### 2. Error Handling (60%)
In a salon, appointment errors directly impact revenue and client satisfaction:

- The system doesn't proactively prevent common mistakes like double-appointment a stylist
- Error messages don't clearly explain how to fix issues
- No warnings about potential conflicts (like appointment a color service too close to another appointment)
- No safeguards against appointment incompatible services back-to-back

#### 3. Calendar Navigation (65%)
Efficient navigation is essential when appointment appointments with clients on the phone:

- No quick way to jump to specific dates when appointment advance appointments
- Switching between day/week/month views is cumbersome
- No visual indicators for high-demand time slots
- Can't easily see multiple stylists' availability side-by-side

#### 4. Appointment Creation (70%)
The appointment creation process needs to be lightning-fast in a busy salon environment:

- Too many required fields slow down the appointment process
- No quick templates for common service combinations (like "cut and color")
- Can't easily duplicate recurring appointments for regular clients
- No client history integration to inform appointment decisions

## Real-World Salon Scenarios That Highlight Issues

### Scenario 1: The Phone is Ringing
When a client calls to book an appointment, the receptionist needs to quickly find available slots. Currently, this requires too many clicks and doesn't allow for quick comparison between stylists.

**Impact**: Longer call times, frustrated clients, and potential lost appointments.

### Scenario 2: The Walk-In Client
When a walk-in client requests a service, staff need to immediately check availability on a mobile device while standing with the client. The poor mobile experience makes this interaction awkward and unprofessional.

**Impact**: Decreased client confidence and potential lost revenue.

### Scenario 3: The Double-Booked Disaster
Without clear conflict detection, it's too easy to double-book a stylist or station. I experienced this regularly with previous systems, and it creates significant stress and client dissatisfaction.

**Impact**: Unhappy clients, stressed staff, and potential revenue loss.

### Scenario 4: End-of-Day Planning
Salon managers typically review the next day's schedule before closing. The current list view doesn't provide enough information for proper preparation.

**Impact**: Inadequate preparation for upcoming appointments and potential supply shortages.

## Recommendations Based on Salon Experience

### Immediate Operational Improvements

1. **Add Quick-Book Templates**: Create templates for common service combinations that can be applied with 2-3 clicks.

2. **Implement Conflict Detection**: Actively prevent double-appointments and flag potential timing conflicts during the appointment process.

3. **Optimize Mobile Experience**: Completely redesign the mobile interface with larger touch targets and simplified views for on-the-floor use.

4. **Add "Jump to Date" Feature**: Enable quick navigation to future dates for advance appointments.

### Medium-Term Enhancements

1. **Client History Integration**: Show previous services and stylist preferences during appointment.

2. **Resource Management**: Add station/room assignment to prevent overappointment physical resources.

3. **Time Block Visualization**: Use color intensity to show high-demand periods at a glance.

4. **Multi-Stylist Comparison**: Enable side-by-side availability comparison for multiple staff members.

## Conclusion: The Salon Owner's Perspective

As someone who has experienced the chaos of salon appointment firsthand, I believe this system has a solid foundation but falls short in critical areas that impact daily salon operations. The current UMUX score of 69% reflects a system that would create friction in a real-world salon environment.

The most urgent improvements relate to mobile responsiveness, error prevention, and streamlining the appointment process. These changes would not only improve the UMUX score but would directly impact salon efficiency, client satisfaction, and ultimately, revenue.

With my combined experience as both a salon owner and UX researcher, I can confidently say that addressing these issues would transform this from a functional system to an exceptional tool that truly understands the unique challenges of salon appointment. 

## Tasks

### Extracted Tasks

- [ ] Task-based usability testing (performing common salon appointment tasks) - M1
- [ ] Contextual inquiry (imagining myself back in my salon environment) - M2
- [ ] Heuristic evaluation (applying Nielsen's usability heuristics) - M3
- [ ] UMUX scoring (quantifying usability across key components) - M4
- [ ] The calendar view is nearly unusable on phones, which is problematic when checking availability while with a client - M5
- [ ] Touch targets are too small for quick interactions, especially with wet or product-covered hands - M6
- [ ] The interface doesn't adapt well to different screen sizes, making it frustrating to use on tablets at styling stations - M7
- [ ] The system doesn't proactively prevent common mistakes like double-appointment a stylist - M8
- [ ] Error messages don't clearly explain how to fix issues - M9
- [ ] No warnings about potential conflicts (like appointment a color service too close to another appointment) - M10
- [ ] No safeguards against appointment incompatible services back-to-back - M11
- [ ] No quick way to jump to specific dates when appointment advance appointments - M12
- [ ] Switching between day/week/month views is cumbersome - M13
- [ ] No visual indicators for high-demand time slots - M14
- [ ] Can't easily see multiple stylists' availability side-by-side - M15
- [ ] Too many required fields slow down the appointment process - M16
- [ ] No quick templates for common service combinations (like "cut and color") - M17
- [ ] Can't easily duplicate recurring appointments for regular clients - M18
- [ ] No client history integration to inform appointment decisions - M19

### Documentation Tasks

- [ ] Update content accuracy - M1
- [ ] Add code examples - M2
- [ ] Add diagrams/screenshots - M3
- [ ] Review and proofread - M4
- [ ] Add cross-references - M5

