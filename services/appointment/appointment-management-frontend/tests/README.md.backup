# Testing in Appointment Management Frontend

## Overview
This project uses a comprehensive testing strategy with multiple testing approaches:
- Component Testing (React Testing Library)
- End-to-End Testing (Playwright)
- Behavior-Driven Development (Cucumber)

## Test Types

### 1. Component Tests
- Framework: React Testing Library
- Focus: Individual component behavior
- Scope: Unit and integration testing
- Run with: `npm run test:bdd:react`

### 2. End-to-End Tests
- Framework: Playwright
- Focus: Full user journey and system integration
- Scope: Complete application flow
- Run with: `npm run test:bdd:e2e`

## Test Scenarios

### Appointment Creation
1. **Happy Path**
   - Successfully create a new appointment
   - Verify appointment appears in list view

2. **Edge Cases**
   - Attempt to book an already booked time slot
   - Validate error handling

3. **Validation**
   - Test input validation for client details
   - Verify error messages for invalid inputs

## Running Tests

### Component Tests
```bash
npm run test:bdd:react
```

### E2E Tests
```bash
npm run test:bdd:e2e
```

### Debug Mode
```bash
npm run test:bdd:debug
```

### Generate Reports
```bash
npm run test:bdd:report
```

## Test Configuration

### Cucumber
- Uses feature files with <PERSON><PERSON><PERSON> syntax
- Supports both React and Playwright testing
- Generates JSON and HTML reports

### Playwright
- Browser automation
- Cross-browser testing
- Detailed tracing and video recording

### React Testing Library
- Component-level testing
- Simulates user interactions
- Focuses on accessibility and user experience

## Best Practices
- Keep tests independent
- Use stable selectors (`data-testid`)
- Cover happy paths and edge cases
- Maintain clear, descriptive scenarios

## Continuous Integration
- Tests run automatically in CI/CD pipeline
- Generates test reports and artifacts
- Blocks deployments with failing tests

## Troubleshooting
- Check `test-results/` for logs and reports
- Use debug scripts for detailed output
- Ensure all dependencies are installed

# Appointment Management Frontend Testing

## UMUX (Usability Metric for User Experience) Testing

This directory contains automated tests to measure the UMUX score of our appointment management application. UMUX is a standardized usability metric that helps quantify the user experience.

### What is UMUX?

UMUX is a simplified usability scale consisting of two items:
1. "[This system's] capabilities meet my requirements."
2. "[This system] is easy to use."

In a traditional UMUX evaluation, users rate these statements on a 7-point scale from "Strongly Disagree" to "Strongly Agree". We've adapted this approach for automated testing by measuring key user tasks and weighting them appropriately.

### Automated UMUX Assessment

Our approach maps core appointment application functionality to the two UMUX dimensions:

1. **System Capabilities** (mapped to key functional tests):
   - Appointment creation
   - Appointment viewing
   - Calendar navigation
   - Staff filtering

2. **Ease of Use** (mapped to usability and interface tests):
   - List view rendering
   - Responsive behavior
   - UI component accessibility
   - Navigation efficiency

### Running the Tests

To run the UMUX tests:

```bash
# Run all tests
npm test

# Run only UMUX tests
npm test -- --grep "UMUX"

# Run with visible browser
npm test -- --headed
```

### Interpreting Results

UMUX scores are reported on a 0-100 scale:

- 90-100: Exceptional
- 80-89: Excellent
- 70-79: Good
- 60-69: Above Average
- 50-59: Average
- Below 50: Poor

Our continuous integration pipeline requires a minimum UMUX score of 90 to pass.

### Extending the Tests

When adding new features to the appointment application, consider:

1. Adding corresponding test coverage in the UMUX test suite
2. Appropriately weighting the new tests
3. Ensuring the weights continue to sum to 100%

## Manual UMUX Assessment

For a complete evaluation, supplement automated testing with manual UMUX surveys asking users to rate:

1. "The appointment system's capabilities meet my requirements."
2. "The appointment system is easy to use."

This provides a more comprehensive picture of real user experience alongside our automated metrics.

# Behavior-Driven Development (BDD) Testing

## Overview
This directory contains Behavior-Driven Development (BDD) tests for the Appointment Management Frontend using Cucumber.

### Test Types
- **Component Tests**: Using React Testing Library (unit/integration level)
- **E2E Tests**: Using Playwright for end-to-end testing

### Directory Structure
- `features/`: Contains `.feature` files describing application behaviors
- `step-definitions/`: Contains TypeScript files implementing step definitions
  - `world.ts`: Manages test context and browser setup for E2E tests
- `support/`: Contains supporting files for test configuration and utilities

### Running Tests

#### Component BDD Tests
```bash
npm run test:bdd
```

#### E2E BDD Tests
```bash
npm run test:bdd:e2e
```

#### Debug BDD Tests
```bash
npm run test:bdd:debug
```

#### Generate HTML Report
```bash
npm run test:bdd:report
```

### Test Tagging
- `@e2e`: End-to-end tests
- `@smoke`: Critical path tests
- `@happy-path`: Successful scenario tests
- `@edge-case`: Boundary condition tests
- `@validation`: Input validation tests
- `@debug`: Tests for debugging purposes

### Writing Feature Files
- Use Gherkin syntax (Given, When, Then)
- Focus on describing behavior from a user's perspective
- Keep scenarios concise and focused

### Writing Step Definitions
- **Component Tests**: Use `@testing-library/react`
- **E2E Tests**: Use Playwright with stable selectors
- Leverage Vitest for assertions

### Best Practices
- Keep step definitions DRY (Don't Repeat Yourself)
- Use meaningful and descriptive scenario names
- Aim for readability and maintainability

### Debugging
- Use `@debug` tag for specific scenarios
- Check `test-results/` for detailed logs, reports, and artifacts

### E2E Testing Considerations
- Tests use stable `data-testid` selectors
- Each scenario starts with a clean browser context
- Error handling and logging are built-in
- Video recording available in CI environments

## Continuous Integration
Tests are integrated into the CI/CD pipeline to ensure:
- Code quality
- Functional correctness
- Performance stability 