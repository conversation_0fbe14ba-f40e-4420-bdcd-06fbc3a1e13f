# E2E Tests for Appointment Management Frontend

## Overview

This directory contains comprehensive end-to-end tests for the appointment management frontend, with a focus on AI-friendly automation and testing practices.

## File Structure

```
tests/e2e/
├── README.md                              # This file
├── ai-friendly-form-tests.spec.ts         # AI-optimized form interaction tests
├── sprint1-mobile-experience.spec.ts      # Sprint 1 mobile experience tests  
├── sprint1-mobile-experience-mcp.spec.ts  # MCP-based mobile experience tests
├── debug-page-structure.spec.ts           # Page structure debugging tests
├── features/                             # Cucumber feature files
├── steps/                               # Cucumber step definitions
└── support/                            # Test utilities and helpers
```

## AI-Friendly Improvements

### Key Features

1. **Descriptive Data-TestID Attributes**
   - All interactive elements have unique `data-testid` attributes
   - Naming convention: `{component}-{type}-{purpose}`
   - Example: `service-select-dropdown`, `client-name-input-field`

2. **Enhanced Accessibility**
   - `aria-label` attributes for screen readers and AI context
   - `role` and `aria-live` attributes for dynamic content
   - Proper form validation with accessible error messages

3. **Visual Indicators**
   - Emojis provide visual context for element purpose
   - State indicators (✅ Available, 🚫 Booked, ❌ Error)
   - Clear visual hierarchy with grouped sections

4. **Semantic Structure**
   - Hierarchical form organization with clear grouping
   - Consistent error message formatting
   - State-aware option labeling

## Test Categories

### AI-Friendly Form Tests (`ai-friendly-form-tests.spec.ts`)

Comprehensive tests demonstrating AI automation capabilities:

- **Service Dropdown Interaction**: Tests `data-testid="service-select-dropdown"`
- **Staff Selection**: Tests `data-testid="staff-select-dropdown"`  
- **Time Slot Selection**: Tests `data-testid="timeslot-select-dropdown"`
- **Client Information**: Tests all input fields with proper selectors
- **Form Actions**: Tests confirm/cancel buttons with clear identifiers
- **Validation Errors**: Tests error message targeting and accessibility
- **Form Structure**: Tests semantic organization and grouping

### Mobile Experience Tests

- **Responsive Layout**: Mobile viewport testing (375x812px)
- **Touch Targets**: 44x44px minimum size validation  
- **Navigation**: Tab switching between Calendar and List views
- **Performance**: Performance monitoring and metrics
- **Accessibility**: Screen reader and keyboard navigation

### MCP-Based Tests

- **Browser Automation**: Using MCP server for reliable testing
- **Visual Verification**: Screenshot-based validation
- **Form Interaction**: Direct form manipulation and validation
- **State Management**: Form state progression testing

## Key Selectors for AI Automation

### Form Elements

```javascript
// Main form container
'[data-testid="appointment-form-container"]'

// Form sections
'[data-testid="service-form-group"]'
'[data-testid="staff-form-group"]'  
'[data-testid="client-details-section"]'
'[data-testid="form-actions-section"]'

// Dropdowns
'select[data-testid="service-select-dropdown"]'
'select[data-testid="staff-select-dropdown"]'
'select[data-testid="timeslot-select-dropdown"]'

// Input fields
'input[data-testid="client-name-input-field"]'
'input[data-testid="client-phone-input-field"]'
'input[data-testid="client-email-input-field"]'

// Action buttons
'button[data-testid="new-appointment-button"]'
'button[data-testid="confirm-appointment-button"]'
'button[data-testid="cancel-appointment-button"]'

// Error messages
'[data-testid="service-error-message"]'
'[data-testid="staff-error-message"]'
'[data-testid="client-name-error-message"]'

// Success indicators
'[data-testid="appointment-confirmation-message"]'
```

### Labels and Headers

```javascript
// Form labels with emojis for context
'[data-testid="service-label"]'        // 🛍️ Service
'[data-testid="staff-label"]'          // 👤 Staff Member  
'[data-testid="timeslot-label"]'       // 🕐 Time Slot
'[data-testid="client-name-label"]'    // 📝 Client Name
'[data-testid="client-phone-label"]'   // 📞 Phone Number
'[data-testid="client-email-label"]'   // 📧 Email Address

// Section headers
'[data-testid="form-header"]'          // 📅 Create New Appointment
'[data-testid="client-details-section"]' // 👥 Client Information
```

## Running Tests

### Prerequisites

```bash
cd services/appointment/appointment-management-frontend
bun install
```

### Start Development Server

```bash
bun run dev:direct
# Server runs on http://localhost:5006/
```

### Run Tests

```bash
# Run all E2E tests
npx playwright test tests/e2e/

# Run specific test file
npx playwright test tests/e2e/ai-friendly-form-tests.spec.ts

# Run with headed browser (visible)
npx playwright test tests/e2e/ai-friendly-form-tests.spec.ts --headed

# Run specific test
npx playwright test -g "AI can easily identify and interact with service dropdown"
```

### Using MCP Server

For more reliable testing, especially during development:

1. Start MCP server (if available)
2. Use MCP commands for direct browser control
3. Better for visual verification and debugging

## Best Practices

### For AI Automation

1. **Use Semantic Selectors**: Always prefer `data-testid` over class names or XPath
2. **Check Accessibility**: Use `aria-label` attributes for element context
3. **Verify State**: Check element states before interaction
4. **Handle Errors**: Use proper error message selectors
5. **Visual Confirmation**: Take screenshots for verification

### For Test Maintenance

1. **Keep Selectors Stable**: Don't change `data-testid` values unnecessarily
2. **Document Changes**: Update this README when adding new selectors
3. **Group Related Tests**: Organize tests by functionality
4. **Use Page Objects**: Consider POM for complex interactions

## Troubleshooting

### Common Issues

1. **Element Not Found**: Check if `data-testid` exists in the component
2. **Timeout Errors**: Increase wait times for slow operations
3. **Form State**: Ensure form is in correct state before interaction
4. **Navigation**: Wait for navigation completion before assertions

### Debugging

1. **Visual Debugging**: Use `--headed` flag to see browser
2. **Screenshots**: Take screenshots at key steps
3. **Console Logs**: Check browser console for errors
4. **Element Inspector**: Use browser dev tools to verify selectors

## Related Documentation

- [AI-Friendly Improvements Guide](../../shared/docs/ai-friendly-improvements.md)
- [Playwright Configuration](../../playwright.config.ts)
- [Component Source Code](../../src/product-features/appointment/)

## Contributing

When adding new tests:

1. Follow the existing naming conventions for `data-testid`
2. Add appropriate `aria-label` attributes for accessibility
3. Include visual indicators (emojis) where helpful
4. Update this README with new selectors
5. Test with both traditional Playwright and MCP methods 

## Tasks

### Extracted Tasks

- [ ] All interactive elements have unique `data-testid` attributes - M1
- [ ] Naming convention: `{component}-{type}-{purpose}` - M2
- [ ] Example: `service-select-dropdown`, `client-name-input-field` - M3
- [ ] `aria-label` attributes for screen readers and AI context - M4
- [ ] `role` and `aria-live` attributes for dynamic content - M5
- [ ] Proper form validation with accessible error messages - M6
- [ ] Emojis provide visual context for element purpose - M7
- [ ] State indicators (✅ Available, 🚫 Booked, ❌ Error) - M8
- [ ] Clear visual hierarchy with grouped sections - M9
- [ ] Hierarchical form organization with clear grouping - M10
- [ ] Consistent error message formatting - M11
- [ ] State-aware option labeling - M12
- [ ] **Service Dropdown Interaction**: Tests `data-testid="service-select-dropdown"` - M13
- [ ] **Staff Selection**: Tests `data-testid="staff-select-dropdown"` - M14
- [ ] **Time Slot Selection**: Tests `data-testid="timeslot-select-dropdown"` - M15
- [ ] **Client Information**: Tests all input fields with proper selectors - M16
- [ ] **Form Actions**: Tests confirm/cancel buttons with clear identifiers - M17
- [ ] **Validation Errors**: Tests error message targeting and accessibility - M18
- [ ] **Form Structure**: Tests semantic organization and grouping - M19
- [ ] **Responsive Layout**: Mobile viewport testing (375x812px) - M20
- [ ] **Touch Targets**: 44x44px minimum size validation - M21
- [ ] **Navigation**: Tab switching between Calendar and List views - M22
- [ ] **Performance**: Performance monitoring and metrics - M23
- [ ] **Accessibility**: Screen reader and keyboard navigation - M24
- [ ] **Browser Automation**: Using MCP server for reliable testing - M25
- [ ] **Visual Verification**: Screenshot-based validation - M26
- [ ] **Form Interaction**: Direct form manipulation and validation - M27
- [ ] **State Management**: Form state progression testing - M28
- [ ] [AI-Friendly Improvements Guide](../../shared/docs/ai-friendly-improvements.md) - M29
- [ ] [Playwright Configuration](../../playwright.config.ts) - M30
- [ ] [Component Source Code](../../src/product-features/appointment/) - M31

### Frontend Tasks

- [ ] Implement UI components - M1
- [ ] Add form validation - M2
- [ ] Add error handling - M3
- [ ] Add loading states - M4
- [ ] Add unit tests - M5
- [ ] Add accessibility features - M6

