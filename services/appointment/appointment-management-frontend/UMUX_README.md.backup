# Beauty CRM - Appointment Management UMUX Focus

This module implements a appointment and appointment management system with a focus on achieving perfect 100% UMUX (Usability Metric for User Experience) scores.

## 🎯 UMUX-Focused Development

Our development approach prioritizes usability through automated metrics and continuous testing:

1. **Automated UMUX Measurement**: We use <PERSON><PERSON> to measure UMUX across key dimensions
2. **Continuous Testing**: CI/CD pipeline ensures UMUX maintains 100% score
3. **User-Centered Design**: All features are developed with usability as the primary focus

## 🚀 Getting Started

### Prerequisites

- Node.js 18+ or Bun 1.0+
- <PERSON>wright installed for UMUX testing

### Install Dependencies

```bash
bun install
# Or with npm
npm install
```

### Start Development Server

```bash
bun dev
# Or
npm run dev
```

### Run UMUX Tests

```bash
bun test:umux
# Or
npm run test:umux
```

## 📊 Understanding Our UMUX Score

The UMUX (Usability Metric for User Experience) score for our appointment management application is calculated based on the following components and their weights:

| Component | Weight | Description |
|-----------|--------|-------------|
| Calendar Navigation | 15% | Testing the ability to navigate between different calendar views (day, week, month) and dates |
| Appointment Creation | 25% | Testing the complete flow of creating a new appointment |
| Appointment Viewing | 15% | Testing the ability to view appointment details |
| Staff Filtering | 10% | Testing the ability to filter appointments by staff member |
| List View Rendering | 10% | Testing the rendering of appointments in list view |
| Responsiveness | 10% | Testing the application's responsiveness across different device sizes |
| Data Consistency | 10% | Testing data consistency between different views |
| Error Handling | 5% | Testing form validation and error handling |

## 📁 Project Structure

```
services/appointment/appointment-management-frontend/
├── src/
│   ├── components/
│   │   ├── AppointmentForm.tsx   # Appointment details form
│   │   ├── AppointmentList.tsx   # List of appointments
│   │   └── AppointmentCalendar.tsx # Main calendar component
│   ├── styles/
│   │   └── calendar.css          # Calendar styling
│   ├── App.tsx                   # Root component
│   └── main.tsx                  # Entry point
├── tests/
│   ├── e2e/
│   │   └── appointment-umux.spec.ts # UMUX tests
│   ├── utils/
│   │   ├── page-objects/           # Page object models
│   │   └── umux-calculator.ts      # UMUX score calculator
│   ├── README.md                   # Testing documentation
│   └── UMUX_MANUAL.md              # UMUX testing guide
├── .github/
│   └── workflows/
│       └── umux-tests.yml          # CI/CD for UMUX
└── playwright.config.ts            # Playwright configuration
```

## 📋 Development Guidelines

To maintain our 100% UMUX score, please follow these guidelines when making changes to the codebase:

1. **Add data-testid attributes to all interactive elements**:
   - All buttons, inputs, and other interactive elements should have a `data-testid` attribute
   - Use the existing naming conventions for data-testid attributes

2. **Ensure all components render correctly across different device sizes**:
   - Test your changes on mobile, tablet, and desktop viewports
   - Use responsive design principles

3. **Maintain form validation**:
   - All forms should validate user input
   - Error messages should be clear and helpful

4. **Test across all supported browsers**:
   - Chrome
   - Firefox
   - Safari

## 🔬 UMUX Testing Methodology

Our UMUX testing follows these principles:

1. **User-centric testing**: Tests are designed to simulate real user interactions
2. **Comprehensive coverage**: Tests cover all key functionality of the application
3. **Reproducible results**: Tests are automated and produce consistent results
4. **Clear pass/fail criteria**: Each test has a clear pass/fail criteria
5. **Zero tolerance for failures**: We maintain a 100% UMUX score at all times

## 📚 Additional Resources

- [Full UMUX Testing Manual](./tests/UMUX_MANUAL.md)
- [Page Object Model Documentation](./tests/utils/page-objects/README.md)
- [Playwright Best Practices](https://playwright.dev/docs/best-practices) 