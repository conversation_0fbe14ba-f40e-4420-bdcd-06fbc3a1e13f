# Supertest API Tests for Appointment Planner

This directory contains API integration tests using Supertest for the Appointment Planner backend service.

## Test Files

### 1. `appointments.api.test.ts`
Tests the actual appointment planner API endpoints with real database integration:
- `GET /api/v1/appointments/available-slots` - Tests available time slots endpoint
- `POST /api/v1/appointments` - Tests appointment creation endpoint
- Health check endpoint
- Database integration with cleanup

### 2. `supertest-integration.test.ts`
Comprehensive integration tests with mock server:
- Mock implementation of API endpoints
- Validation testing
- Error handling scenarios
- Content-type handling
- Response format validation

### 3. `planner-api.test.ts` (Frontend)
Frontend API integration tests:
- Mock fetch calls to backend API
- Error handling and network failures
- Response format validation
- API integration patterns

## Running the Tests

### Backend Tests

```bash
# Run all API tests
bun run test:api

# Run API tests in watch mode
bun run test:api:watch

# Run only supertest integration tests
bun run test:supertest

# Run supertest tests in watch mode
bun run test:supertest:watch

# Run specific test file
bun test src/tests/api/appointments.api.test.ts
```

### Frontend Tests

```bash
# Run all API integration tests
bun run test:api

# Run API tests in watch mode
bun run test:api:watch

# Run specific test file
bun test src/tests/api/planner-api.test.ts
```

## API Endpoints Tested

### Available Slots
- **Endpoint**: `GET /api/v1/appointments/available-slots`
- **Parameters**: 
  - `salonId` (required)
  - `date` (required, format: YYYY-MM-DD)
  - `treatmentDuration` (optional, default: 30)
- **Response**: Array of time slots with availability status

### Create Appointment
- **Endpoint**: `POST /api/v1/appointments`
- **Body**: Appointment data (JSON)
- **Response**: Created appointment with ID and status

### Health Check
- **Endpoint**: `GET /health`
- **Response**: Service health status

## Test Environment Setup

### Database
The tests use a separate test database to avoid conflicts with development data:
- SQLite file database for testing
- Automatic cleanup between tests
- Migration setup in test environment

### Environment Variables
```bash
TEST_DATABASE_URL=file:./test.db
VITE_API_BASE_URL=http://localhost:5016
```

## Test Patterns

### 1. Supertest with Hono
```typescript
import request from 'supertest';
import { app } from '../../index';

const response = await request(app.fetch)
  .get('/api/v1/appointments/available-slots')
  .query({ salonId: 'salon-123', date: '2024-01-15' })
  .expect(200);
```

### 2. Mock API Testing
```typescript
const mockFetch = vi.fn().mockResolvedValue({
  ok: true,
  status: 200,
  json: async () => ({ success: true, data: [] }),
});
global.fetch = mockFetch;
```

### 3. Database Cleanup
```typescript
beforeEach(async () => {
  await prisma.appointment.deleteMany();
  await prisma.appointmentOutbox.deleteMany();
});
```

## Best Practices

1. **Isolation**: Each test is isolated with database cleanup
2. **Mocking**: Use mocks for external dependencies
3. **Validation**: Test both success and error scenarios
4. **Real Data**: Use realistic test data that matches production patterns
5. **Error Handling**: Test various error conditions and edge cases

## Common Test Scenarios

### Success Cases
- Valid appointment creation
- Available slots retrieval
- Proper response format

### Error Cases
- Missing required parameters
- Invalid data formats
- Network failures
- Database errors
- Validation failures

### Edge Cases
- Conflicting appointment times
- Invalid date formats
- Malformed JSON payloads
- Empty responses

## Debugging Tests

### Verbose Output
```bash
# Run with verbose output
bun test --reporter=verbose src/tests/api/

# Run with coverage
bun test --coverage src/tests/api/
```

### Database Inspection
```bash
# Check test database state
bunx prisma studio --schema=./prisma/schema.prisma
```

### Network Debugging
```bash
# Enable network logging
DEBUG=supertest bun test src/tests/api/
```
