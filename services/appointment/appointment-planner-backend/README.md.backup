# Appointment Planner Backend

A simple backend service for salon appointment integration.

## Setup

1. Install dependencies:
   ```
   npm install
   ```

2. Configure environment:
   - Copy `.env.example` to `.env` (or create a new `.env` file)
   - Update the `DATABASE_URL` to point to your PostgreSQL database

3. Set up the database:
   ```
   npm run prisma:generate
   npm run prisma:push
   ```

4. Start the development server:
   ```
   npm run dev
   ```

## API Endpoints

### Book an appointment

```
POST /api/book
```

Request body:
```json
{
  "salonId": "salon-id",
  "salonName": "Salon Name",
  "salonLogo": "https://example.com/logo.png", // optional
  "salonColor": "#FF5733", // optional
  "customerName": "John Doe",
  "customerEmail": "<EMAIL>",
  "customerPhone": "+1234567890", // optional
  "treatmentName": "Haircut",
  "treatmentDuration": 30,
  "treatmentPrice": 35.00,
  "startTime": "2023-06-15T10:00:00Z",
  "endTime": "2023-06-15T10:30:00Z",
  "notes": "First time customer" // optional
}
```

Response:
```json
{
  "id": "appointment-id",
  "status": "PENDING",
  // ... other appointment details
}
```

### Get Available Time Slots

```
GET /api/available-slots?salonId=salon-id&date=2023-06-15&treatmentDuration=30
```

Parameters:
- `salonId`: ID of the salon
- `date`: Date to check availability (YYYY-MM-DD)
- `treatmentDuration`: Duration of the treatment in minutes (optional, default: 30)

Response:
```json
[
  {
    "startTime": "2023-06-15T09:00:00Z",
    "endTime": "2023-06-15T09:30:00Z",
    "available": true
  },
  {
    "startTime": "2023-06-15T09:30:00Z",
    "endTime": "2023-06-15T10:00:00Z",
    "available": false
  },
  // ... other time slots
]
```

## Database Schema

We use a simple appointment model:

- `id`: Unique appointment identifier
- `status`: Appointment status (PENDING, CONFIRMED, CANCELLED, COMPLETED)
- `salonId`, `salonName`, etc.: Salon details
- `customerName`, `customerEmail`, etc.: Customer details
- `treatmentName`, `treatmentDuration`, `treatmentPrice`: Service details   
- `startTime`, `endTime`: Appointment time slot
- `notes`: Optional appointment notes

## Development

- Run tests: `npm test`
- Format code: `npm run format`
- Check types: `npm run check` 