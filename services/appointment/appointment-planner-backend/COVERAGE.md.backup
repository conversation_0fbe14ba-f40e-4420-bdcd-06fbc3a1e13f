# Test Coverage Configuration - Appointment Planner

This document outlines the test coverage configuration and requirements for the Appointment Planner services.

## Coverage Requirements

### Minimum Thresholds: 70%
- **Lines**: 70% minimum line coverage
- **Functions**: 70% minimum function coverage  
- **Branches**: 70% minimum branch coverage
- **Statements**: 70% minimum statement coverage

## Coverage Commands

### Backend (appointment-planner-backend)

```bash
# Run all tests with coverage
bun run test:coverage

# Run API tests with coverage
bun run test:coverage:api

# Generate HTML coverage report
bun run test:coverage:html

# Run with strict threshold enforcement
bun run test:coverage:threshold

# View coverage report in browser
open coverage/index.html
```

### Frontend (appointment-planner-frontend)

```bash
# Run all tests with coverage
bun run test:coverage

# Run API tests with coverage  
bun run test:coverage:api

# Generate HTML coverage report
bun run test:coverage:html

# Run with strict threshold enforcement
bun run test:coverage:threshold

# View coverage report in browser
open coverage/index.html
```

## Coverage Configuration

### Backend Configuration
Located in `vite.config.ts`:

```typescript
test: {
  coverage: {
    provider: 'v8',
    reporter: ['text', 'json', 'html', 'lcov'],
    thresholds: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70,
    },
    exclude: [
      'src/tests/**',
      'src/**/*.test.ts',
      'src/**/*.spec.ts',
      'prisma/**',
      '**/*.config.{ts,js}',
    ],
  },
}
```

### Frontend Configuration
Located in `vitest.config.ts`:

```typescript
test: {
  coverage: {
    provider: 'v8',
    reporter: ['text', 'json', 'html', 'lcov'],
    thresholds: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70,
    },
    exclude: [
      'src/tests/**',
      'src/**/*.test.{ts,tsx}',
      'src/main.tsx',
      'src/vite-env.d.ts',
    ],
  },
}
```

## Coverage Reports

### Console Output
```bash
# Example output
 ✓ src/tests/api/appointments.api.test.ts (8)
 ✓ src/tests/api/supertest-integration.test.ts (12)

 Test Files  2 passed (2)
      Tests  20 passed (20)
   Start at  10:30:15
   Duration  2.34s (transform 89ms, setup 0ms, collect 234ms, tests 1.89s)

 % Coverage report from v8
--------------------|---------|----------|---------|---------|-------------------
File                | % Stmts | % Branch | % Funcs | % Lines | Uncovered Line #s
--------------------|---------|----------|---------|---------|-------------------
All files           |   85.2  |   78.4   |   82.1  |   85.2  |
 src/application    |   92.3  |   85.7   |   90.0  |   92.3  |
  appointmentService|   92.3  |   85.7   |   90.0  |   92.3  | 45-47
 src/domain         |   78.9  |   70.0   |   75.0  |   78.9  |
  entities          |   78.9  |   70.0   |   75.0  |   78.9  | 23,34,67
--------------------|---------|----------|---------|---------|-------------------
```

### HTML Report
- Generated in `coverage/index.html`
- Interactive browsable report
- Line-by-line coverage details
- Branch coverage visualization

### LCOV Report
- Generated in `coverage/lcov.info`
- Compatible with external tools
- CI/CD integration friendly

## Coverage Exclusions

### Backend Exclusions
- Test files (`src/tests/**`, `**/*.test.ts`)
- Configuration files (`**/*.config.{ts,js}`)
- Type definitions (`**/*.d.ts`)
- Prisma generated files (`prisma/**`)
- Build artifacts (`dist/**`)

### Frontend Exclusions
- Test files (`src/tests/**`, `**/*.test.{ts,tsx}`)
- Entry point (`src/main.tsx`)
- Vite environment types (`src/vite-env.d.ts`)
- Configuration files (`**/*.config.{ts,js}`)
- Build artifacts (`dist/**`)

## CI/CD Integration

### GitHub Actions Example
```yaml
- name: Run tests with coverage
  run: bun run test:coverage

- name: Check coverage thresholds
  run: bun run test:coverage:threshold

- name: Upload coverage to Codecov
  uses: codecov/codecov-action@v3
  with:
    file: ./coverage/lcov.info
```

### Coverage Badges
Add to README.md:
```markdown
[![Coverage](https://codecov.io/gh/your-org/beauty-crm/branch/main/graph/badge.svg)](https://codecov.io/gh/your-org/beauty-crm)
```

## Best Practices

### 1. Write Testable Code
- Keep functions small and focused
- Avoid deep nesting
- Use dependency injection
- Separate business logic from infrastructure

### 2. Test Coverage Strategy
- **Unit Tests**: Test individual functions/classes
- **Integration Tests**: Test API endpoints and database interactions
- **Edge Cases**: Test error conditions and boundary values

### 3. Coverage Quality
- Aim for meaningful tests, not just coverage numbers
- Test both happy path and error scenarios
- Include edge cases and boundary conditions
- Mock external dependencies appropriately

### 4. Monitoring Coverage
- Run coverage checks in CI/CD pipeline
- Review coverage reports regularly
- Address coverage gaps systematically
- Set up coverage trend monitoring

## Troubleshooting

### Low Coverage Issues
```bash
# Identify uncovered lines
bun run test:coverage:html
open coverage/index.html

# Run specific test files
bun test src/specific/file.test.ts --coverage

# Debug coverage collection
DEBUG=vitest:coverage bun run test:coverage
```

### Common Coverage Gaps
1. **Error Handling**: Add tests for catch blocks
2. **Edge Cases**: Test boundary conditions
3. **Async Code**: Ensure promises are properly awaited
4. **Conditional Logic**: Test all branches

### Performance Considerations
- Coverage collection adds overhead
- Use `--coverage` only when needed
- Consider parallel test execution
- Optimize test setup/teardown

## Coverage Goals

### Short Term (Current Sprint)
- [ ] Achieve 70% minimum coverage across all metrics
- [ ] Set up automated coverage reporting
- [ ] Document coverage gaps

### Medium Term (Next 2 Sprints)
- [ ] Increase coverage to 80%
- [ ] Implement coverage trend monitoring
- [ ] Add coverage gates to CI/CD

### Long Term (Ongoing)
- [ ] Maintain 85%+ coverage
- [ ] Regular coverage reviews
- [ ] Coverage-driven development practices
