# Inventory Management Frontend

Microfrontend for inventory management in the Beauty CRM system.

## Module Federation Setup

This application is configured as a remote microfrontend using Module Federation. It exposes its main component at `./InventoryManagement` which can be consumed by the shell application.

## Development

You can start the development server in two ways:

```bash
# Default: Use Module Federation with platform-shell-lifecycle
npm run dev

# Alternative: Use standard Vite development server
npm run dev:vite
```

Both servers will run on port 5004 and expose the remote entry at http://localhost:5004/remoteEntry.js.

## Configuration

The Module Federation configuration is defined in two places:

1. `vite.config.ts` - For direct Vite usage
2. `platform-shell-lifecycle.metadata.json` - For platform-shell-lifecycle usage

## Troubleshooting

If you encounter issues with the Module Federation setup:

1. Check that the vite.config.ts includes the federation plugin with proper configuration
2. Verify that the required dependencies are installed
3. Make sure port 5004 is available
4. If using platform-shell-lifecycle, check that the metadata file is correctly configured

## Building for Production

```bash
npm run build
```

This will create a production build with the Module Federation remote entry in the `dist` folder. 