# Layout Feature

This feature provides layout components and structures for the application's user interface, including A/B testing capabilities.

## Components

- `DoubleSidebarLayout`: A layout with two sidebars for complex navigation structures
- `DoubleHeaderLayout`: A layout with two headers for rich header content
- `SidebarHeaderLayout`: A layout emphasizing sidebar navigation with a minimal header
- `HeaderSidebarLayout`: A layout with prominent header navigation and slim sidebar
- `PrivateLayout`: Default authenticated user layout
- `PublicHeader`: Header component for public pages
- `PublicRoute`: Route wrapper for public routes (no authentication required)
- `PrivateRoute`: Route wrapper for authenticated routes

## A/B Testing

The layout system includes built-in A/B testing capabilities. Users are randomly assigned one of five layout variants:

```typescript
type LayoutVariant =
  | 'private'      // Default layout
  | 'doubleSidebar'// Two-sidebar layout
  | 'doubleHeader' // Two-header layout
  | 'sidebarHeader'// Prominent sidebar
  | 'headerSidebar'// Prominent header
```

### Testing Features
- Random variant assignment
- Persistent layout selection (localStorage)
- Interaction tracking
- Time spent analytics
- Manual variant override for testing

### Usage

```typescript
// Using TestLayout (automatically handles A/B testing)
<TestLayout>
  <YourContent />
</TestLayout>

// Force a specific variant (for testing)
const { setPreferredVariant } = useLayoutTest();
setPreferredVariant('headerSidebar');

// Access analytics
const analytics = JSON.parse(localStorage.getItem('layout_analytics'));
console.log(analytics);
```

## Layout Variants

### DoubleSidebarLayout
A specialized layout featuring:
- Main sidebar with organization selector
- Secondary sidebar for feature-specific navigation
- Header with user actions
- Content area with customizable padding

### DoubleHeaderLayout
A layout variant with:
- Main header with organization selector
- Secondary header for feature-specific actions
- Sidebar with navigation
- Content area with customizable padding

### SidebarHeaderLayout
A layout emphasizing sidebar navigation:
- Prominent sidebar with organization controls
- Minimal header for user actions
- Full-width sidebar (md size)
- Vertical navigation emphasis

### HeaderSidebarLayout
A layout emphasizing header navigation:
- Prominent double header (main + navigation)
- Slim sidebar for secondary navigation
- Organization controls in header
- Horizontal navigation emphasis

## Features

- Responsive design
- Consistent styling
- Flexible content areas
- Organization context integration
- User context integration
- Mobile-friendly navigation
- A/B testing infrastructure
- Analytics tracking

## Analytics Data

The A/B testing system tracks:
```typescript
interface LayoutAnalytics {
  variant: LayoutVariant;
  startTime: number;
  interactions: number;
  lastInteraction: number;
}
```

## UI Design Examples

### Desktop View
![Layout Desktop View](@/features/docs/assets/images/layout-desktop.png)

### Mobile View
![Layout Mobile View](@/features/docs/assets/images/layout-mobile.png)

### Tablet View
![Layout Tablet View](@/features/docs/assets/images/layout-tablet.png) 

## Tasks

### Extracted Tasks

- [ ] `DoubleSidebarLayout`: A layout with two sidebars for complex navigation structures - M1
- [ ] `DoubleHeaderLayout`: A layout with two headers for rich header content - M2
- [ ] `SidebarHeaderLayout`: A layout emphasizing sidebar navigation with a minimal header - M3
- [ ] `HeaderSidebarLayout`: A layout with prominent header navigation and slim sidebar - M4
- [ ] `PrivateLayout`: Default authenticated user layout - M5
- [ ] `PublicHeader`: Header component for public pages - M6
- [ ] `PublicRoute`: Route wrapper for public routes (no authentication required) - M7
- [ ] `PrivateRoute`: Route wrapper for authenticated routes - M8
- [ ] Random variant assignment - M9
- [ ] Persistent layout selection (localStorage) - M10
- [ ] Interaction tracking - M11
- [ ] Time spent analytics - M12
- [ ] Manual variant override for testing - M13
- [ ] Main sidebar with organization selector - M14
- [ ] Secondary sidebar for feature-specific navigation - M15
- [ ] Header with user actions - M16
- [ ] Content area with customizable padding - M17
- [ ] Main header with organization selector - M18
- [ ] Secondary header for feature-specific actions - M19
- [ ] Sidebar with navigation - M20
- [ ] Content area with customizable padding - M21
- [ ] Prominent sidebar with organization controls - M22
- [ ] Minimal header for user actions - M23
- [ ] Full-width sidebar (md size) - M24
- [ ] Vertical navigation emphasis - M25
- [ ] Prominent double header (main + navigation) - M26
- [ ] Slim sidebar for secondary navigation - M27
- [ ] Organization controls in header - M28
- [ ] Horizontal navigation emphasis - M29
- [ ] Responsive design - M30
- [ ] Consistent styling - M31
- [ ] Flexible content areas - M32
- [ ] Organization context integration - M33
- [ ] User context integration - M34
- [ ] Mobile-friendly navigation - M35
- [ ] A/B testing infrastructure - M36
- [ ] Analytics tracking - M37

### Frontend Tasks

- [ ] Implement UI components - M1
- [ ] Add form validation - M2
- [ ] Add error handling - M3
- [ ] Add loading states - M4
- [ ] Add unit tests - M5
- [ ] Add accessibility features - M6

