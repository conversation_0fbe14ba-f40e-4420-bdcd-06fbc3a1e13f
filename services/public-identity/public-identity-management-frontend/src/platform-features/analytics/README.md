# Analytics Feature

This feature provides analytics tracking and reporting capabilities for the application.

## Components

- `AnalyticsPage`: Main analytics dashboard page
- `PageTrackingWrapper`: Component for tracking page views and navigation
- `AnalyticsSidebar`: Navigation component for analytics section

## Features

- Page view tracking
- User behavior analytics
- Performance monitoring
- Custom event tracking
- Analytics dashboard

## Usage

```typescript
// Track page views
<PageTrackingWrapper>
  <YourPage />
</PageTrackingWrapper>

// Analytics routes
import { AnalyticsRoutes } from '@/features/analytics/routes';

// Analytics sidebar
import { AnalyticsSidebar } from '@/features/analytics/components/AnalyticsSidebar';
```

## Integration

The analytics feature integrates with:
- Page routing system
- User context
- Organization context
- Modern layout system

## Data Collection

- Page views
- User interactions
- Performance metrics
- Custom events
- Error tracking

## UI Design Examples

### Analytics Dashboard
![Analytics Dashboard](@/features/docs/assets/images/analytics-dashboard.png)

### Page Tracking
![Page Tracking](@/features/docs/assets/images/page-tracking.png)

### Performance Metrics
![Performance Metrics](@/features/docs/assets/images/performance-metrics.png)

### User Behavior Analysis
![User Behavior Analysis](@/features/docs/assets/images/user-behavior.png)

# Web Vitals Tracking

This module provides enhanced analytics capabilities, including Web Vitals tracking for performance monitoring.

## Web Vitals

[Web Vitals](https://web.dev/vitals/) are a set of quality signals that are essential to delivering a great user experience on the web. The Core Web Vitals include:

- **Largest Contentful Paint (LCP)** 🖼️: Measures loading performance
- **First Input Delay (FID)** 👆: Measures interactivity
- **Cumulative Layout Shift (CLS)** 📏: Measures visual stability

In addition to the Core Web Vitals, we also track:
- **First Contentful Paint (FCP)** 🎨
- **Time to First Byte (TTFB)** 📡
- **Interaction to Next Paint (INP)** ⚡ (replacing FID)
- **Time to Interactive (TTI)** 🔄
- **Total Blocking Time (TBT)** ⏱️

## Development Mode Only

Web Vitals tracking is **only enabled in development mode** (when `APP_ENV_NAME=dev`), to avoid impacting production performance. The module will automatically detect the environment and disable tracking in production.

## How to Use

### Context-Based Architecture

Web Vitals tracking is implemented using a React Context-based approach:

1. **WebVitalsProvider**: Top-level provider that initializes Web Vitals tracking (only in development mode)
2. **useWebVitals hook**: Hook that provides access to the Web Vitals context

The `WebVitalsProvider` is already integrated in the `RootProvider`, so you don't need to add it manually to your components.

### Access Web Vitals Functionality in Components

```tsx
import { useWebVitals } from '@/platform-features/analytics/contexts/WebVitalsContext';

function MyComponent() {
  // Access Web Vitals context (only works in development mode)
  const { reportAllWebVitals, isEnabled } = useWebVitals();

  // You can check if tracking is enabled (development mode)
  const handleReportClick = () => {
    if (isEnabled()) {
      reportAllWebVitals();
      console.log('Performance metrics reported to console');
    } else {
      console.log('Web Vitals tracking is disabled in production mode');
    }
  };

  return (
    <div>
      <button onClick={handleReportClick}>Show Performance Metrics</button>
      
      {/* Only show in development mode */}
      {isEnabled() && (
        <button onClick={reportAllWebVitals}>
          Debug Performance
        </button>
      )}
    </div>
  );
}
```

### Error Boundary Integration

Web Vitals metrics are automatically collected and reported when an error occurs in development mode, helping diagnose if performance issues contributed to the error.

## Console Output

When Web Vitals are tracked in development mode, you'll see beautiful console output with:

- Color-coded metrics based on performance rating (good, needs improvement, poor)
- Emoji indicators for each metric type
- Detailed information on timing and performance
- Recommendations for improving poor metrics

## Dependencies

This feature requires the `web-vitals` package. If it's not already installed, add it:

```bash
npm install web-vitals
# or
yarn add web-vitals
```

## Architecture

The Web Vitals tracking functionality is split into:

1. **WebVitalsContext.tsx**: Context provider and hook for accessing Web Vitals functionality
2. **webVitalsService.ts**: Core service for Web Vitals metric collection and reporting
3. **logger.ts**: General logging service, used to record Web Vitals as log entries

## WebVitals Context API

The useWebVitals hook provides:

- `reportAllWebVitals()`: Generate a summary report of all collected Web Vitals metrics
- `isEnabled()`: Check if Web Vitals tracking is enabled (in development mode) 

## Tasks

### Extracted Tasks

- [ ] `AnalyticsPage`: Main analytics dashboard page - M1
- [ ] `PageTrackingWrapper`: Component for tracking page views and navigation - M2
- [ ] `AnalyticsSidebar`: Navigation component for analytics section - M3
- [ ] Page view tracking - M4
- [ ] User behavior analytics - M5
- [ ] Performance monitoring - M6
- [ ] Custom event tracking - M7
- [ ] Analytics dashboard - M8
- [ ] Page routing system - M9
- [ ] User context - M10
- [ ] Organization context - M11
- [ ] Modern layout system - M12
- [ ] User interactions - M13
- [ ] Performance metrics - M14
- [ ] Custom events - M15
- [ ] Error tracking - M16
- [ ] **Largest Contentful Paint (LCP)** 🖼️: Measures loading performance - M17
- [ ] **First Input Delay (FID)** 👆: Measures interactivity - M18
- [ ] **Cumulative Layout Shift (CLS)** 📏: Measures visual stability - M19
- [ ] **First Contentful Paint (FCP)** 🎨 - M20
- [ ] **Time to First Byte (TTFB)** 📡 - M21
- [ ] **Interaction to Next Paint (INP)** ⚡ (replacing FID) - M22
- [ ] **Time to Interactive (TTI)** 🔄 - M23
- [ ] **Total Blocking Time (TBT)** ⏱️ - M24
- [ ] Color-coded metrics based on performance rating (good, needs improvement, poor) - M25
- [ ] Emoji indicators for each metric type - M26
- [ ] Detailed information on timing and performance - M27
- [ ] Recommendations for improving poor metrics - M28
- [ ] `reportAllWebVitals()`: Generate a summary report of all collected Web Vitals metrics - M29
- [ ] `isEnabled()`: Check if Web Vitals tracking is enabled (in development mode) - M30

### Frontend Tasks

- [ ] Implement UI components - M1
- [ ] Add form validation - M2
- [ ] Add error handling - M3
- [ ] Add loading states - M4
- [ ] Add unit tests - M5
- [ ] Add accessibility features - M6

