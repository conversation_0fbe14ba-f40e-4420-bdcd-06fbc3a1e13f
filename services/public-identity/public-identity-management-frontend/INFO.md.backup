# Identity Management Frontend

## Overview

This document provides a visual overview of the Identity Management Frontend service, which is part of the Beauty CRM platform. This service handles user authentication, profile management, and tenant configuration through a modern React-based interface.

## Architecture

<div align="center">

### Frontend Architecture

![Frontend Architecture](./assets/identity-frontend-architecture.svg)

</div>

## Key Features

- **User Authentication**: Complete authentication flows including login, registration, password reset, and multi-factor authentication
- **User Profile Management**: Self-service profile management including personal details, preferences, and security settings
- **Tenant Administration**: Management interface for salon/spa administrators to configure their tenant settings
- **Session Management**: Visibility and control over active user sessions
- **Role-Based Access Control**: UI components and routes respond to user permissions

## Technology Stack

<div align="center">

### Frontend Technology Stack

```mermaid
graph TD
    subgraph "UI Layer"
        React["React 18"]
        TS["TypeScript 5"]
        CSS["TailwindCSS"]
    end
    
    subgraph "State Management"
        Zustand["Zustand"]
        ReactQuery["React Query"]
    end
    
    subgraph "Validation & Forms"
        Zod["Zod"]
        HookForm["React Hook Form"]
    end
    
    subgraph "API Integration"
        Fetch["Fetch API"]
        Axios["Axios"]
    end
    
    React -->|uses| TS
    React -->|styled with| CSS
    React -->|state| Zustand
    React -->|server state| ReactQuery
    React -->|forms| HookForm
    HookForm -->|validation| Zod
    ReactQuery -->|http client| Axios
    ReactQuery -->|native| Fetch
    
    classDef primary fill:#42a5f5,stroke:#1976d2,color:white
    classDef secondary fill:#66bb6a,stroke:#43a047,color:white
    classDef utility fill:#ab47bc,stroke:#8e24aa,color:white
    
    class React,TS,CSS primary
    class Zustand,ReactQuery,Axios,Fetch secondary
    class Zod,HookForm utility
```

</div>

## Component Structure

The frontend follows a modular component structure:

- **Layout Components**: Page layouts, navigation, and shared UI elements
- **Feature Components**: Components specific to authentication, user management, and tenant configuration
- **UI Components**: Reusable UI elements like buttons, inputs, and cards
- **Hooks**: Custom React hooks for shared logic and state management
- **Services**: API integration and business logic services

## Testing Strategy

The Identity Management Frontend employs comprehensive testing:

- **Unit Tests**: Testing individual components and hooks
- **Integration Tests**: Testing component interactions and form flows
- **E2E Tests**: Testing complete user journeys using Playwright

## Performance Metrics

<div align="center">

### Frontend Performance

| Metric | Value | Status |
|--------|-------|--------|
| Lighthouse Performance | 92/100 | ✅ Good |
| First Contentful Paint | 0.8s | ✅ Good |
| Time to Interactive | 1.3s | ✅ Good |
| Largest Contentful Paint | 1.1s | ✅ Good |
| Cumulative Layout Shift | 0.02 | ✅ Good |
| Bundle Size (gzipped) | 124KB | ✅ Good |

</div>

## Deployment Configuration

This service is deployed on Fly.io with the following configuration:

- **Static Asset Hosting**: Optimized for global edge delivery
- **Authentication Integration**: Secure authentication flows
- **Environment Configuration**: Production, staging, and development environments
- **Monitoring**: Real-time performance and error monitoring

## Integration Points

The Identity Management Frontend integrates with:

- **Identity Management Backend**: For user and tenant data management
- **API Gateway**: For routing to appropriate backend services
- **Authentication Service**: For secure token management and session validation

## Future Enhancements

Planned enhancements for this service include:

- OAuth provider integration (Google, Apple, Microsoft)
- Enhanced security features (IP-based access control, advanced MFA)
- Improved accessibility compliance (WCAG AAA)
- Performance optimizations for mobile devices 