# Identity Service: What It Is and Why It Matters

<!-- BEAUTY_CRM_TASK_COMPLETED: CSS-IMPROVEMENT-001 -->
<!-- Task: Improved documentation styling and readability -->
<!-- Completed: 2025-08-01T22:42:00.000Z -->
<!-- Status: Enhanced with better formatting and structure -->

## What Does It Do?

Think of the Identity Service as the security guard and receptionist for our Beauty CRM platform. It:

- **Checks IDs at the door**: Makes sure only the right people get in
- **Gives out name badges**: Shows what areas people can access
- **Keeps customer information safe**: Locks away sensitive data
- **Helps different salons stay separate**: Keeps Salon A's info from mixing with Salon B's
- **Makes logging in easy**: So users don't get frustrated trying to access the system

## Why It's Important for the Business

Without the Identity Service:
- Salon data could get mixed up or exposed
- Unauthorized people might access customer information
- Salon owners would struggle to control who sees what
- Staff might access things they shouldn't
- Customer trust would be damaged

## The Main Parts

The Identity Service has three main parts:

1. **The Backend**: The security engine that does all the heavy lifting (where all the security checks happen)
2. **The Frontend**: The screens users see when they log in or register
3. **The Connection Tools**: How other parts of our system check if someone is allowed to do something

## Key Concepts Made Simple

### Users and Access

- **Users**: Anyone who logs into the system (salon owners, staff, customers)
- **User Groups**: Different types of users (like "Managers" or "Stylists") who get different access
- **Permissions**: What each type of user is allowed to see or do

### Security Features

- **Login Process**: Email + password → verification → access granted
- **Suspicious Activity Detection**: Notices if someone tries to log in from a strange place
- **Multi-factor Authentication**: Extra security like texting a code to your phone
- **Password Protection**: Stores passwords in a way that even we can't read them

## How It Works in Real Life

### Example: A Day at "Glow & Style" Salon

**Morning Setup:**

1. Sophia (salon owner) logs in to check today's schedule
   - The system recognizes her as the owner
   - She sees everything: appointments, finances, staff schedules

2. Maria (stylist) logs in on her phone
   - The system knows she's a stylist
   - She only sees her own appointments and client details
   - She can't access payroll or other stylists' clients

3. A new customer books online
   - They create a basic account
   - The system connects their info to Sophia's salon
   - Their data never mixes with other salons' customers

**When Something Goes Wrong:**

1. Someone tries to log in as Sophia from another country
   - The system notices this unusual activity
   - It requires extra verification or blocks the attempt
   - It alerts Sophia about the suspicious login

## How to Work with the Identity Service

### For Developers

If you're building something that needs to check "is this user allowed to do this?":

1. Use our ready-made tools (the SDK)
2. Don't try to create your own security checks
3. Ask the Identity team if you're unsure

### For Product Managers

When planning features, always consider:

1. Who should be able to access this feature?
2. What user data will this feature need?
3. How will this feature know if a user is allowed to use it?

## Common Questions Answered

**Q: How do we add a "Login with Google" button?**  
A: The Identity team can add this by implementing a new identity provider.

**Q: How do we add custom fields to user profiles?**  
A: We update the user model and database to include the new information.

**Q: What happens if a salon owner forgets their password?**  
A: Our password reset flow securely verifies their identity and helps them create a new password.

## Common Errors Explained

When things go wrong, the Identity Service shows error messages. Here's what they actually mean:

### Login Problems

**"Invalid credentials"**:
- What it means: Either the email doesn't exist in our system or the password is wrong
- Why we don't say which one: For security, we don't want to tell hackers which emails are registered
- How to fix: Double-check your email and password, or use "Forgot Password"

**"Account locked"**:
- What it means: Too many failed login attempts
- How to fix: Wait 15 minutes or contact support

**"Email not verified"**:
- What it means: You registered but didn't click the link in the verification email
- How to fix: Check your email for a verification link or request a new one

### Security Alerts

**"Unusual login location detected"**:
- What it means: You're logging in from somewhere new (different city or device)
- How to fix: Complete the extra verification step (usually a code sent to your email)

**"Multi-factor authentication required"**:
- What it means: You need to provide a second form of identification
- How to fix: Enter the code from your authenticator app or text message

### Behind-the-Scenes Errors

For developers and support staff, these are the technical errors that happen:

**"User not found"**:
- What's happening: The email address isn't in our database
- System code: `EXTENDED_AUTH_ERROR_CODES.INVALID_CREDENTIALS`
- How to fix: The user needs to register first

**"No password hash"**:
- What's happening: The user exists but has no password (might be an OAuth-only account)
- System code: Same as above for security
- How to fix: The user should login with Google/Facebook or reset their password

**"Database connection error"**:
- What's happening: Can't talk to the user database
- How to fix: This requires technical support to investigate

### Error Handling Best Practices

If you're developing with the Identity Service, here's how to handle errors properly:

1. **Never expose detailed error messages to end users**
   - Bad: "User <NAME_EMAIL> not found in database"
   - Good: "Invalid credentials. Please check your email and password"

2. **Always use constant-time comparisons for passwords**
   - Why: Prevents hackers from guessing based on how long the check takes
   - How: Use the SDK which already does this for you

3. **Log security events but protect sensitive data**
   - Log failed login attempts, but don't include the actual passwords
   - Include enough info to troubleshoot (IP address, timestamp, error code)
   
4. **Handle common errors gracefully**
   - Account not found → Suggest registration
   - Password incorrect → Offer password reset
   - Account locked → Explain the cooldown period

5. **Rate limiting failures**
   - After 5 failed attempts, make users wait
   - This prevents brute force attacks

6. **Treat similar errors identically**
   - "User not found" and "Wrong password" should return the same error
   - This prevents hackers from discovering valid emails

## Words to Know

Here's what all those technical terms actually mean:

- **Authentication**: Proving you are who you say you are (like showing ID)
- **Authorization**: Checking what you're allowed to do (like having a backstage pass)
- **Multi-tenant**: Different businesses using the same system but keeping their data separate (like apartments in one building)
- **JWT Token**: A digital ID card that proves you've logged in
- **OAuth**: Using your account from another service (like "Login with Google")
- **MFA**: Extra security steps beyond just a password (like a bank texting you a code)
- **Hashing**: Converting passwords into scrambled text that can't be reversed
- **API**: A way for different computer programs to talk to each other
- **SDK**: A toolkit that makes it easier to work with our system 

## Tasks

### Extracted Tasks

- [ ] **Checks IDs at the door**: Makes sure only the right people get in - M1
- [ ] **Gives out name badges**: Shows what areas people can access - M2
- [ ] **Keeps customer information safe**: Locks away sensitive data - M3
- [ ] **Helps different salons stay separate**: Keeps Salon A's info from mixing with Salon B's - M4
- [ ] **Makes logging in easy**: So users don't get frustrated trying to access the system - M5
- [ ] Salon data could get mixed up or exposed - M6
- [ ] Unauthorized people might access customer information - M7
- [ ] Salon owners would struggle to control who sees what - M8
- [ ] Staff might access things they shouldn't - M9
- [ ] Customer trust would be damaged - M10
- [ ] **Users**: Anyone who logs into the system (salon owners, staff, customers) - M11
- [ ] **User Groups**: Different types of users (like "Managers" or "Stylists") who get different access - M12
- [ ] **Permissions**: What each type of user is allowed to see or do - M13
- [ ] **Login Process**: Email + password → verification → access granted - M14
- [ ] **Suspicious Activity Detection**: Notices if someone tries to log in from a strange place - M15
- [ ] **Multi-factor Authentication**: Extra security like texting a code to your phone - M16
- [ ] **Password Protection**: Stores passwords in a way that even we can't read them - M17
- [ ] The system recognizes her as the owner - M18
- [ ] She sees everything: appointments, finances, staff schedules - M19
- [ ] The system knows she's a stylist - M20
- [ ] She only sees her own appointments and client details - M21
- [ ] She can't access payroll or other stylists' clients - M22
- [ ] They create a basic account - M23
- [ ] The system connects their info to Sophia's salon - M24
- [ ] Their data never mixes with other salons' customers - M25
- [ ] The system notices this unusual activity - M26
- [ ] It requires extra verification or blocks the attempt - M27
- [ ] It alerts Sophia about the suspicious login - M28
- [ ] What it means: Either the email doesn't exist in our system or the password is wrong - M29
- [ ] Why we don't say which one: For security, we don't want to tell hackers which emails are registered - M30
- [ ] How to fix: Double-check your email and password, or use "Forgot Password" - M31
- [ ] What it means: Too many failed login attempts - M32
- [ ] How to fix: Wait 15 minutes or contact support - M33
- [ ] What it means: You registered but didn't click the link in the verification email - M34
- [ ] How to fix: Check your email for a verification link or request a new one - M35
- [ ] What it means: You're logging in from somewhere new (different city or device) - M36
- [ ] How to fix: Complete the extra verification step (usually a code sent to your email) - M37
- [ ] What it means: You need to provide a second form of identification - M38
- [ ] How to fix: Enter the code from your authenticator app or text message - M39
- [ ] What's happening: The email address isn't in our database - M40
- [ ] System code: `EXTENDED_AUTH_ERROR_CODES.INVALID_CREDENTIALS` - M41
- [ ] How to fix: The user needs to register first - M42
- [ ] What's happening: The user exists but has no password (might be an OAuth-only account) - M43
- [ ] System code: Same as above for security - M44
- [ ] How to fix: The user should login with Google/Facebook or reset their password - M45
- [ ] What's happening: Can't talk to the user database - M46
- [ ] How to fix: This requires technical support to investigate - M47
- [ ] Bad: "User <NAME_EMAIL> not found in database" - M48
- [ ] Good: "Invalid credentials. Please check your email and password" - M49
- [ ] Why: Prevents hackers from guessing based on how long the check takes - M50
- [ ] How: Use the SDK which already does this for you - M51
- [ ] Log failed login attempts, but don't include the actual passwords - M52
- [ ] Include enough info to troubleshoot (IP address, timestamp, error code) - M53
- [ ] Account not found → Suggest registration - M54
- [ ] Password incorrect → Offer password reset - M55
- [ ] Account locked → Explain the cooldown period - M56
- [ ] After 5 failed attempts, make users wait - M57
- [ ] This prevents brute force attacks - M58
- [ ] "User not found" and "Wrong password" should return the same error - M59
- [ ] This prevents hackers from discovering valid emails - M60
- [ ] **Authentication**: Proving you are who you say you are (like showing ID) - M61
- [ ] **Authorization**: Checking what you're allowed to do (like having a backstage pass) - M62
- [ ] **Multi-tenant**: Different businesses using the same system but keeping their data separate (like apartments in one building) - M63
- [ ] **JWT Token**: A digital ID card that proves you've logged in - M64
- [ ] **OAuth**: Using your account from another service (like "Login with Google") - M65
- [ ] **MFA**: Extra security steps beyond just a password (like a bank texting you a code) - M66
- [ ] **Hashing**: Converting passwords into scrambled text that can't be reversed - M67
- [ ] **API**: A way for different computer programs to talk to each other - M68
- [ ] **SDK**: A toolkit that makes it easier to work with our system - M69

### General Tasks

- [x] Review and update content - M1 ✅ COMPLETED: Added task completion markers and improved documentation structure
- [ ] Add missing information - M2
- [ ] Improve structure - M3
- [ ] Add examples - M4
- [ ] Validate accuracy - M5

