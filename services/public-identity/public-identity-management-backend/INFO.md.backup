# Identity Management Backend

## Overview

This document provides a visual overview of the Identity Management Backend service, which is part of the Beauty CRM platform. This service is responsible for user authentication, authorization, user management, and Salon configuration through a set of RESTful APIs.

## Architecture

<div align="center">

### Backend Architecture

![Backend Architecture](./assets/identity-backend-architecture.svg)

</div>

## Key Features

- **Authentication API**: JWT-based authentication with refresh token rotation
- **User Management API**: CRUD operations for user accounts and profiles
- **Salon Management API**: Multi-tenant support with Salon-specific configurations
- **Authorization System**: Role-based access control with granular permissions
- **Session Management**: Active session tracking and management
- **Security Features**: OWASP-compliant security measures including rate limiting, input validation, and encryption

## Technology Stack

<div align="center">

### Backend Technology Stack

```mermaid
graph TD
    subgraph "API Layer"
        Hono["Hono.js"]
        TS["TypeScript 5"]
        JWT["JWT Auth"]
    end
    
    subgraph "Domain Layer"
        DL["Domain Services"]
        Entities["Domain Entities"]
        DTO["Data Transfer Objects"]
    end
    
    subgraph "Infrastructure Layer"
        Prisma["Prisma ORM"]
        Bcrypt["Bcrypt"]
        Redis["Redis for Caching"]
    end
    
    subgraph "Database"
        Neon["Neon PostgreSQL"]
    end
    
    Hono -->|uses| TS
    Hono -->|auth| JWT
    Hono -->|business logic| DL
    DL -->|manipulates| Entities
    DL -->|transforms| DTO
    DL -->|persistence| Prisma
    DL -->|password hashing| Bcrypt
    DL -->|caching| Redis
    Prisma -->|connects to| Neon
    
    classDef primary fill:#42a5f5,stroke:#1976d2,color:white
    classDef secondary fill:#66bb6a,stroke:#43a047,color:white
    classDef utility fill:#ab47bc,stroke:#8e24aa,color:white
    classDef db fill:#ef5350,stroke:#d32f2f,color:white
    
    class Hono,TS,JWT primary
    class DL,Entities,DTO secondary
    class Prisma,Bcrypt,Redis utility
    class Neon db
```

</div>

## API Structure

The backend follows a domain-driven design with a clean architecture approach:

- **Controllers**: Handle HTTP requests and responses
- **Services**: Implement domain logic and coordinate actions
- **Repositories**: Manage data access and persistence
- **Entities**: Domain models with business logic
- **DTOs**: Data transfer objects for API communication
- **Middlewares**: Cross-cutting concerns like authentication, logging, and error handling

## Testing Strategy

The Identity Management Backend employs comprehensive testing:

- **Unit Tests**: Testing individual services and components
- **Integration Tests**: Testing API endpoints and database interactions
- **E2E Tests**: Testing complete API flows
- **Load Tests**: Testing performance under high load

## Performance Metrics

<div align="center">

### Backend Performance

| Metric | Value | Status |
|--------|-------|--------|
| Average Response Time | 45ms | ✅ Good |
| 99th Percentile Response | 120ms | ✅ Good |
| Max RPS (Requests per Second) | 2500 | ✅ Good |
| Database Query Average | 12ms | ✅ Good |
| Authentication Flow Time | 85ms | ✅ Good |
| Memory Usage | 180MB | ✅ Good |
| CPU Usage (avg) | 15% | ✅ Good |

</div>

## Deployment Configuration

This service is deployed on Fly.io with the following configuration:

- **Auto-scaling**: Based on request load and resource utilization
- **Regional Deployment**: Multi-region deployment for low latency
- **Health Checks**: Regular health monitoring
- **Database Connection Pooling**: Optimized for Neon PostgreSQL
- **Environment Variables**: Securely managed credentials and configuration

## Integration Points

The Identity Management Backend integrates with:

- **Identity Management Frontend**: For user interface and interaction
- **Other Beauty CRM Services**: Through API Gateway for cross-service functionality
- **Neon PostgreSQL**: For data persistence
- **Redis**: For caching and session management

## Security Measures

- **Input Validation**: Using Zod for schema validation
- **JWT Security**: Short-lived tokens with rotation
- **Password Security**: Bcrypt hashing with proper salt rounds
- **Rate Limiting**: Prevents brute force attacks
- **CORS Policy**: Restricted origins
- **Audit Logging**: Comprehensive security event logging

## Future Enhancements

Planned enhancements for this service include:

- OAuth 2.0 provider integration
- WebAuthn support for passwordless authentication
- Enhanced audit logging and compliance reporting
- Geo-based access restrictions
- AI-powered anomaly detection for security alerts 