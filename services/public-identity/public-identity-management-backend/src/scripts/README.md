# Beauty CRM Scripts

This directory contains utility scripts for managing the Beauty CRM system.

## Available Scripts

### Setup Database
```bash
npx ts-node scripts/setupDb.ts
```
Connects to the database and performs initial setup operations.

### Make User Admin (Direct DB Access)
```bash
npx ts-node scripts/makeUserAdmin.ts <EMAIL>
```
This script directly updates the database to change a user's role to ADMIN.
- **Arguments**: The email address of the user you want to make an admin

### Make User Admin (API Method)
```bash
npx ts-node scripts/makeUserAdminAPI.ts <EMAIL>
```
This script uses the API to change a user's role to ADMIN. It requires an existing admin account to authenticate.
- **Arguments**: The email address of the user you want to make an admin
- **Interactive Inputs**: Admin email and password for authentication

## Requirements

- Node.js and npm/npx installed
- Access to the Beauty CRM database or API
- Proper environment configuration (API_URL if using the API method)

## Troubleshooting

If you encounter errors:

1. Ensure the database is running and accessible
2. Check that the target user exists
3. Verify your admin credentials have sufficient permissions
4. Make sure the API is running if using the API method

For database connection issues, check your `.env` file contains the correct database connection string. 

## Tasks

### Extracted Tasks

- [ ] **Arguments**: The email address of the user you want to make an admin - M1
- [ ] **Arguments**: The email address of the user you want to make an admin - M2
- [ ] **Interactive Inputs**: Admin email and password for authentication - M3
- [ ] Node.js and npm/npx installed - M4
- [ ] Access to the Beauty CRM database or API - M5
- [ ] Proper environment configuration (API_URL if using the API method) - M6

### Backend Tasks

- [ ] Implement API endpoints - M1
- [ ] Add input validation - M2
- [ ] Add error handling - M3
- [ ] Add unit tests - M4
- [ ] Add integration tests - M5
- [ ] Add documentation - M6

