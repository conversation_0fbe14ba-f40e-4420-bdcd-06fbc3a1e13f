# Auth API Tests

## Cleanup
DELETE /api/v1/auth/cleanup -> 200,404 {"success":true}

## Register
POST /api/v1/auth/register email,password,metadata.firstName,metadata.lastName -> 200 {"success":true}

## Sign In
POST /api/v1/auth/sign-in email,password -> 200 {"success":true} => userToken: $.data.token

## Sign Out
POST /api/v1/auth/sign-out [Authorization: Bearer ${vars.userToken}] -> 200 {"success":true}


## Tasks

### Backend Tasks

- [ ] Implement API endpoints - M1
- [ ] Add input validation - M2
- [ ] Add error handling - M3
- [ ] Add unit tests - M4
- [ ] Add integration tests - M5
- [ ] Add documentation - M6

