# Auth API Tests
> baseUrl: ${env.BASE_URL}

# Create a new user
POST /api/v1/auth/register {"email":"<EMAIL>","password":"Test123!@#","metadata":{"firstName":"Test","lastName":"User"}} -> 200 {"success":true}

# First login
POST /api/v1/auth/sign-in {"email":"<EMAIL>","password":"Test123!@#"} -> 200 {"success":true} => userToken: $.data.token

# Setup MFA
POST /api/v1/auth/mfa/setup [Authorization: Bearer ${vars.userToken}] -> 200 {"success":true} => secretKey: $.data.secretKey

# Verify MFA
POST /api/v1/auth/mfa/verify [Authorization: Bearer ${vars.userToken}] {"code": "123456", "secretKey": "${vars.secretKey}"} -> 200 {"success":true} 

# Logout
POST /api/v1/auth/sign-out [Authorization: Bearer ${vars.userToken}] -> 200 {"success":true}

# Logout
POST /api/v1/auth/sign-out [Authorization: Bearer ${vars.userToken}] -> 200 {"success":true}



## Tasks

### Backend Tasks

- [ ] Implement API endpoints - M1
- [ ] Add input validation - M2
- [ ] Add error handling - M3
- [ ] Add unit tests - M4
- [ ] Add integration tests - M5
- [ ] Add documentation - M6

