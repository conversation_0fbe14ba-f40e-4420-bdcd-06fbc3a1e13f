# Auth API Tests

## Cleanup
DELETE /api/v1/auth/cleanup -> 200,404 {"success":true}

## Register
POST /api/v1/auth/register email,password,metadata.firstName,metadata.lastName -> 200 {"success":true}

## Sign In
POST /api/v1/auth/sign-in email,password -> 200 {"success":true} => userToken: $.data.token

## Sign Out
POST /api/v1/auth/sign-out [Authorization: Bearer ${vars.userToken}] -> 200 {"success":true}
