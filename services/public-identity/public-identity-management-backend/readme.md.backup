
# DDD Decision Flowchart

```mermaid
graph TD
    A[Does it encode business rules?] -->|Yes| B[Domain Interface]
    A -->|No| C[Pure Tech Implementation?]
    C -->|Yes| D[Infrastructure Service]
    B --> E[Examples: Password complexity rules, Token expiration policies]
    D --> F[Examples: Bcrypt hashing, JWT token generation] 
```

# Key Differentiators

# Domain Services Contain:

Password complexity requirements (minLength=8, specialCharsRequired)

Token expiration policies (refreshTokenExpiresIn=7d)

Authorization rules (adminCanDeleteUsers)

# Infrastructure Services Contain:

Cryptographic algorithm implementations (bcrypt/scrypt)

Token encoding/decoding mechanics (JWT/PASETO)

Storage adapters (Redis for token storage)

# Folder Structure  

```mermaid
src/
├── domain/
│   └── auth/
│       ├── PasswordHashingService.ts   # Interface
│       └── TokenGenerationService.ts   # Interface
└── infrastructure/
    └── auth/
        ├── BcryptPasswordHashingService.ts  # Impl
        └── JwtTokenGenerationService.ts     # Impl
```

This structure keeps business rules technology-agnostic while allowing infrastructure implementations to be swapped without domain layer changes.