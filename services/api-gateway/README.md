# Beauty CRM API Gateway

A multi-salon API Gateway for the Beauty CRM SaaS platform, providing centralized authentication, salon isolation, and service routing.

## 🏗️ Architecture Overview

```mermaid
graph TB
    Client[Client Applications] --> Gateway[API Gateway]
    Gateway --> Auth[Authentication]
    Gateway --> Salon[Salon Middleware]
    Gateway --> Proxy[Service Proxy]
    
    Proxy --> AppointmentSvc[Appointment Service]
    Proxy --> StaffSvc[Staff Service]
    Proxy --> SalonSvc[Salon Service]
    
    Gateway --> DB[(Shared Database)]
    
    subgraph "Multi-Salon Context"
        Salon --> SalonDB[Salon Validation]
        SalonDB --> SalonData[Salon Data]
    end
```

## 🔑 Key Features

### Multi-Salon Support
- **Salon Isolation**: Row-level security with salon ID
- **Multiple Salon Resolution Methods**:
  - Header: `x-salon-id`
  - Query Parameter: `salonId`
  - Subdomain: `{salon-slug}.beauty-crm.com`
  - Path Parameter: `/api/salons/{salonId}/...`

### Authentication & Authorization
- **JWT-based Authentication**
- **Role-based Access Control**
- **Salon-User Validation**
- **Cross-service Token Propagation**

### Service Proxy
- **Intelligent Routing** to microservices
- **Header Injection** for salon context
- **Load Balancing** and **Failover**
- **Request/Response Logging**

### Observability
- **Structured Logging** with salon context
- **Health Checks** for all dependencies
- **Performance Metrics**
- **Error Tracking**

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ or Bun
- PostgreSQL database

### Installation
```bash
# Install dependencies
bun install

# Copy environment file
cp .env.example .env

# Build the application
bun run build

# Start in development mode
bun run dev
```

### Environment Configuration
```env
# Required
PORT=3000
JWT_SECRET=your-secret-key
DATABASE_URL=postgresql://user:pass@localhost:5432/beauty_crm

# Service URLs
APPOINTMENT_SERVICE_URL=http://appointment-service:4000
STAFF_SERVICE_URL=http://staff-service:4000
SALON_SERVICE_URL=http://salon-service:4000
```

## 📡 API Endpoints

### Health Check
```http
GET /health
GET /health/detailed
```

### Salon Management
```http
GET /api/salons/current
GET /api/salons/settings
PUT /api/salons/settings
GET /api/salons/features/{feature}
GET /api/salons/public/by-slug/{slug}
```

### Service Proxies
```http
# Appointments
GET /api/appointments
POST /api/appointments
PUT /api/appointments/{id}

# Staff
GET /api/staff
POST /api/staff

# Salons
GET /api/salons
POST /api/salons
```

## 🔐 Authentication Flow

1. **Client Login**: User authenticates with credentials
2. **JWT Generation**: Server generates JWT with salon context
3. **Request Headers**: Client includes `Authorization: Bearer {token}`
4. **Token Validation**: Gateway validates JWT and extracts user/salon info
5. **Salon Validation**: Ensures user belongs to requested salon
6. **Service Forwarding**: Adds salon headers to downstream requests

## 🏢 Multi-Salon Patterns

### Salon Resolution Priority
1. **Header**: `x-salon-id` (highest priority)
2. **Query**: `?salonId=xxx`
3. **Subdomain**: `salon-slug.beauty-crm.com`
4. **Path**: `/api/salons/{salonId}/...`

### Database Isolation
```sql
-- Row Level Security Example
CREATE POLICY salon_isolation ON appointments
  FOR ALL TO app_user
  USING (salon_id = current_setting('app.current_salon_id'));
```

### Request Headers Added
```http
x-salon-id: clh7qxk9x0000356h4tgkjl2m
x-salon-slug: luxury-salon
x-user-id: clh7qxk9x0001356h4tgkjl2n
x-user-role: manager
```

## 🔌 Plugin System (Future)

The gateway is designed to support plugins for:
- **Custom Authentication** providers
- **Rate Limiting** strategies
- **Caching** mechanisms
- **Monitoring** integrations
- **Custom Middleware**

## 📊 Monitoring & Observability

### Structured Logging
```json
{
  "level": "info",
  "message": "Request completed",
  "method": "GET",
  "url": "/api/appointments",
  "statusCode": 200,
  "duration": "45ms",
  "salonId": "clh7qxk9x0000356h4tgkjl2m",
  "userId": "clh7qxk9x0001356h4tgkjl2n"
}
```

### Health Check Response
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00Z",
  "uptime": 3600,
  "services": {
    "database": "healthy",
    "redis": "healthy"
  }
}
```

## 🛡️ Security Features

- **Helmet.js** for security headers
- **CORS** configuration
- **Rate Limiting** per IP/salon
- **Input Validation** with Zod
- **SQL Injection** protection via Prisma
- **XSS Protection**
- **CSRF Protection**

## 🚀 Deployment

### Docker
```bash
# Build image
docker build -t beauty-crm/api-gateway .

# Run container
docker run -p 3000:3000 \
  -e DATABASE_URL=postgresql://... \
  -e JWT_SECRET=... \
  beauty-crm/api-gateway
```

### Production Considerations
- Use **Redis** for session storage
- Configure **Load Balancer**
- Set up **SSL/TLS** termination
- Enable **Monitoring** and **Alerting**
- Configure **Log Aggregation**

## 📈 Performance

- **Response Time**: < 50ms for proxy requests
- **Throughput**: 1000+ requests/second
- **Memory Usage**: < 512MB
- **CPU Usage**: < 50% under normal load

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit pull request

## 📄 License

MIT License - see LICENSE file for details


## Tasks

### Extracted Tasks

- [ ] **Salon Isolation**: Row-level security with salon ID - M1
- [ ] **Multiple Salon Resolution Methods**: - M2
- [ ] Header: `x-salon-id` - M3
- [ ] Query Parameter: `salonId` - M4
- [ ] Subdomain: `{salon-slug}.beauty-crm.com` - M5
- [ ] Path Parameter: `/api/salons/{salonId}/...` - M6
- [ ] **JWT-based Authentication** - M7
- [ ] **Role-based Access Control** - M8
- [ ] **Salon-User Validation** - M9
- [ ] **Cross-service Token Propagation** - M10
- [ ] **Intelligent Routing** to microservices - M11
- [ ] **Header Injection** for salon context - M12
- [ ] **Load Balancing** and **Failover** - M13
- [ ] **Request/Response Logging** - M14
- [ ] **Structured Logging** with salon context - M15
- [ ] **Health Checks** for all dependencies - M16
- [ ] **Performance Metrics** - M17
- [ ] **Error Tracking** - M18
- [ ] Node.js 18+ or Bun - M19
- [ ] PostgreSQL database - M20
- [ ] **Custom Authentication** providers - M21
- [ ] **Rate Limiting** strategies - M22
- [ ] **Caching** mechanisms - M23
- [ ] **Monitoring** integrations - M24
- [ ] **Custom Middleware** - M25
- [ ] **Helmet.js** for security headers - M26
- [ ] **CORS** configuration - M27
- [ ] **Rate Limiting** per IP/salon - M28
- [ ] **Input Validation** with Zod - M29
- [ ] **SQL Injection** protection via Prisma - M30
- [ ] **XSS Protection** - M31
- [ ] **CSRF Protection** - M32
- [ ] Use **Redis** for session storage - M33
- [ ] Configure **Load Balancer** - M34
- [ ] Set up **SSL/TLS** termination - M35
- [ ] Enable **Monitoring** and **Alerting** - M36
- [ ] Configure **Log Aggregation** - M37
- [ ] **Response Time**: < 50ms for proxy requests - M38
- [ ] **Throughput**: 1000+ requests/second - M39
- [ ] **Memory Usage**: < 512MB - M40
- [ ] **CPU Usage**: < 50% under normal load - M41

### Backend Tasks

- [ ] Implement API endpoints - M1
- [ ] Add input validation - M2
- [ ] Add error handling - M3
- [ ] Add unit tests - M4
- [ ] Add integration tests - M5
- [ ] Add documentation - M6

