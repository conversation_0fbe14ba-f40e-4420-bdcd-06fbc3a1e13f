# treatment-management-backend

To install dependencies:

```bash
bun install
```

To run:

```bash
bun run index.ts
```

This project was created using `bun init` in bun v1.2.15. [Bun](https://bun.sh) is a fast all-in-one JavaScript runtime.


## Tasks

### Backend Tasks

- [ ] Implement API endpoints - M1
- [ ] Add input validation - M2
- [ ] Add error handling - M3
- [ ] Add unit tests - M4
- [ ] Add integration tests - M5
- [ ] Add documentation - M6

