# Treatment Management Service

A comprehensive treatment catalog management service for beauty salon CRM, built with Domain-Driven Design (DDD) architecture.

## 🏗️ Architecture

### DDD Layers
```
src/
├── domain/                    # Business Logic Layer
│   ├── models/               # Domain entities
│   ├── repositories/         # Domain contracts
│   └── events/              # Domain events
├── application/              # Application Layer
│   └── services/            # Use cases & orchestration
├── infrastructure/          # Infrastructure Layer
│   ├── repositories/        # Database implementations
│   └── clients/            # External integrations
└── controllers/             # Presentation Layer
```

### Key Features
- ✅ **REST API** for management UI operations
- ✅ **NATS Events** for real-time integration
- ✅ **Rich Domain Model** with business logic
- ✅ **Event-driven Architecture** for loose coupling
- ✅ **Multi-tenant Support** via salon ID
- ✅ **Comprehensive Validation** with Zod schemas

## 🚀 Quick Start

### Development Mode
```bash
# Start development environment
./scripts/start-dev.sh

# Or manually:
cd treatment-management-backend
bun install
bun run db:generate
bun run dev
```

### Production Mode
```bash
# Start production environment
./scripts/start-prod.sh

# Or manually:
docker compose -f docker-compose.app.yml up -d --build
```

## 📡 API Endpoints

### Treatment Management
```http
GET    /api/treatments?salonId=salon1           # List all treatments
GET    /api/treatments/active?salonId=salon1    # Active treatments only
GET    /api/treatments/search?query=facial      # Search treatments
GET    /api/treatments/:id                      # Get specific treatment
POST   /api/treatments                          # Create treatment
PUT    /api/treatments/:id                      # Update treatment
PATCH  /api/treatments/:id/activate             # Activate treatment
PATCH  /api/treatments/:id/deactivate           # Deactivate treatment
DELETE /api/treatments/:id                      # Delete treatment
POST   /api/treatments/bulk/toggle-active       # Bulk activate/deactivate
```

### Health & Info
```http
GET    /health                                  # Health check
GET    /api                                     # API information
```

## 🔗 Service URLs

### Direct Access
- **Service**: http://localhost:4008
- **Health**: http://localhost:4008/health
- **API**: http://localhost:4008/api

### Via Traefik (Production)
- **Service**: http://treatment-management.localhost
- **Treatments**: http://treatments.localhost
- **API**: http://treatment-management.localhost/api

## 📊 Database

### Connection
```
Database: beauty_crm_treatment
URL: postgresql://beauty_crm:beauty_crm_password@localhost:5432/beauty_crm_treatment
```

### Schema
- **treatments**: Core treatment catalog
- **treatment_media**: Images, videos, icons
- **treatment_pricing**: Complex pricing rules
- **treatment_tags**: Flexible labeling
- **treatment_availability**: Time-based constraints
- **treatment_restrictions**: Business rules
- **treatment_add_ons**: Optional extras
- **treatment_analytics**: Performance metrics

## 📨 NATS Integration

### Published Events
```typescript
// Domain events for internal processing
'treatment.events' → Full domain events

// Simplified events for external services
'treatments.updated' → {
  salonId: string,
  treatments: Array<{id, name, duration, price}>,
  eventType: 'created' | 'updated' | 'deleted',
  timestamp: string
}
```

### Event Consumers
- **Appointment Service**: Updates treatment cache
- **Analytics Service**: Tracks metrics
- **Management UI**: Real-time updates

## 🐳 Docker Configuration

### Networks
- `network_backend`: Service communication
- `network_database`: Database access
- `beauty_crm_traefik-public`: Traefik routing

### Dependencies
- **PostgreSQL**: Database storage
- **NATS**: Event messaging
- **Traefik**: Reverse proxy

### Labels
```yaml
app.service: treatment-management
app.type: management
app.environment: production
```

## 🔧 Development

### Prerequisites
- Bun runtime
- Docker & Docker Compose
- PostgreSQL (via Docker)
- NATS (via Docker)

### Environment Variables
```bash
DATABASE_URL=postgresql://beauty_crm:beauty_crm_password@localhost:5432/beauty_crm_treatment
NATS_URL=nats://localhost:4222
PORT=4008
HOST=0.0.0.0
SERVICE_NAME=treatment-management-backend
```

### Scripts
```bash
bun run dev          # Development server
bun run start        # Production server
bun run build        # Build application
bun run db:generate  # Generate Prisma client
bun run db:push      # Push schema changes
bun run db:migrate   # Run migrations
bun run db:studio    # Open Prisma Studio
```

## 🧪 Testing

### API Testing
```bash
# Health check
curl http://localhost:4008/health

# List treatments
curl "http://localhost:4008/api/treatments?salonId=salon1"

# Create treatment
curl -X POST http://localhost:4008/api/treatments \
  -H "Content-Type: application/json" \
  -d '{
    "salonId": "salon1",
    "name": "Deep Cleansing Facial",
    "description": "Comprehensive facial treatment",
    "duration": 60,
    "basePrice": 85.00
  }'
```

### Via Traefik
```bash
# Health check
curl http://treatment-management.localhost/health

# List treatments
curl "http://treatment-management.localhost/api/treatments?salonId=salon1"
```

## 🔄 Integration

### With Appointment Service
1. **NATS Subscription**: Appointment service subscribes to `treatments.updated`
2. **Local Cache**: Maintains treatment cache for fast booking
3. **Validation**: Validates treatment IDs during appointment creation

### With Management UI
1. **REST API**: Direct API calls for CRUD operations
2. **Real-time Updates**: WebSocket or polling for live updates
3. **Bulk Operations**: Efficient management of multiple treatments

### With Planner UI
1. **Treatment Display**: Shows available treatments from cache
2. **Real-time Updates**: Reflects treatment changes immediately
3. **Booking Integration**: Uses treatment info for appointment creation

## 📈 Monitoring

### Health Checks
- **Application**: `/health` endpoint
- **Database**: Connection validation
- **NATS**: Connection status

### Metrics
- **Treatment Operations**: CRUD statistics
- **Event Publishing**: NATS message counts
- **API Performance**: Response times
- **Cache Hit Rates**: Treatment lookup efficiency

## 🚨 Troubleshooting

### Common Issues
1. **Database Connection**: Check PostgreSQL container status
2. **NATS Connection**: Verify NATS container and network
3. **Traefik Routing**: Check service labels and network configuration
4. **Port Conflicts**: Ensure port 4008 is available

### Logs
```bash
# Application logs
docker compose -f docker-compose.app.yml logs -f treatment-management-backend

# Database logs
docker compose -f ../appointment/docker-compose.databases.yml logs -f postgres

# NATS logs
docker compose -f ../appointment/docker-compose.databases.yml logs -f nats
```

## 🔮 Future Enhancements

### Planned Features
- **Categories**: Hierarchical treatment organization
- **Media Management**: Image and video uploads
- **Complex Pricing**: Time-based and seasonal pricing
- **Prerequisites**: Treatment dependencies
- **Analytics**: Advanced reporting and insights
- **Multi-language**: Internationalization support

### Scalability
- **Horizontal Scaling**: Multiple service instances
- **Database Sharding**: Salon-based partitioning
- **Caching Layer**: Redis for frequently accessed data
- **CDN Integration**: Media asset delivery


## Tasks

### Extracted Tasks

- [ ] ✅ **REST API** for management UI operations - M1
- [ ] ✅ **NATS Events** for real-time integration - M2
- [ ] ✅ **Rich Domain Model** with business logic - M3
- [ ] ✅ **Event-driven Architecture** for loose coupling - M4
- [ ] ✅ **Multi-tenant Support** via salon ID - M5
- [ ] ✅ **Comprehensive Validation** with Zod schemas - M6
- [ ] **Service**: http://localhost:4008 - M7
- [ ] **Health**: http://localhost:4008/health - M8
- [ ] **API**: http://localhost:4008/api - M9
- [ ] **Service**: http://treatment-management.localhost - M10
- [ ] **Treatments**: http://treatments.localhost - M11
- [ ] **API**: http://treatment-management.localhost/api - M12
- [ ] **treatments**: Core treatment catalog - M13
- [ ] **treatment_media**: Images, videos, icons - M14
- [ ] **treatment_pricing**: Complex pricing rules - M15
- [ ] **treatment_tags**: Flexible labeling - M16
- [ ] **treatment_availability**: Time-based constraints - M17
- [ ] **treatment_restrictions**: Business rules - M18
- [ ] **treatment_add_ons**: Optional extras - M19
- [ ] **treatment_analytics**: Performance metrics - M20
- [ ] **Appointment Service**: Updates treatment cache - M21
- [ ] **Analytics Service**: Tracks metrics - M22
- [ ] **Management UI**: Real-time updates - M23
- [ ] `network_backend`: Service communication - M24
- [ ] `network_database`: Database access - M25
- [ ] `beauty_crm_traefik-public`: Traefik routing - M26
- [ ] **PostgreSQL**: Database storage - M27
- [ ] **NATS**: Event messaging - M28
- [ ] **Traefik**: Reverse proxy - M29
- [ ] Bun runtime - M30
- [ ] Docker & Docker Compose - M31
- [ ] PostgreSQL (via Docker) - M32
- [ ] NATS (via Docker) - M33
- [ ] **Application**: `/health` endpoint - M34
- [ ] **Database**: Connection validation - M35
- [ ] **NATS**: Connection status - M36
- [ ] **Treatment Operations**: CRUD statistics - M37
- [ ] **Event Publishing**: NATS message counts - M38
- [ ] **API Performance**: Response times - M39
- [ ] **Cache Hit Rates**: Treatment lookup efficiency - M40
- [ ] **Categories**: Hierarchical treatment organization - M41
- [ ] **Media Management**: Image and video uploads - M42
- [ ] **Complex Pricing**: Time-based and seasonal pricing - M43
- [ ] **Prerequisites**: Treatment dependencies - M44
- [ ] **Analytics**: Advanced reporting and insights - M45
- [ ] **Multi-language**: Internationalization support - M46
- [ ] **Horizontal Scaling**: Multiple service instances - M47
- [ ] **Database Sharding**: Salon-based partitioning - M48
- [ ] **Caching Layer**: Redis for frequently accessed data - M49
- [ ] **CDN Integration**: Media asset delivery - M50

### General Tasks

- [ ] Review and update content - M1
- [ ] Add missing information - M2
- [ ] Improve structure - M3
- [ ] Add examples - M4
- [ ] Validate accuracy - M5

