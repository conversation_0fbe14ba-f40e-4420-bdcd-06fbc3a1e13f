# Sprint 3: Appointment System Integration
*Cross-System Data Synchronization & Real-Time Visibility*

## Sprint Overview

**Sprint Goal:** Integrate the 4 appointment systems so appointments booked through the client-facing planner are immediately visible in the salon management calendar.

**Sprint Duration:** 3 weeks (15 working days)
**Team Capacity:** 4 developers, 1 DevOps engineer  
**Story Points Commitment:** 89 points

## Business Context

Currently, appointments booked through the public planner system (appointment-planner-*) are isolated from the internal salon management system (appointment-management-*). This creates operational blind spots where salon staff cannot see client appointments booked online, leading to:

- Double bookings
- Poor customer experience 
- Manual workarounds
- Disconnected appointment data

Sprint 3 will establish bidirectional data flow between systems, enabling:
- Real-time appointment visibility across all systems
- Unified appointment status management
- Consistent customer experience 
- Operational efficiency for salon staff

## Current System Analysis

### System Architecture Overview
```
┌─────────────────────┐    ┌─────────────────────┐
│   Planner Frontend  │    │ Management Frontend │
│   (Client-facing)   │    │   (Staff-facing)    │
└──────────┬──────────┘    └──────────┬──────────┘
           │                          │
           │ POST /api/v1/appointments │ GET /api/appointments
           ▼                          ▼
┌─────────────────────┐    ┌─────────────────────┐
│  Planner Backend    │    │ Management Backend  │
│  (Port 4000)        │    │  (Port 4000)         │
│  ┌─────────────────┐│    │  ┌─────────────────┐│
│  │ planner DB ││    │  │ appointments DB ││
│  │ (customer data) ││    │  │ (staff data)    ││
│  └─────────────────┘│    │  └─────────────────┘│
└─────────────────────┘    └─────────────────────┘
       (ISOLATED)                 (ISOLATED)
```
### Data Model Conflicts

**Planner System (External)**
```typescript
interface PlannerAppointment {
  id: string;
  salonId: string;
  salonName: string;
  customerName: string;    // ← Customer focus
  customerEmail: string;
  customerPhone?: string;
  treatmentName: string;     // ← Service as string
  treatmentDuration: number;
  treatmentPrice: number;
  staffId?: string;   // ← Optional specialist
  startTime: DateTime;
  endTime: DateTime;
  status: 'PENDING' | 'CONFIRMED' | 'CANCELLED' | 'COMPLETED';
}
```
**Management System (Internal)**
```typescript
interface ManagementAppointment {
  id: string;
  customerId: string;        // ← Client relationship
  staffId: string;         // ← Required staff
  treatmentId: string;       // ← Service relationship  
  stationId?: string;      // ← Station management
  startTime: string;
  endTime: string;
  status: 'SCHEDULED' | 'CONFIRMED' | 'CANCELLED' | 'COMPLETED' | 'RESCHEDULED';
  notes?: string;
}
```
## Integration Strategy

### Phase 1: Data Bridge Architecture (Week 1) - 34 points

#### Epic 1.1: Shared Data Models (13 points)

**Task 1.1.1: Create Unified Appointment Interface (5 points)**
```typescript
// shared-appointment-types/src/UnifiedAppointment.ts
export interface UnifiedAppointment {
  // Core identifiers
  id: string;
  externalId?: string;     // For cross-system mapping
  
  // Customer/Client data
  customerId: string;
  customerName: string;
  customerEmail: string;
  customerPhone?: string;
  
  // Salon/Staff data  
  salonId: string;
  salonName?: string;
  staffId: string;
  staffName?: string;
  stationId?: string;
  
  // Service data
  treatmentId: string;
  treatmentName: string;
  treatmentDuration: number;
  treatmentPrice: number;
  
  // Scheduling
  startTime: Date;
  endTime: Date;
  timezone?: string;
  
  // Status & metadata
  status: UnifiedAppointmentStatus;
  source: 'PLANNER' | 'MANAGEMENT';
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

export enum UnifiedAppointmentStatus {
  PENDING = 'PENDING',
  CONFIRMED = 'CONFIRMED', 
  SCHEDULED = 'SCHEDULED',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  NO_SHOW = 'NO_SHOW',
  RESCHEDULED = 'RESCHEDULED'
}
```
**Task 1.1.2: Create Data Transformation Layer (8 points)**
```typescript
// shared-appointment-types/src/transformers.ts
export class AppointmentTransformer {
  
  static plannerToUnified(plannerAppt: PlannerAppointment): UnifiedAppointment {
    return {
      id: plannerAppt.id,
      customerId: this.generateCustomerId(plannerAppt.customerEmail),
      customerName: plannerAppt.customerName,
      customerEmail: plannerAppt.customerEmail,
      customerPhone: plannerAppt.customerPhone,
      salonId: plannerAppt.salonId,
      salonName: plannerAppt.salonName,
      staffId: plannerAppt.staffId || 'any',
      treatmentId: this.generateTreatmentId(plannerAppt.treatmentName),
      treatmentName: plannerAppt.treatmentName,
      treatmentDuration: plannerAppt.treatmentDuration,
      treatmentPrice: plannerAppt.treatmentPrice,
      startTime: new Date(plannerAppt.startTime),
      endTime: new Date(plannerAppt.endTime),
      status: this.mapPlannerStatus(plannerAppt.status),
      source: 'PLANNER',
      createdAt: new Date(),
      updatedAt: new Date()
    };
  }
  
  static managementToUnified(mgmtAppt: ManagementAppointment): UnifiedAppointment {
    // Implementation for reverse transformation
  }
  
  static unifiedToManagement(unified: UnifiedAppointment): ManagementAppointment {
    // Transform unified model to management format
  }
  
  static unifiedToPlanner(unified: UnifiedAppointment): PlannerAppointment {
    // Transform unified model to planner format  
  }
}
```
#### Epic 1.2: Event-Driven Synchronization (21 points)

**Task 1.2.1: Event Bus Infrastructure (8 points)**
```typescript
// shared-event-bus/src/AppointmentEventBus.ts
export interface AppointmentEvent {
  eventId: string;
  eventType: 'CREATED' | 'UPDATED' | 'CANCELLED' | 'CONFIRMED';
  source: 'PLANNER' | 'MANAGEMENT';
  appointment: UnifiedAppointment;
  timestamp: Date;
  metadata?: Record<string, unknown>;
}

export class AppointmentEventBus {
  private subscribers: Map<string, EventHandler[]> = new Map();
  
  publish(event: AppointmentEvent): Promise<void> {
    // Publish to Redis/Message Queue
    // Notify all registered subscribers
  }
  
  subscribe(eventType: string, handler: EventHandler): void {
    // Register event handlers
  }
  
  // Integration with external systems
  publishToWebhook(event: AppointmentEvent, webhookUrl: string): Promise<void> {
    // HTTP webhook notifications
  }
}
```
**Task 1.2.2: Planner System Event Integration (8 points)**
```typescript
// appointment-planner-backend/src/services/EventPublisher.ts
export class AppointmentEventPublisher {
  constructor(private eventBus: AppointmentEventBus) {}
  
  async publishAppointmentCreated(appointment: PlannerAppointment): Promise<void> {
    const unifiedAppointment = AppointmentTransformer.plannerToUnified(appointment);
    
    await this.eventBus.publish({
      eventId: cuid(),
      eventType: 'CREATED',
      source: 'PLANNER',
      appointment: unifiedAppointment,
      timestamp: new Date(),
      metadata: {
        salonId: appointment.salonId,
        customerEmail: appointment.customerEmail
      }
    });
  }
}

// Update appointmentController.ts
export class AppointmentController {
  constructor(
    private appointmentService: AppointmentService,
    private eventPublisher: AppointmentEventPublisher  // ← Add event publisher
  ) {}

  createAppointment = async (c: ValidatedContext<AppointmentInput>) => {
    try {
      const appointment = await this.appointmentService.createAppointment(data);
      
      // Publish event for cross-system synchronization  
      await this.eventPublisher.publishAppointmentCreated(appointment);
      
      return c.json(appointment, 201);
    } catch (error) {
      // ... existing error handling
    }
  };
}
```
**Task 1.2.3: Management System Event Integration (5 points)**
```typescript
// appointment-management-backend/src/services/EventSubscriber.ts
export class AppointmentEventSubscriber {
  constructor(
    private appointmentService: AppointmentService,
    private eventBus: AppointmentEventBus
  ) {
    this.setupSubscriptions();
  }
  
  private setupSubscriptions(): void {
    this.eventBus.subscribe('CREATED', this.handleAppointmentCreated.bind(this));
    this.eventBus.subscribe('UPDATED', this.handleAppointmentUpdated.bind(this));
  }
  
  private async handleAppointmentCreated(event: AppointmentEvent): Promise<void> {
    if (event.source === 'PLANNER') {
      // Transform and create appointment in management system
      const managementAppointment = AppointmentTransformer.unifiedToManagement(event.appointment);
      await this.appointmentService.createFromExternal(managementAppointment);
      
      console.log(`Synced appointment ${event.appointment.id} from planner to management`);
    }
  }
}
```
### Phase 2: Real-Time Data Flow (Week 2) - 34 points

#### Epic 2.1: Management Backend API Integration (18 points)

**Task 2.1.1: Replace Mock Data with Real API (13 points)**
```typescript
// appointment-management-backend/src/infrastructure/repositories/PostgresAppointmentRepository.ts
export class PostgresAppointmentRepository implements AppointmentRepository {
  constructor(private prisma: PrismaClient) {}
  
  async getAllAppointments(): Promise<Appointment[]> {
    const appointments = await this.prisma.appointment.findMany({
      include: {
        stylist: true,
        treatment: {
          include: {
            treatment: true
          }
        }
      }
    });
    
    return appointments.map(this.mapToAppointment);
  }
  
  async createAppointment(appointment: Appointment): Promise<Appointment> {
    // Create appointment with proper relationships
    const created = await this.prisma.appointment.create({
      data: {
        id: appointment.id,
        customerId: appointment.customerId,
        staffId: appointment.staffId,
        treatmentId: appointment.treatmentId,
        startTime: new Date(appointment.startTime),
        endTime: new Date(appointment.endTime),
        status: appointment.status,
        notes: appointment.notes
      },
      include: {
        stylist: true,
        treatment: {
          include: { treatment: true }
        }   
      }
    });
    
    return this.mapToAppointment(created);
  }
  
  async createFromExternal(externalAppointment: UnifiedAppointment): Promise<Appointment> {
    // Handle client creation/lookup
    const customer = await this.ensureClientExists({
      id: externalAppointment.customerId,
      name: externalAppointment.customerName,
      email: externalAppointment.customerEmail,
      phone: externalAppointment.customerPhone
    });
    
    // Handle treatment mapping
    const treatment = await this.ensureTreatmentExists({
      name: externalAppointment.treatmentName,
      duration: externalAppointment.treatmentDuration,
      price: externalAppointment.treatmentPrice
    });
    
    // Create appointment
    return this.createAppointment({
      id: cuid(),
      customerId: customer.id,    
      staffId: externalAppointment.staffId === 'any' ? await this.findAvailableStaff() : externalAppointment.staffId,
      treatmentId: treatment.id,
      startTime: externalAppointment.startTime.toISOString(),
      endTime: externalAppointment.endTime.toISOString(),
      status: this.mapStatus(externalAppointment.status),
      notes: `Booked via ${externalAppointment.source}. External ID: ${externalAppointment.id}`
    });
  }
}
```
**Task 2.1.2: Update Management Controllers for Real Data (5 points)**
```typescript
// appointment-management-backend/src/controllers/AppointmentController.ts
export class AppointmentController {
  constructor(private appointmentService: AppointmentService) {}

  getAll = async (c: Context) => {
    try {
      const appointments = await this.appointmentService.getAllAppointments();
      return c.json(appointments);
    } catch (error) {
      return c.json({ error: 'Failed to fetch appointments' }, 500);
    }
  };

  getByDateRange = async (c: Context) => {
    try {
      const startDate = c.req.query('startDate');
      const endDate = c.req.query('endDate');
      
      if (!startDate || !endDate) {
        return c.json({ error: 'startDate and endDate are required' }, 400);
      }

      const appointments = await this.appointmentService.getAppointmentsByDateRange(
        new Date(startDate),
        new Date(endDate)
      );
      
      return c.json(appointments);
    } catch (error) {
      return c.json({ error: 'Failed to fetch appointments' }, 500);
    }
  };

  getByStaffAndDateRange = async (c: Context) => {
    try {
      const staffId = c.req.param('staffId');
      const startDate = c.req.query('startDate');
      const endDate = c.req.query('endDate');
      
      if (!startDate || !endDate) {
        return c.json({ error: 'startDate and endDate are required' }, 400);
      }

      const appointments = await this.appointmentService.getAppointmentsByStaffAndDateRange(
        staffId,
        new Date(startDate), 
        new Date(endDate)
      );
      
      return c.json(appointments);
    } catch (error) {
      return c.json({ error: 'Failed to fetch appointments' }, 500);
    }
  };
}
```
#### Epic 2.2: Frontend Real-Time Updates (16 points)

**Task 2.2.1: Replace Mock Data with API Calls (8 points)**
```typescript
// appointment-management-frontend/src/services/appointmentApi.ts
export class AppointmentApiService {
  private baseUrl: string;
  
  constructor(baseUrl = process.env.VITE_MANAGEMENT_API_URL || 'http://localhost:3001') {
    this.baseUrl = baseUrl;
  }
  
  async getAllAppointments(): Promise<Appointment[]> {
    const response = await fetch(`${this.baseUrl}/api/appointments`);
    if (!response.ok) {
      throw new Error(`Failed to fetch appointments: ${response.statusText}`);
    }
    return response.json();
  }
  
  async getAppointmentsByStaffAndDateRange(
    staffId: string,
    startDate: Date,
    endDate: Date
  ): Promise<Appointment[]> {
    const params = new URLSearchParams({
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString()
    });
    
    const response = await fetch(
      `${this.baseUrl}/api/appointments/staff/${staffId}/date-range?${params}`
    );
    
    if (!response.ok) {
      throw new Error(`Failed to fetch appointments: ${response.statusText}`);
    }
    
    return response.json();
  }
  
  async createAppointment(appointment: Omit<Appointment, 'id'>): Promise<Appointment> {
    const response = await fetch(`${this.baseUrl}/api/appointments`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(appointment)
    });
    
    if (!response.ok) {
      throw new Error(`Failed to create appointment: ${response.statusText}`);
    }
    
    return response.json();
  }
}
```
**Task 2.2.2: Update useAppointmentData Hook (8 points)**
```typescript
// appointment-management-frontend/src/product-features/appointments/useAppointmentData.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { AppointmentApiService } from '@/services/appointmentApi';

export function useAppointmentData() {
  const queryClient = useQueryClient();
  const appointmentApi = new AppointmentApiService();
  
  const [selectedStaff, setSelectedStaff] = useState<string | null>(null);
  const [currentDate, setCurrentDate] = useState(new Date());
  
  // Calculate date range for current view (e.g., current month)
  const startDate = useMemo(() => {
    const start = new Date(currentDate);
    start.setDate(1); // First day of month
    return start;
  }, [currentDate]);
  
  const endDate = useMemo(() => {
    const end = new Date(currentDate);
    end.setMonth(end.getMonth() + 1);
    end.setDate(0); // Last day of month
    return end;
  }, [currentDate]);

  // Fetch appointments with React Query
  const { 
    data: appointments = [], 
    isLoading: isLoadingData,
    error: appointmentsError
  } = useQuery({
    queryKey: ['appointments', selectedStaff, startDate, endDate],
    queryFn: () => selectedStaff 
      ? appointmentApi.getAppointmentsByStaffAndDateRange(selectedStaff, startDate, endDate)
      : appointmentApi.getAllAppointments(),
    enabled: !!selectedStaff,
    staleTime: 30000, // 30 seconds
    refetchInterval: 60000, // Refetch every minute for real-time updates
  });

  // Create appointment mutation
  const createAppointmentMutation = useMutation({
    mutationFn: appointmentApi.createAppointment,
    onSuccess: () => {
      // Invalidate and refetch appointments
      queryClient.invalidateQueries({ queryKey: ['appointments'] });
    },
    onError: (error) => {
      console.error('Failed to create appointment:', error);
    }
  });

  // Transform appointments to calendar events
  const events = useMemo(() => {
    return appointments.map((appointment) => ({
      id: appointment.id,
      title: `${appointment.treatment?.name || 'Treatment'} - ${appointment.client?.name || 'Client'}`,
      start: appointment.startTime,
      end: appointment.endTime,
      backgroundColor: getStatusColor(appointment.status),
      borderColor: getStatusBorderColor(appointment.status),
      textColor: '#fff',
      extendedProps: { appointment }
    }));
  }, [appointments]);

  const handleAppointmentCreated = useCallback((newAppointment: Appointment) => {
    createAppointmentMutation.mutate(newAppointment);
  }, [createAppointmentMutation]);

  return {
    appointments,
    events,
    isLoadingData,
    appointmentsError,
    selectedStaff,
    setSelectedStaff,
    currentDate,
    setCurrentDate,
    handleAppointmentCreated,
    // ... other existing methods
  };
}
```
### Phase 3: Cross-System Status Sync (Week 3) - 21 points

#### Epic 3.1: Bidirectional Status Updates (13 points)

**Task 3.1.1: Status Change Event Propagation (8 points)**
```typescript
// shared-appointment-types/src/StatusSyncService.ts
export class AppointmentStatusSyncService {
  constructor(
    private eventBus: AppointmentEventBus,
    private plannerService: PlannerAppointmentService,
    private managementService: ManagementAppointmentService
  ) {}
  
  async syncStatusChange(
    appointmentId: string,
    newStatus: UnifiedAppointmentStatus,
    source: 'PLANNER' | 'MANAGEMENT'
  ): Promise<void> {
    
    // Update in source system
    if (source === 'PLANNER') {
      await this.plannerService.updateStatus(appointmentId, newStatus);
      // Propagate to management system
      await this.managementService.updateStatusFromExternal(appointmentId, newStatus);
    } else {
      await this.managementService.updateStatus(appointmentId, newStatus); 
      // Propagate to planner system
      await this.plannerService.updateStatusFromExternal(appointmentId, newStatus);
    }
    
    // Publish event for any other subscribers
    await this.eventBus.publish({
      eventId: cuid(),
      eventType: 'UPDATED',
      source,
      appointment: await this.getUnifiedAppointment(appointmentId),
      timestamp: new Date(),
      metadata: { statusChange: { newStatus, source } }
    });
  }
}
```
**Task 3.1.2: Conflict Resolution (5 points)**
```typescript
// shared-appointment-types/src/ConflictResolver.ts
export class AppointmentConflictResolver {
  
  resolveStatusConflict(
    plannerStatus: UnifiedAppointmentStatus,
    managementStatus: UnifiedAppointmentStatus,
    lastUpdated: { planner: Date; management: Date }
  ): UnifiedAppointmentStatus {
    
    // Priority rules for status conflicts
    const statusPriority = {
      CANCELLED: 10,     // Cancellation always wins
      COMPLETED: 9,      // Completion has high priority  
      IN_PROGRESS: 8,    // Current session state
      CONFIRMED: 7,      // Confirmed by staff
      SCHEDULED: 6,      // Scheduled by staff
      PENDING: 5,        // Initial state
      NO_SHOW: 4,        // Negative outcome
      RESCHEDULED: 3     // Transition state
    };
    
    // If same status, no conflict
    if (plannerStatus === managementStatus) {
      return plannerStatus;
    }
    
    // Use priority-based resolution
    const plannerPriority = statusPriority[plannerStatus];
    const managementPriority = statusPriority[managementStatus];
    
    if (plannerPriority > managementPriority) {
      return plannerStatus;
    } else if (managementPriority > plannerPriority) {
      return managementStatus;
    } else {
      // Same priority - use timestamp (last updated wins)
      return lastUpdated.planner > lastUpdated.management 
        ? plannerStatus 
        : managementStatus;
    }
  }
}
```
#### Epic 3.2: Real-Time WebSocket Updates (8 points)

**Task 3.2.1: WebSocket Event Broadcasting (5 points)**
```typescript
// shared-event-bus/src/WebSocketEventBroadcaster.ts
export class WebSocketEventBroadcaster {
  private wss: WebSocketServer;
  
  constructor(private eventBus: AppointmentEventBus) {
    this.setupWebSocketServer();
    this.subscribeToEvents();
  }
  
  private setupWebSocketServer(): void {
    this.wss = new WebSocketServer({ port: 8080 });
    
    this.wss.on('connection', (ws, req) => {
      console.log('Client connected to appointment events');
      
      // Send current appointment state on connection
      this.sendInitialState(ws);
      
      ws.on('close', () => {
        console.log('Client disconnected from appointment events');
      });
    });
  }
  
  private subscribeToEvents(): void {
    this.eventBus.subscribe('CREATED', this.broadcastEvent.bind(this));
    this.eventBus.subscribe('UPDATED', this.broadcastEvent.bind(this));
    this.eventBus.subscribe('CANCELLED', this.broadcastEvent.bind(this));
  }
  
  private broadcastEvent(event: AppointmentEvent): void {
    const message = JSON.stringify({
      type: 'APPOINTMENT_EVENT',
      event
    });
    
    this.wss.clients.forEach((client) => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(message);
      }
    });
  }
}
```
**Task 3.2.2: Frontend WebSocket Integration (3 points)**
```typescript
// appointment-management-frontend/src/hooks/useAppointmentWebSocket.ts
export function useAppointmentWebSocket() {
  const queryClient = useQueryClient();
  const [isConnected, setIsConnected] = useState(false);
  
  useEffect(() => {
    const ws = new WebSocket(process.env.VITE_WEBSOCKET_URL || 'ws://localhost:8080');
    
    ws.onopen = () => {
      setIsConnected(true);
      console.log('Connected to appointment events');
    };
    
    ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      
      if (data.type === 'APPOINTMENT_EVENT') {
        const appointmentEvent = data.event as AppointmentEvent;
        
        // Invalidate relevant queries to trigger refetch
        queryClient.invalidateQueries({
          queryKey: ['appointments']
        });
        
        // Show notification for real-time updates
        if (appointmentEvent.source === 'PLANNER') {
          showNotification({
            title: 'New Appointment', 
            message: `${appointmentEvent.appointment.customerName} booked ${appointmentEvent.appointment.treatmentName}`,
            color: 'green'
          });
        }
      }
    };
    
    ws.onclose = () => {
      setIsConnected(false);
      console.log('Disconnected from appointment events');
    };
    
    return () => {
      ws.close();
    };
  }, [queryClient]);
  
  return { isConnected };
}

// Update useAppointmentData to include WebSocket
export function useAppointmentData() {
  const { isConnected } = useAppointmentWebSocket();
  
  // ... existing code
  
  return {
    // ... existing returns
    isConnected
  };
}
```
## Environment Configuration

### Development Environment Setup
```bash
# Terminal 1: Planner Backend
cd services/appointment/appointment-planner-backend
bun install && bun run dev

# Terminal 2: Planner Frontend  
cd services/appointment/appointment-planner-frontend
bun install && bun run dev

# Terminal 3: Management Backend
cd services/appointment/appointment-management-backend
bun install && bun run dev

# Terminal 4: Management Frontend
cd services/appointment/appointment-management-frontend
bun install && bun run dev

# Terminal 5: Event Bus & WebSocket
cd shared-event-bus
bun install && bun run dev
```
### Environment Variables
```bash
# appointment-planner-backend/.env
DATABASE_URL="postgresql://user:pass@localhost:5432/planner_db"
MANAGEMENT_API_URL="http://localhost:3001"
EVENT_BUS_URL="redis://localhost:6379"

# appointment-management-backend/.env  
DATABASE_URL="postgresql://user:pass@localhost:5432/management_db"
PLANNER_API_URL="http://localhost:5016"
EVENT_BUS_URL="redis://localhost:6379"

# Frontend .env files
VITE_PLANNER_API_URL="http://localhost:5016"
VITE_MANAGEMENT_API_URL="http://localhost:3001"
VITE_WEBSOCKET_URL="ws://localhost:8080"
```
## Testing Strategy

### Integration Tests
```typescript
// tests/integration/appointment-sync.test.ts
describe('Appointment Cross-System Sync', () => {
  it('should sync appointment from planner to management', async () => {
    // 1. Create appointment in planner
    const appointment = await plannerApi.createAppointment({
      salonId: 'test-salon',
      customerName: 'John Doe',
      customerEmail: '<EMAIL>',
      treatmentName: 'Haircut',
      treatmentDuration: 30,
      treatmentPrice: 50,
      startTime: new Date('2024-02-01T10:00:00Z'),
      endTime: new Date('2024-02-01T10:30:00Z')
    });
    
    // 2. Wait for event propagation  
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 3. Verify appointment appears in management system
    const managementAppointments = await managementApi.getAllAppointments();
    const syncedAppointment = managementAppointments.find(
      a => a.notes?.includes(appointment.id)
    );
    
    expect(syncedAppointment).toBeDefined();
    expect(syncedAppointment.customerName).toBe('John Doe');
    expect(syncedAppointment.treatmentName).toBe('Haircut');
  });
  
  it('should handle status updates bidirectionally', async () => {
    // Test status sync from management to planner
    // Test conflict resolution
    // Test WebSocket notifications
  });
});
```
### E2E Tests
```typescript
// tests/e2e/cross-system-flow.spec.ts
test('complete appointment flow across systems', async ({ page }) => {
  // 1. Client books appointment in planner
  await page.goto('http://localhost:3000'); // Planner frontend
  await page.click('[data-testid="book-appointment"]');
  // ... fill form and submit
  
  // 2. Staff sees appointment in management system
  await page.goto('http://localhost:3002'); // Management frontend  
  await page.waitForSelector('[data-testid="appointment-calendar"]');
  await expect(page.locator('.appointment-event')).toContainText('John Doe');
  
  // 3. Staff confirms appointment
  await page.click('.appointment-event');
  await page.click('[data-testid="confirm-appointment"]');
  
  // 4. Verify status updated in planner
  // ... navigation and verification logic
});
```

## Risk Management

| Risk | Impact | Likelihood | Mitigation |
|------|--------|------------|------------|
| Data synchronization lag | High | Medium | Implement retry logic, monitoring, and manual sync tools |
| Database schema conflicts | High | Medium | Thorough mapping and transformation layer testing |
| Event bus failure | High | Low | Fallback HTTP webhooks, local queue buffering |
| WebSocket connection issues | Medium | Medium | Graceful degradation to polling, reconnection logic |
| Performance degradation | Medium | Medium | Event throttling, async processing, monitoring |

## Success Metrics

### Technical Metrics
- **Sync Latency**: < 2 seconds for appointment propagation
- **Data Consistency**: > 99.9% across systems  
- **API Response Time**: < 500ms for appointment queries
- **WebSocket Uptime**: > 99.5%

### Business Metrics  
- **Staff Efficiency**: 30% reduction in appointment coordination time
- **Double Booking Rate**: < 0.1% (down from current manual tracking issues)
- **Customer Experience**: Real-time appointment confirmations
- **System Adoption**: 100% of appointments visible across systems

## Deployment Strategy

### Week 1: Infrastructure
1. Deploy shared data models package
2. Set up event bus infrastructure (Redis)
3. Database migrations for cross-system mapping

### Week 2: Backend Integration
1. Deploy updated planner-backend with event publishing
2. Deploy updated appointment-management-backend with event subscription  
ption  
3. Gradual rollout with feature flags

### Week 3: Frontend & Real-Time
1. Deploy management-frontend with real API integration
2. Enable WebSocket broadcasting
3. Full integration testing and monitoring

---

**Sprint 3 Completion Criteria:**
✅ Appointments booked in planner appear in management calendar within 2 seconds  
✅ Status changes sync bidirectionally between systems
✅ Real-time WebSocket updates for management frontend
✅ Comprehensive integration and E2E test coverage
✅ Production monitoring and alerting in place