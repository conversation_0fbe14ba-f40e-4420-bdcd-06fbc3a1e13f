# Beauty CRM Tilt Development Environment

This document describes how to use Tilt for local development of the Beauty CRM system.

## 🚀 Quick Start

### Prerequisites

1. **Install Tilt**: Follow the [official installation guide](https://docs.tilt.dev/install.html)
2. **Docker & Docker Compose**: Ensure Docker is running
3. **Bun**: Make sure Bun is installed for package management

### Basic Usage

```bash
# Start with default services (databases, proxy, salon, treatment, appointment)
tilt up

# Start with specific services
tilt up -- --monitoring --api_gateway

# Stop all services
tilt down
```

## 📋 Available Services

### Infrastructure Services

| Service | Flag | Description | Default |
|---------|------|-------------|---------|
| **Databases** | `--databases` | Redis, PostgreSQL, NATS | ✅ |
| **Proxy** | `--proxy` | Traefik, Dashy | ✅ |
| **Monitoring** | `--monitoring` | SigNoz APM stack | ❌ |
| **API Gateway** | `--api_gateway` | Unified routing gateway | ❌ |

### Application Services

| Service | Flag | Description | Default |
|---------|------|-------------|---------|
| **Salon** | `--salon` | Salon management system | ✅ |
| **Treatment** | `--treatment` | Treatment management system | ✅ |
| **Appointment** | `--appointment` | Appointment booking system | ✅ |
| **Staff** | `--staff` | Staff management system | ❌ |
| **Identity** | `--identity` | Authentication services | ❌ |
| **ELK** | `--elk` | Elasticsearch, Logstash, Kibana | ❌ |

## 🎯 Common Development Scenarios

### Full Development Environment
```bash
# All services for comprehensive testing
tilt up -- --monitoring --api_gateway --staff --identity
```

### Minimal Setup
```bash
# Just databases and one service
tilt up -- --no-proxy --no-appointment --no-treatment
```

### Monitoring Focus
```bash
# Infrastructure with monitoring
tilt up -- --monitoring --elk --no-salon --no-treatment --no-appointment
```

### API Gateway Testing
```bash
# Test unified routing
tilt up -- --api_gateway --staff
```

## 🌐 Access Points

### Main Applications
- **Beauty CRM**: http://beauty-crm.localhost
- **Salon Management**: http://salon.localhost
- **Treatment Management**: http://treatment.localhost
- **Appointment Management**: http://appointment.localhost

### Infrastructure Dashboards
- **Traefik Dashboard**: http://traefik.localhost
- **Dashy Dashboard**: http://dashboard.localhost

- **NATS Monitoring**: http://nats.localhost

### Development Tools
- **Tilt UI**: http://tilt.localhost (via Traefik)
- **Tilt UI (Direct)**: http://localhost:10350
- **SigNoz APM**: http://localhost:3301 (when monitoring enabled)

## 🔧 Development Features

### Live Updates
All application services support live updates:
- **Backend**: Source code changes trigger automatic rebuilds
- **Frontend**: Asset and source changes are synced instantly
- **Dependencies**: Package.json changes trigger `bun install`

### Resource Dependencies
Services start in the correct order:
1. Infrastructure (databases, proxy)
2. Backend services
3. Frontend services
4. API Gateway proxies

### Service Labels
Services are organized by labels in the Tilt UI:
- `infrastructure`: Core infrastructure services
- `application`: Business logic services
- `monitoring`: Observability tools
- `backend`/`frontend`: Service type classification

## 📊 Monitoring & Observability

### Health Checks
All services include health checks:
- **Databases**: Connection and readiness checks
- **Applications**: `/health` endpoint monitoring
- **Proxy**: Service availability checks

### Logging
Centralized logging configuration:
- **JSON format**: Structured logs for all services
- **Rotation**: 10MB max size, 3 files retained
- **Aggregation**: Available through monitoring stack

### Metrics
When monitoring is enabled:
- **Application metrics**: Custom business metrics
- **Infrastructure metrics**: Resource usage, performance
- **Distributed tracing**: Request flow across services

## 🛠️ Development Commands

### Testing
```bash
# Run all tests (local resource)
# tilt trigger run-tests

# Run linting (local resource)
tilt trigger lint

# Build shared libraries (local resource)
tilt trigger build-shared
```

### Service Management
```bash
# Enable specific services
tilt enable salon treatment

# Disable services
tilt disable staff identity

# Restart a service
tilt restart salon-management-backend
```

## 🔍 Troubleshooting

### Common Issues

1. **Port Conflicts**
   - Check if ports 80, 8080, 5432, 6379 are available
   - Use `docker ps` to see running containers

2. **Service Dependencies**
   - Ensure databases start before applications
   - Check resource dependency order in Tilt UI

3. **Live Update Issues**
   - Verify file sync paths in Tiltfile
   - Check container file permissions

4. **Network Issues**
   - Ensure Docker networks are created
   - Check Traefik routing configuration

### Debug Commands
```bash
# Check service logs
tilt logs salon-management-backend

# View resource status
tilt get all

# Debug specific service
tilt describe salon-management-backend
```

## 📁 File Structure

```
.
├── Tiltfile                    # Main Tilt configuration
├── tilt_config.json           # Service configuration
├── TILT_README.md             # This documentation
└── services/
    ├── appointment/
    │   ├── docker-compose.*.yml
    │   └── */Dockerfile
    ├── salon/
    │   └── docker-compose.app.yml
    ├── treatment/
    │   └── docker-compose.app.yml
    └── orchestration/
        └── docker-compose.api-gateway.yml
```

## 🚀 Advanced Usage

### Custom Service Groups
Define your own service combinations by modifying the Tiltfile or using environment variables.

### Multi-Repository Support
The Tiltfile supports loading services from different repositories using the `include()` function.

### CI/CD Integration
Use `tilt ci` for automated testing in CI pipelines.

For more advanced usage, see the [official Tilt documentation](https://docs.tilt.dev/).
