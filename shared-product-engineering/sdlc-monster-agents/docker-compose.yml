version: '3.8'

services:
  n8n:
    image: n8nio/n8n:latest
    container_name: sdlc-monster-n8n
    restart: unless-stopped
    ports:
      - "5678:5678"
    environment:
      # n8n Configuration
      - N8N_HOST=0.0.0.0
      - N8N_PORT=5678
      - N8N_PROTOCOL=http
      - NODE_ENV=production
      
      # Enable LangChain and built-in modules
      - N8N_ENABLE_BUILTIN_MODULES=true
      - N8N_ALLOW_UNSANDBOXED_CODE=true
      - NODE_FUNCTION_ALLOW_BUILTIN=*
      
      # LangSmith Configuration (optional)
      - LANGCHAIN_ENDPOINT=https://api.smith.langchain.com
      - LANGCHAIN_TRACING_V2=true
      - LANGCHAIN_API_KEY=${LANGSMITH_API_KEY:-}
      
      # Gemini AI Configuration
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      
      # Database Configuration (SQLite for simplicity)
      - DB_TYPE=sqlite
      - DB_SQLITE_DATABASE=/home/<USER>/.n8n/database.sqlite
      
      # Security
      - N8N_BASIC_AUTH_ACTIVE=false
      - N8N_USER_MANAGEMENT_DISABLED=true
      
      # Logging
      - N8N_LOG_LEVEL=info
      - N8N_LOG_OUTPUT=console
      
    volumes:
      # n8n data persistence
      - n8n_data:/home/<USER>/.n8n
      
      # Mount the entire Beauty CRM project for file system access
      - /private/var/www/2025/ollamar1/beauty-crm:/workspace:rw
      
      # Mount workflows directory
      - ./workflows:/home/<USER>/.n8n/workflows:ro
      
      # Mount logs directory
      - ./logs:/workspace/logs:rw
      
    networks:
      - sdlc_network
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5678/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  n8n_data:
    driver: local

networks:
  sdlc_network:
    driver: bridge
