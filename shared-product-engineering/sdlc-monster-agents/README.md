# SDLC Monster Agents - Autonomous Code Modification POC

## Overview

This is a proof of concept demonstrating autonomous code modification using n8n workflows, LangChain, and Gemini AI. The system can read code files, analyze them with AI, make improvements, and write the changes back to the filesystem.

## Architecture

```mermaid
graph LR
    A[Manual Trigger] --> B[File Reader]
    B --> C[Gemini AI Processor]
    C --> D[File Writer]
    
    B --> E[Local Filesystem]
    D --> E
    
    C --> F[Gemini AI API]
    
    G[Logs] --> H[modification-log.txt]
    B --> G
    C --> G
    D --> G
```

## Components

### 1. n8n Workflow Nodes
- **Manual Trigger**: Starts the autonomous code modification process
- **File Reader**: Reads the target JavaScript file from the filesystem
- **Gemini AI Processor**: Uses Gemini AI to analyze and improve the code
- **File Writer**: Writes the improved code back to the filesystem

### 2. LangChain Integration
- Uses `@langchain/google-genai` for Gemini AI integration
- Implements proper error handling and logging
- Supports autonomous decision-making through AI prompts

### 3. File System Operations
- Reads from: `/test-files/sample-code.js`
- Creates backups before modification
- Logs all operations with timestamps
- Verifies write operations for reliability

## Prerequisites

### Required Environment Variables
```bash
export GEMINI_API_KEY="your_gemini_api_key_here"
```

### Optional Environment Variables
```bash
export LANGSMITH_API_KEY="your_langsmith_api_key_here"  # For tracing
```

### System Requirements
- Docker and Docker Compose
- Access to Google Gemini AI API
- Node.js environment (handled by Docker)

## Quick Start

1. **Set up environment variables**:
   ```bash
   export GEMINI_API_KEY="your_gemini_api_key_here"
   ```

2. **Run the startup script**:
   ```bash
   ./start-poc.sh
   ```

3. **Access n8n interface**:
   - Open http://localhost:5678 in your browser
   - Import the workflow from `workflows/01-autonomous-code-modifier-poc.json`

4. **Execute the workflow**:
   - Click the "Execute Workflow" button
   - Monitor the execution in real-time
   - Check the logs for detailed operation tracking

## File Structure

```
sdlc-monster-agents/
├── workflows/
│   └── 01-autonomous-code-modifier-poc.json
├── test-files/
│   └── sample-code.js
├── logs/
│   └── modification-log.txt (created during execution)
├── data/
│   └── (n8n persistent data)
├── docker-compose.yml
├── start-poc.sh
└── README.md
```

## How It Works

### Step 1: File Reading
The workflow reads the target JavaScript file and performs initial validation:
- Checks if file exists
- Reads file content
- Logs file size and read operation
- Passes content to AI processor

### Step 2: AI Processing
Gemini AI analyzes the code and makes improvements:
- Receives the original code content
- Uses a structured prompt for code improvement
- Adds timestamp comments and quality improvements
- Returns only the improved code (no explanations)

### Step 3: File Writing
The improved code is written back to the filesystem:
- Creates a backup of the original file
- Writes the improved code
- Verifies the write operation
- Logs success/failure status

### Step 4: Logging
All operations are logged with timestamps:
- File read operations
- AI processing status
- File write operations
- Error handling and recovery

## Success Criteria

✅ **File System Access**: Successfully reads and writes files on local filesystem  
✅ **AI Integration**: Gemini AI processes and improves code autonomously  
✅ **Real Modifications**: Changes are visible in git status and filesystem  
✅ **Error Handling**: Comprehensive error handling with rollback capabilities  
✅ **Logging**: Complete operation logging with timestamps  
✅ **Backup Creation**: Automatic backup before modifications  

## Monitoring and Debugging

### View n8n Logs
```bash
docker-compose logs -f n8n
```

### View Operation Logs
```bash
tail -f logs/modification-log.txt
```

### Check Git Status
```bash
git status  # Should show modified test-files/sample-code.js
```

### Verify File Changes
```bash
git diff test-files/sample-code.js
```

## Troubleshooting

### Common Issues

1. **Gemini API Key Not Set**
   - Ensure `GEMINI_API_KEY` environment variable is set
   - Verify the API key is valid and has proper permissions

2. **n8n Not Starting**
   - Check Docker is running: `docker ps`
   - View logs: `docker-compose logs n8n`
   - Ensure port 5678 is not in use

3. **File Permission Issues**
   - Ensure the Docker container has write access to the workspace
   - Check file permissions on the target directory

4. **Workflow Import Issues**
   - Verify the JSON workflow file is valid
   - Check n8n version compatibility
   - Ensure LangChain nodes are available

## Next Steps

This POC demonstrates the core concept of autonomous code modification. Future enhancements could include:

- **Multi-file Processing**: Handle multiple files in a single workflow
- **Git Integration**: Automatic commit and push of changes
- **Code Quality Gates**: Automated testing before applying changes
- **Rollback Mechanisms**: Automatic rollback on test failures
- **Scheduled Operations**: Time-based autonomous code maintenance
- **Advanced AI Prompts**: More sophisticated code analysis and improvement

## Security Considerations

- The workflow has full filesystem access within the mounted workspace
- Gemini AI processes your code content (ensure compliance with data policies)
- Backups are created before any modifications
- All operations are logged for audit purposes

---

**🤖 This POC demonstrates that autonomous code modification is possible and can be safely implemented with proper safeguards and monitoring.**
