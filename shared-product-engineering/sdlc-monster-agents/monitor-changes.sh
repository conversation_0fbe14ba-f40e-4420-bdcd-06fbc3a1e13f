#!/bin/bash

# Real-time monitoring script for autonomous code modification
echo "🔍 Monitoring autonomous code modification in real-time..."
echo "========================================================="

TARGET_FILE="test-files/sample-code.js"
LOG_FILE="logs/modification-log.txt"

echo "📁 Monitoring files:"
echo "  - Target: $TARGET_FILE"
echo "  - Logs: $LOG_FILE"
echo ""

# Show initial state
echo "📋 INITIAL STATE:"
echo "File size: $(wc -c < $TARGET_FILE) bytes"
echo "File lines: $(wc -l < $TARGET_FILE) lines"
echo ""

# Monitor for changes
echo "👀 Watching for changes... (Press Ctrl+C to stop)"
echo ""

while true; do
    # Check if log file has new content
    if [ -f "$LOG_FILE" ]; then
        LAST_LOG=$(tail -1 "$LOG_FILE" 2>/dev/null)
        if [[ "$LAST_LOG" == *"COMPLETED SUCCESSFULLY"* ]]; then
            echo "🎉 AUTONOMOUS MODIFICATION DETECTED!"
            echo "=================================="
            echo ""
            echo "📊 RESULTS:"
            echo "New file size: $(wc -c < $TARGET_FILE) bytes"
            echo "New file lines: $(wc -l < $TARGET_FILE) lines"
            echo ""
            echo "📁 Generated files:"
            ls -la test-files/*.backup.* 2>/dev/null || echo "No backup files found"
            echo ""
            echo "🔍 Git status:"
            git status --porcelain $TARGET_FILE
            echo ""
            echo "✅ Autonomous code modification completed!"
            break
        fi
    fi
    
    sleep 1
done
