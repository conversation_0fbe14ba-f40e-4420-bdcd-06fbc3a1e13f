{"name": "Autonomous Code Modifier POC", "nodes": [{"parameters": {}, "id": "trigger-node", "name": "Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"code": {"execute": {"code": "// Autonomous Code Modification POC using Gemini AI\nconst fs = require('fs');\nconst path = require('path');\n\n// Configuration\nconst TARGET_FILE = '/private/var/www/2025/ollamar1/beauty-crm/shared-product-engineering/sdlc-monster-agents/test-files/sample-code.js';\nconst LOG_FILE = '/private/var/www/2025/ollamar1/beauty-crm/shared-product-engineering/sdlc-monster-agents/logs/modification-log.txt';\n\n// Ensure logs directory exists\nconst logDir = path.dirname(LOG_FILE);\nif (!fs.existsSync(logDir)) {\n  fs.mkdirSync(logDir, { recursive: true });\n}\n\n// Log function\nfunction logOperation(message) {\n  const timestamp = new Date().toISOString();\n  const logEntry = `[${timestamp}] ${message}\\n`;\n  fs.appendFileSync(LOG_FILE, logEntry);\n  console.log(logEntry.trim());\n}\n\ntry {\n  // Read the target file\n  logOperation('Starting autonomous code modification process');\n  \n  if (!fs.existsSync(TARGET_FILE)) {\n    throw new Error(`Target file does not exist: ${TARGET_FILE}`);\n  }\n  \n  const originalContent = fs.readFileSync(TARGET_FILE, 'utf8');\n  logOperation(`Successfully read file: ${TARGET_FILE}`);\n  logOperation(`Original file size: ${originalContent.length} characters`);\n  \n  // Pass the content to the next node for AI processing\n  return [{\n    json: {\n      originalContent,\n      targetFile: TARGET_FILE,\n      logFile: LOG_FILE,\n      timestamp: new Date().toISOString(),\n      operation: 'file-read-success'\n    }\n  }];\n  \n} catch (error) {\n  logOperation(`ERROR: ${error.message}`);\n  return [{\n    json: {\n      error: error.message,\n      targetFile: TARGET_FILE,\n      logFile: LOG_FILE,\n      timestamp: new Date().toISOString(),\n      operation: 'file-read-error'\n    }\n  }];\n}"}}}, "id": "file-reader-node", "name": "File Reader", "type": "@n8n/n8n-nodes-langchain.code", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"modelName": "models/gemini-2.0-flash-exp"}, "id": "gemini-model-node", "name": "Gemini AI Model", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [680, 300]}, {"parameters": {"code": {"execute": {"code": "// AI-powered code modification using Gemini\nconst { ChatGoogleGenerativeAI } = require('@langchain/google-genai');\nconst fs = require('fs');\n\n// Get data from previous node\nconst inputData = $input.all()[0].json;\n\nif (inputData.error) {\n  return [inputData]; // Pass through error\n}\n\nconst { originalContent, targetFile, logFile } = inputData;\n\n// Log function\nfunction logOperation(message) {\n  const timestamp = new Date().toISOString();\n  const logEntry = `[${timestamp}] ${message}\\n`;\n  fs.appendFileSync(logFile, logEntry);\n  console.log(logEntry.trim());\n}\n\ntry {\n  // Initialize Gemini AI\n  const model = new ChatGoogleGenerativeAI({\n    modelName: 'gemini-2.0-flash-exp',\n    apiKey: process.env.GEMINI_API_KEY,\n    temperature: 0.1\n  });\n  \n  logOperation('Initialized Gemini AI model');\n  \n  // Create prompt for code improvement\n  const prompt = `You are an autonomous code improvement agent. Please analyze the following JavaScript code and make a simple improvement by adding a timestamp comment at the top and improving any basic code quality issues you find.\n\nOriginal code:\n\\`\\`\\`javascript\n${originalContent}\n\\`\\`\\`\n\nPlease return ONLY the improved JavaScript code without any explanation or markdown formatting. Add a comment at the top with the current timestamp and a brief description of what you improved.`;\n  \n  logOperation('Sending code to Gemini AI for analysis and improvement');\n  \n  // Get AI response\n  const response = await model.invoke(prompt);\n  const improvedCode = response.content;\n  \n  logOperation('Received improved code from Gemini AI');\n  logOperation(`Improved code length: ${improvedCode.length} characters`);\n  \n  return [{\n    json: {\n      originalContent,\n      improvedCode,\n      targetFile,\n      logFile,\n      timestamp: new Date().toISOString(),\n      operation: 'ai-processing-success',\n      aiModel: 'gemini-2.0-flash-exp'\n    }\n  }];\n  \n} catch (error) {\n  logOperation(`AI Processing ERROR: ${error.message}`);\n  return [{\n    json: {\n      error: error.message,\n      originalContent,\n      targetFile,\n      logFile,\n      timestamp: new Date().toISOString(),\n      operation: 'ai-processing-error'\n    }\n  }];\n}"}}}, "id": "ai-processor-node", "name": "AI Code Processor", "type": "@n8n/n8n-nodes-langchain.code", "typeVersion": 1, "position": [900, 300]}, {"parameters": {"code": {"execute": {"code": "// Write improved code back to filesystem\nconst fs = require('fs');\nconst path = require('path');\n\n// Get data from previous node\nconst inputData = $input.all()[0].json;\n\nif (inputData.error) {\n  return [inputData]; // Pass through error\n}\n\nconst { originalContent, improvedCode, targetFile, logFile } = inputData;\n\n// Log function\nfunction logOperation(message) {\n  const timestamp = new Date().toISOString();\n  const logEntry = `[${timestamp}] ${message}\\n`;\n  fs.appendFileSync(logFile, logEntry);\n  console.log(logEntry.trim());\n}\n\ntry {\n  // Create backup of original file\n  const backupFile = targetFile + '.backup.' + Date.now();\n  fs.copyFileSync(targetFile, backupFile);\n  logOperation(`Created backup: ${backupFile}`);\n  \n  // Write improved code to target file\n  fs.writeFileSync(targetFile, improvedCode, 'utf8');\n  logOperation(`Successfully wrote improved code to: ${targetFile}`);\n  \n  // Verify the write operation\n  const verifyContent = fs.readFileSync(targetFile, 'utf8');\n  const writeSuccess = verifyContent === improvedCode;\n  \n  if (writeSuccess) {\n    logOperation('File write verification: SUCCESS');\n    logOperation('Autonomous code modification completed successfully');\n  } else {\n    logOperation('File write verification: FAILED');\n  }\n  \n  return [{\n    json: {\n      originalContent,\n      improvedCode,\n      targetFile,\n      backupFile,\n      logFile,\n      writeSuccess,\n      timestamp: new Date().toISOString(),\n      operation: 'file-write-complete',\n      summary: {\n        originalSize: originalContent.length,\n        improvedSize: improvedCode.length,\n        backupCreated: true,\n        fileModified: writeSuccess\n      }\n    }\n  }];\n  \n} catch (error) {\n  logOperation(`File Write ERROR: ${error.message}`);\n  return [{\n    json: {\n      error: error.message,\n      originalContent,\n      improvedCode,\n      targetFile,\n      logFile,\n      timestamp: new Date().toISOString(),\n      operation: 'file-write-error'\n    }\n  }];\n}"}}}, "id": "file-writer-node", "name": "File Writer", "type": "@n8n/n8n-nodes-langchain.code", "typeVersion": 1, "position": [1120, 300]}], "connections": {"Manual Trigger": {"main": [[{"node": "File Reader", "type": "main", "index": 0}]]}, "File Reader": {"main": [[{"node": "AI Code Processor", "type": "main", "index": 0}]]}, "AI Code Processor": {"main": [[{"node": "File Writer", "type": "main", "index": 0}]]}}, "active": false, "settings": {}, "createdAt": "2025-01-02T00:00:00.000Z", "updatedAt": "2025-01-02T00:00:00.000Z", "id": "autonomous-code-modifier-poc"}