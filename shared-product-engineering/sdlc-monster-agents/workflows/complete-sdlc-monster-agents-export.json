{
  "name": "Complete SDLC Monster Agents - Full Implementation",
  "active": true,
  "nodes": [
    {
      "parameters": {
        "httpMethod": "POST",
        "path": "sdlc-monster-complete",
        "responseMode": "responseNode",
        "options": {}
      },
      "id": "webhook-trigger",
      "name": "Webhook Trigger",
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 2,
      "position": [240, 300],
      "webhookId": "sdlc-monster-complete"
    },
    {
      "parameters": {
        "code": {
          "execute": {
            "code": "// SDLC Monster Agents - File System Monitor & Reader\n// Complete autonomous code modification system\nconst fs = require('fs');\nconst path = require('path');\nconst { execSync } = require('child_process');\n\n// Configuration\nconst WORKSPACE_ROOT = '/workspace';\nconst AGENTS_DIR = '/workspace/shared-product-engineering/sdlc-monster-agents';\nconst TARGET_FILE = `${AGENTS_DIR}/test-files/sample-code.js`;\nconst LOG_FILE = `${AGENTS_DIR}/logs/sdlc-monster-complete-log.txt`;\nconst DATA_DIR = `${AGENTS_DIR}/data`;\n\n// Ensure all directories exist\n[path.dirname(LOG_FILE), DATA_DIR, `${AGENTS_DIR}/backups`].forEach(dir => {\n  if (!fs.existsSync(dir)) {\n    fs.mkdirSync(dir, { recursive: true });\n  }\n});\n\n// Enhanced logging function with levels\nfunction logOperation(message, level = 'INFO') {\n  const timestamp = new Date().toISOString();\n  const logEntry = `[${timestamp}] [${level}] ${message}\\n`;\n  fs.appendFileSync(LOG_FILE, logEntry);\n  console.log(logEntry.trim());\n}\n\n// System health check\nfunction performHealthCheck() {\n  const checks = {\n    workspace: fs.existsSync(WORKSPACE_ROOT),\n    agentsDir: fs.existsSync(AGENTS_DIR),\n    targetFile: fs.existsSync(TARGET_FILE),\n    geminiApiKey: !!process.env.GEMINI_API_KEY,\n    gitRepo: false\n  };\n  \n  try {\n    execSync('git status', { cwd: WORKSPACE_ROOT, stdio: 'pipe' });\n    checks.gitRepo = true;\n  } catch (e) {\n    logOperation('Git repository not detected', 'WARN');\n  }\n  \n  return checks;\n}\n\ntry {\n  logOperation('=== SDLC MONSTER AGENTS - COMPLETE AUTONOMOUS SYSTEM STARTING ===', 'SYSTEM');\n  logOperation('Initializing file system monitor and health checks...', 'SYSTEM');\n  \n  // Perform comprehensive health check\n  const healthCheck = performHealthCheck();\n  logOperation(`System Health Check: ${JSON.stringify(healthCheck)}`, 'SYSTEM');\n  \n  if (!healthCheck.targetFile) {\n    throw new Error(`Target file does not exist: ${TARGET_FILE}`);\n  }\n  \n  if (!healthCheck.geminiApiKey) {\n    throw new Error('GEMINI_API_KEY environment variable not found');\n  }\n  \n  // Read and analyze target file\n  const originalContent = fs.readFileSync(TARGET_FILE, 'utf8');\n  const fileStats = fs.statSync(TARGET_FILE);\n  \n  logOperation(`File monitoring complete: ${TARGET_FILE}`, 'MONITOR');\n  logOperation(`File size: ${originalContent.length} characters`, 'MONITOR');\n  logOperation(`File lines: ${originalContent.split('\\n').length}`, 'MONITOR');\n  logOperation(`Last modified: ${fileStats.mtime.toISOString()}`, 'MONITOR');\n  \n  // Git status check\n  let gitStatus = 'unknown';\n  try {\n    gitStatus = execSync('git status --porcelain', { \n      cwd: path.dirname(TARGET_FILE), \n      encoding: 'utf8' \n    }).trim();\n    logOperation(`Git status: ${gitStatus || 'clean'}`, 'GIT');\n  } catch (e) {\n    logOperation(`Git status check failed: ${e.message}`, 'WARN');\n  }\n  \n  // Prepare data for AI processing\n  const analysisData = {\n    originalContent,\n    targetFile: TARGET_FILE,\n    logFile: LOG_FILE,\n    fileStats: {\n      size: originalContent.length,\n      lines: originalContent.split('\\n').length,\n      lastModified: fileStats.mtime.toISOString()\n    },\n    systemHealth: healthCheck,\n    gitStatus,\n    timestamp: new Date().toISOString(),\n    operation: 'file-analysis-complete'\n  };\n  \n  logOperation('File system monitoring completed successfully', 'SUCCESS');\n  logOperation('Preparing data for AI analysis...', 'SYSTEM');\n  \n  return [{ json: analysisData }];\n  \n} catch (error) {\n  logOperation(`CRITICAL ERROR in file monitor: ${error.message}`, 'ERROR');\n  return [{\n    json: {\n      error: error.message,\n      targetFile: TARGET_FILE,\n      logFile: LOG_FILE,\n      timestamp: new Date().toISOString(),\n      operation: 'file-monitor-error'\n    }\n  }];\n}"
          }
        }
      },
      "id": "file-monitor",
      "name": "File System Monitor",
      "type": "@n8n/n8n-nodes-langchain.code",
      "typeVersion": 1,
      "position": [460, 300]
    },
    {
      "parameters": {
        "code": {
          "execute": {
            "code": "// SDLC Monster Agents - AI Decision Engine with Gemma\n// LangChain integration with comprehensive AI analysis\nconst { ChatGoogleGenerativeAI } = require('@langchain/google-genai');\nconst fs = require('fs');\n\n// Get data from previous node\nconst inputData = $input.all()[0].json;\n\nif (inputData.error) {\n  return [inputData]; // Pass through error\n}\n\nconst { originalContent, targetFile, logFile, fileStats, systemHealth } = inputData;\n\n// Enhanced logging function\nfunction logOperation(message, level = 'INFO') {\n  const timestamp = new Date().toISOString();\n  const logEntry = `[${timestamp}] [${level}] ${message}\\n`;\n  fs.appendFileSync(logFile, logEntry);\n  console.log(logEntry.trim());\n}\n\ntry {\n  logOperation('=== AI DECISION ENGINE - GEMMA ANALYSIS STARTING ===', 'AI');\n  \n  // Initialize Gemma AI using LangChain\n  logOperation('Initializing LangChain with Gemma AI model: gemma-3n-e4b-it', 'AI');\n  \n  const model = new ChatGoogleGenerativeAI({\n    modelName: 'gemma-3n-e4b-it',\n    apiKey: process.env.GEMINI_API_KEY,\n    temperature: 0.1,\n    maxOutputTokens: 4096,\n    topP: 0.8,\n    topK: 40\n  });\n  \n  logOperation('Gemma AI model initialized successfully', 'AI');\n  logOperation(`Processing file with ${fileStats.lines} lines, ${fileStats.size} characters`, 'AI');\n  \n  // Comprehensive AI prompt for autonomous code improvement\n  const prompt = `You are an autonomous SDLC agent powered by Gemma AI. You are analyzing and improving code as part of a self-healing development system.\n\nSYSTEM CONTEXT:\n- File: ${targetFile}\n- Size: ${fileStats.size} characters, ${fileStats.lines} lines\n- Last modified: ${fileStats.lastModified}\n- System health: ${JSON.stringify(systemHealth)}\n\nCODE TO ANALYZE AND IMPROVE:\n\\`\\`\\`javascript\n${originalContent}\n\\`\\`\\`\n\nAs an autonomous SDLC agent, please make comprehensive improvements:\n\n1. **DOCUMENTATION**: Add complete JSDoc comments for all functions with @param, @returns, @throws\n2. **ERROR HANDLING**: Add robust error handling, input validation, and meaningful error messages\n3. **CODE QUALITY**: Improve variable names, use modern JavaScript (const/let, template literals, arrow functions where appropriate)\n4. **PERFORMANCE**: Optimize any inefficient code patterns\n5. **SECURITY**: Add input sanitization and validation where needed\n6. **MAINTAINABILITY**: Add meaningful comments explaining complex logic\n7. **TESTING**: Add inline examples or test cases in comments\n8. **STANDARDS**: Follow JavaScript best practices and coding standards\n9. **AUTONOMOUS HEADER**: Add a header comment showing:\n   - Timestamp of modification\n   - AI agent details (Gemma AI via LangChain)\n   - Summary of improvements made\n   - Version tracking\n\nIMPORTANT: Return ONLY the improved JavaScript code without any explanation, markdown formatting, or code blocks. The code should be production-ready, well-documented, and significantly improved from the original.\n\nThe improved code should be autonomous-agent-grade quality suitable for a self-healing SDLC system.`;\n  \n  logOperation('Sending comprehensive analysis request to Gemma AI...', 'AI');\n  logOperation(`Prompt length: ${prompt.length} characters`, 'AI');\n  \n  // Get AI response using LangChain\n  const startTime = Date.now();\n  const response = await model.invoke(prompt);\n  const processingTime = Date.now() - startTime;\n  \n  const improvedCode = response.content;\n  \n  if (!improvedCode) {\n    throw new Error('No improved code received from Gemma AI');\n  }\n  \n  logOperation(`Gemma AI analysis completed in ${processingTime}ms`, 'AI');\n  logOperation(`AI response length: ${improvedCode.length} characters`, 'AI');\n  logOperation(`Code expansion: ${((improvedCode.length / originalContent.length - 1) * 100).toFixed(1)}%`, 'AI');\n  \n  // AI decision analysis\n  const aiAnalysis = {\n    originalSize: originalContent.length,\n    improvedSize: improvedCode.length,\n    expansionRatio: improvedCode.length / originalContent.length,\n    processingTimeMs: processingTime,\n    modelUsed: 'gemma-3n-e4b-it',\n    framework: 'LangChain',\n    improvements: [\n      'JSDoc documentation',\n      'Error handling',\n      'Code quality',\n      'Modern JavaScript',\n      'Security enhancements',\n      'Performance optimizations'\n    ]\n  };\n  \n  logOperation(`AI Analysis: ${JSON.stringify(aiAnalysis)}`, 'AI');\n  logOperation('=== AI DECISION ENGINE COMPLETED SUCCESSFULLY ===', 'AI');\n  \n  return [{\n    json: {\n      originalContent,\n      improvedCode,\n      targetFile,\n      logFile,\n      aiAnalysis,\n      timestamp: new Date().toISOString(),\n      operation: 'ai-analysis-complete'\n    }\n  }];\n  \n} catch (error) {\n  logOperation(`AI PROCESSING ERROR: ${error.message}`, 'ERROR');\n  logOperation(`Error stack: ${error.stack}`, 'ERROR');\n  \n  return [{\n    json: {\n      error: error.message,\n      originalContent,\n      targetFile,\n      logFile,\n      timestamp: new Date().toISOString(),\n      operation: 'ai-analysis-error'\n    }\n  }];\n}"
          }
        }
      },
      "id": "ai-decision-engine",
      "name": "Gemma AI Decision Engine",
      "type": "@n8n/n8n-nodes-langchain.code",
      "typeVersion": 1,
      "position": [680, 300]
    }
