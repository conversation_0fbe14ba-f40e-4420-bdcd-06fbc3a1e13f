// Sample JavaScript file for autonomous code modification testing
// This file will be modified by the SDLC Monster Agents

function calculateSum(a, b) {
    return a + b;
}

function greetUser(name) {
    console.log("Hello, " + name + "!");
}

const numbers = [1, 2, 3, 4, 5];
const sum = numbers.reduce((acc, num) => acc + num, 0);

console.log("Sum of numbers:", sum);

module.exports = {
    calculateSum,
    greetUser
};
