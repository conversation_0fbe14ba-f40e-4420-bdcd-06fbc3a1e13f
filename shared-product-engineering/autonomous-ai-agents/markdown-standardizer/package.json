{"name": "beauty-crm-markdown-standardizer", "version": "1.0.0", "description": "Standardizes all markdown files using ReadmeRoadmap.md template", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "standardize": "node standardize-all.js"}, "dependencies": {"express": "^4.18.2", "axios": "^1.6.2", "fs-extra": "^11.2.0", "glob": "^10.3.10", "marked": "^11.1.1", "gray-matter": "^4.0.3", "simple-git": "^3.20.0", "helmet": "^7.1.0", "cors": "^2.8.5", "morgan": "^1.10.0", "dotenv": "^16.3.1", "uuid": "^9.0.1", "path": "^0.12.7"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["markdown", "standardization", "template", "automation"], "author": "Beauty CRM Team", "license": "MIT"}