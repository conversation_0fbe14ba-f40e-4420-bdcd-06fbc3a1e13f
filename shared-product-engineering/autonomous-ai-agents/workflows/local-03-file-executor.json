{"name": "Local 03 - File Executor Agent", "nodes": [{"parameters": {}, "id": "webhook-trigger", "name": "Execute Local Task Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [100, 300], "webhookId": "execute-local-task"}, {"parameters": {"mode": "runOnceForEachItem", "code": "// Local File Executor - Makes REAL changes to local files\nconst fs = require('fs');\nconst path = require('path');\nconst { execSync } = require('child_process');\n\nconst taskData = $json.task;\nconst analysis = taskData.analysis;\n\nconsole.log(`🚀 Local File Executor - Processing task: ${taskData.id}`);\nconsole.log(`📁 File: ${taskData.file}`);\nconsole.log(`🎯 Type: ${taskData.type}`);\nconsole.log(`⚡ Feasibility: ${(analysis.feasibilityScore * 100).toFixed(1)}%`);\nconsole.log(`🔧 Can Automate: ${analysis.canAutomate}`);\n\nconst workspaceRoot = '/workspace';\nconst filePath = path.join(workspaceRoot, taskData.file);\n\nlet executionResult = {\n  taskId: taskData.id,\n  status: 'in-progress',\n  startedAt: new Date().toISOString(),\n  changes: [],\n  errors: [],\n  gitChanges: [],\n  backupFiles: [],\n  agent: 'local-file-executor'\n};\n\ntry {\n  // Verify file exists\n  if (!fs.existsSync(filePath)) {\n    throw new Error(`File not found: ${taskData.file}`);\n  }\n  \n  // Read current file content\n  const originalContent = fs.readFileSync(filePath, 'utf8');\n  const lines = originalContent.split('\\n');\n  \n  console.log(`📖 Read file: ${lines.length} lines, ${originalContent.length} characters`);\n  \n  // Create backup before making changes\n  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');\n  const backupPath = `${filePath}.backup.${timestamp}`;\n  fs.writeFileSync(backupPath, originalContent, 'utf8');\n  executionResult.backupFiles.push(backupPath);\n  console.log(`💾 Backup created: ${path.basename(backupPath)}`);\n  \n  // Execute based on task type and analysis\n  let newContent = originalContent;\n  let changesMade = [];\n  \n  const targetLineIndex = taskData.line - 1;\n  if (targetLineIndex >= 0 && targetLineIndex < lines.length) {\n    const originalLine = lines[targetLineIndex];\n    \n    switch (taskData.type) {\n      case 'todo':\n        // Mark TODO as reviewed and add timestamp\n        const reviewedLine = `${originalLine} // ✅ AI-reviewed: ${new Date().toISOString().split('T')[0]} - Feasibility: ${(analysis.feasibilityScore * 100).toFixed(0)}%`;\n        lines[targetLineIndex] = reviewedLine;\n        changesMade.push(`Marked TODO as AI-reviewed on line ${taskData.line}`);\n        break;\n        \n      case 'fixme':\n        // Add FIXME analysis and priority\n        const fixmeNote = `${originalLine} // 🔧 FIXME analyzed: ${analysis.reasoning} - Priority: ${taskData.priority}`;\n        lines[targetLineIndex] = fixmeNote;\n        changesMade.push(`Added FIXME analysis on line ${taskData.line}`);\n        break;\n        \n      case 'bug':\n        // Add bug tracking information\n        const bugTracker = `${originalLine} // 🐛 BUG tracked: Risk=${analysis.riskLevel}, Est=${analysis.estimatedMinutes}min - ${new Date().toISOString().split('T')[0]}`;\n        lines[targetLineIndex] = bugTracker;\n        changesMade.push(`Added bug tracking info on line ${taskData.line}`);\n        break;\n        \n      case 'deprecated':\n        // Add deprecation warning with timeline\n        const deprecationWarning = `${originalLine} // ⚠️ DEPRECATED: Scheduled for removal - Risk: ${analysis.riskLevel} - ${new Date().toISOString().split('T')[0]}`;\n        lines[targetLineIndex] = deprecationWarning;\n        changesMade.push(`Added deprecation warning on line ${taskData.line}`);\n        break;\n        \n      case 'security':\n        // Add security review flag with high priority\n        const securityFlag = `${originalLine} // 🔒 SECURITY CRITICAL: Requires immediate review - Flagged ${new Date().toISOString().split('T')[0]}`;\n        lines[targetLineIndex] = securityFlag;\n        changesMade.push(`Added security review flag on line ${taskData.line}`);\n        break;\n        \n      case 'optimize':\n        // Add optimization analysis\n        const optimizeNote = `${originalLine} // 🚀 OPTIMIZE: ${analysis.reasoning} - Est impact: ${analysis.estimatedMinutes}min`;\n        lines[targetLineIndex] = optimizeNote;\n        changesMade.push(`Added optimization analysis on line ${taskData.line}`);\n        break;\n        \n      case 'refactor':\n        // Add refactoring plan\n        const refactorPlan = `${originalLine} // 🔄 REFACTOR planned: Complexity=${taskData.complexity}/5, Risk=${analysis.riskLevel}`;\n        lines[targetLineIndex] = refactorPlan;\n        changesMade.push(`Added refactoring plan on line ${taskData.line}`);\n        break;\n        \n      default:\n        // Generic task processing\n        const genericNote = `${originalLine} // 📝 AI-processed: ${analysis.recommendation} - ${new Date().toISOString().split('T')[0]}`;\n        lines[targetLineIndex] = genericNote;\n        changesMade.push(`Added AI processing note on line ${taskData.line}`);\n    }\n    \n    newContent = lines.join('\\n');\n  } else {\n    throw new Error(`Invalid line number: ${taskData.line} (file has ${lines.length} lines)`);\n  }\n  \n  // Write changes to file\n  if (newContent !== originalContent) {\n    fs.writeFileSync(filePath, newContent, 'utf8');\n    console.log(`✅ File updated: ${taskData.file}`);\n    \n    executionResult.changes = changesMade;\n    executionResult.status = 'completed';\n    \n    // Check git status to confirm real changes\n    try {\n      const gitStatus = execSync('git status --porcelain', { \n        cwd: workspaceRoot, \n        encoding: 'utf8' \n      });\n      \n      if (gitStatus.trim()) {\n        executionResult.gitChanges = gitStatus.trim().split('\\n');\n        console.log(`📝 Git changes detected: ${executionResult.gitChanges.length} files`);\n        executionResult.gitChanges.forEach(change => {\n          console.log(`   ${change}`);\n        });\n      }\n    } catch (gitError) {\n      console.log(`⚠️ Could not check git status: ${gitError.message}`);\n      executionResult.errors.push(`Git status check failed: ${gitError.message}`);\n    }\n    \n  } else {\n    executionResult.status = 'no-changes';\n    executionResult.changes = ['No changes were necessary'];\n  }\n  \n} catch (error) {\n  console.error(`❌ Execution error: ${error.message}`);\n  executionResult.status = 'failed';\n  executionResult.errors.push(error.message);\n}\n\nexecutionResult.completedAt = new Date().toISOString();\nexecutionResult.duration = new Date() - new Date(executionResult.startedAt);\n\nconsole.log(`🏁 Local execution ${executionResult.status}:`);\nconsole.log(`   Changes: ${executionResult.changes.length}`);\nconsole.log(`   Git changes: ${executionResult.gitChanges.length}`);\nconsole.log(`   Duration: ${executionResult.duration}ms`);\n\nreturn executionResult;"}, "id": "execute-file-changes", "name": "Execute Local File Changes", "type": "n8n-nodes-langchain.code", "typeVersion": 1, "position": [300, 300]}, {"parameters": {"mode": "runOnceForEachItem", "code": "// Local Quality Gate - Run TypeScript and ESLint checks\nconst { execSync } = require('child_process');\nconst executionResult = $json;\n\nconsole.log(`🔍 Local Quality Gate - Checking task: ${executionResult.taskId}`);\n\nlet qualityResult = {\n  taskId: executionResult.taskId,\n  executionResult: executionResult,\n  qualityChecks: [],\n  passed: true,\n  checkedAt: new Date().toISOString(),\n  agent: 'local-quality-gate'\n};\n\nif (executionResult.status === 'completed' && executionResult.changes.length > 0) {\n  const workspaceRoot = '/workspace';\n  const taskData = $('Execute Local Task Webhook').item.json.task;\n  \n  // Run TypeScript check if it's a TS file\n  if (taskData.file.endsWith('.ts') || taskData.file.endsWith('.tsx')) {\n    try {\n      console.log('🔧 Running TypeScript check...');\n      const tsOutput = execSync('npx tsc --noEmit --skipLibCheck', { \n        cwd: workspaceRoot, \n        encoding: 'utf8',\n        timeout: 30000\n      });\n      qualityResult.qualityChecks.push({ \n        check: 'typescript', \n        status: 'passed',\n        output: tsOutput || 'No errors'\n      });\n      console.log('✅ TypeScript check passed');\n    } catch (tsError) {\n      const isWarning = tsError.message.includes('warning') || tsError.status === 0;\n      qualityResult.qualityChecks.push({ \n        check: 'typescript', \n        status: isWarning ? 'warning' : 'failed', \n        error: tsError.message,\n        output: tsError.stdout || ''\n      });\n      if (!isWarning) {\n        qualityResult.passed = false;\n      }\n      console.log(`${isWarning ? '⚠️' : '❌'} TypeScript check ${isWarning ? 'warnings' : 'failed'}`);\n    }\n  }\n  \n  // Run ESLint check\n  try {\n    console.log('🔧 Running ESLint check...');\n    const lintOutput = execSync(`npx eslint \"${taskData.file}\" --format=compact`, { \n      cwd: workspaceRoot, \n      encoding: 'utf8',\n      timeout: 20000\n    });\n    qualityResult.qualityChecks.push({ \n      check: 'eslint', \n      status: 'passed',\n      output: lintOutput || 'No issues'\n    });\n    console.log('✅ ESLint check passed');\n  } catch (lintError) {\n    const isWarningOnly = lintError.message.includes('warning') && !lintError.message.includes('error');\n    qualityResult.qualityChecks.push({ \n      check: 'eslint', \n      status: isWarningOnly ? 'warning' : 'failed',\n      error: lintError.message,\n      output: lintError.stdout || ''\n    });\n    if (!isWarningOnly) {\n      qualityResult.passed = false;\n    }\n    console.log(`${isWarningOnly ? '⚠️' : '❌'} ESLint check ${isWarningOnly ? 'warnings' : 'failed'}`);\n  }\n  \n  // File integrity check\n  const fs = require('fs');\n  const path = require('path');\n  const filePath = path.join(workspaceRoot, taskData.file);\n  \n  try {\n    const stats = fs.statSync(filePath);\n    const fileSizeMB = stats.size / (1024 * 1024);\n    \n    qualityResult.qualityChecks.push({ \n      check: 'file-integrity', \n      status: 'passed',\n      fileSize: fileSizeMB.toFixed(2) + 'MB',\n      lastModified: stats.mtime.toISOString()\n    });\n    \n    if (fileSizeMB > 1) {\n      console.log(`⚠️ Large file detected: ${fileSizeMB.toFixed(2)}MB`);\n    }\n  } catch (sizeError) {\n    qualityResult.qualityChecks.push({ \n      check: 'file-integrity', \n      status: 'failed',\n      error: sizeError.message \n    });\n    qualityResult.passed = false;\n  }\n  \n} else {\n  qualityResult.qualityChecks.push({ \n    check: 'execution', \n    status: executionResult.status === 'completed' ? 'passed' : 'failed',\n    message: executionResult.status === 'no-changes' ? 'No changes to validate' : 'Execution failed'\n  });\n  \n  if (executionResult.status === 'failed') {\n    qualityResult.passed = false;\n  }\n}\n\nconst passedChecks = qualityResult.qualityChecks.filter(c => c.status === 'passed').length;\nconst warningChecks = qualityResult.qualityChecks.filter(c => c.status === 'warning').length;\nconst totalChecks = qualityResult.qualityChecks.length;\n\nconsole.log(`🎯 Local Quality Gate Result:`);\nconsole.log(`   Passed: ${passedChecks}/${totalChecks}`);\nconsole.log(`   Warnings: ${warningChecks}`);\nconsole.log(`   Overall: ${qualityResult.passed ? 'PASSED' : 'FAILED'}`);\n\nreturn qualityResult;"}, "id": "quality-gate", "name": "Local Quality Gate", "type": "n8n-nodes-langchain.code", "typeVersion": 1, "position": [500, 300]}, {"parameters": {"mode": "runOnceForEachItem", "code": "// Local Git Operations - Create real git commits\nconst { execSync } = require('child_process');\nconst qualityResult = $json;\nconst executionResult = qualityResult.executionResult;\n\nconsole.log(`📝 Local Git Operations - Task: ${qualityResult.taskId}`);\n\nlet gitResult = {\n  taskId: qualityResult.taskId,\n  qualityResult: qualityResult,\n  gitOperations: [],\n  commitHash: null,\n  gitStatus: null,\n  success: false,\n  performedAt: new Date().toISOString(),\n  agent: 'local-git-operations'\n};\n\nif (qualityResult.passed && executionResult.status === 'completed' && executionResult.gitChanges.length > 0) {\n  const workspaceRoot = '/workspace';\n  const taskData = $('Execute Local Task Webhook').item.json.task;\n  \n  try {\n    // Get current git status\n    const initialStatus = execSync('git status --porcelain', { \n      cwd: workspaceRoot, \n      encoding: 'utf8' \n    });\n    gitResult.gitStatus = initialStatus.trim();\n    console.log(`📊 Initial git status: ${gitResult.gitStatus.split('\\n').length} changes`);\n    \n    // Add the modified file\n    const addCommand = `git add \"${taskData.file}\"`;\n    execSync(addCommand, { cwd: workspaceRoot });\n    gitResult.gitOperations.push(`Added file: ${taskData.file}`);\n    console.log(`✅ Added file to git: ${taskData.file}`);\n    \n    // Create commit message based on analysis\n    const analysis = taskData.analysis;\n    let commitMessage = analysis.gitCommitMessage || `AI Agent: ${taskData.type} in ${taskData.file}`;\n    \n    // Enhance commit message with details\n    commitMessage += `\\n\\n- Task: ${taskData.description}\\n- Type: ${taskData.type}\\n- Priority: ${taskData.priority}\\n- Feasibility: ${(analysis.feasibilityScore * 100).toFixed(0)}%\\n- Risk: ${analysis.riskLevel}\\n- Agent: local-file-executor`;\n    \n    // Commit the changes\n    const commitCommand = `git commit -m \"${commitMessage}\"`;\n    const commitOutput = execSync(commitCommand, { \n      cwd: workspaceRoot, \n      encoding: 'utf8' \n    });\n    \n    // Extract commit hash\n    const hashMatch = commitOutput.match(/\\[\\w+\\s([a-f0-9]+)\\]/);\n    if (hashMatch) {\n      gitResult.commitHash = hashMatch[1];\n    }\n    \n    gitResult.gitOperations.push(`Created commit: ${gitResult.commitHash}`);\n    gitResult.success = true;\n    \n    console.log(`✅ Git commit created: ${gitResult.commitHash}`);\n    console.log(`📝 Commit message: ${commitMessage.split('\\n')[0]}`);\n    \n    // Get final git status\n    const finalStatus = execSync('git status --porcelain', { \n      cwd: workspaceRoot, \n      encoding: 'utf8' \n    });\n    \n    console.log(`📊 Final git status: ${finalStatus.trim() ? finalStatus.trim().split('\\n').length + ' remaining changes' : 'Clean working directory'}`);\n    \n  } catch (gitError) {\n    console.error(`❌ Git operation failed: ${gitError.message}`);\n    gitResult.gitOperations.push(`Error: ${gitError.message}`);\n    gitResult.success = false;\n  }\n  \n} else {\n  const reason = !qualityResult.passed ? 'Quality checks failed' : \n                executionResult.status !== 'completed' ? 'Execution failed' :\n                'No git changes detected';\n  \n  gitResult.gitOperations.push(`Skipped git operations: ${reason}`);\n  console.log(`⏸️ Skipped git operations: ${reason}`);\n}\n\nconsole.log(`🏁 Git operations ${gitResult.success ? 'completed' : 'skipped/failed'}`);\n\nreturn gitResult;"}, "id": "git-operations", "name": "Local Git Operations", "type": "n8n-nodes-langchain.code", "typeVersion": 1, "position": [700, 300]}, {"parameters": {"url": "http://localhost:5678/webhook/local-task-completed", "sendBody": true, "specifyHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "bodyParameters": {"parameters": [{"name": "taskId", "value": "={{ $json.taskId }}"}, {"name": "gitResult", "value": "={{ $json }}"}, {"name": "commitHash", "value": "={{ $json.commitHash }}"}, {"name": "success", "value": "={{ $json.success }}"}, {"name": "completedAt", "value": "={{ $json.performedAt }}"}, {"name": "source", "value": "local-file-executor"}]}, "options": {"timeout": 10000}}, "id": "notify-completion", "name": "Notify Local Completion", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [900, 300]}], "connections": {"Execute Local Task Webhook": {"main": [[{"node": "Execute Local File Changes", "type": "main", "index": 0}]]}, "Execute Local File Changes": {"main": [[{"node": "Local Quality Gate", "type": "main", "index": 0}]]}, "Local Quality Gate": {"main": [[{"node": "Local Git Operations", "type": "main", "index": 0}]]}, "Local Git Operations": {"main": [[{"node": "Notify Local Completion", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "1", "meta": {"templateCredsSetupCompleted": true}, "id": "local-file-executor", "tags": [{"createdAt": "2025-01-08T00:00:00.000Z", "updatedAt": "2025-01-08T00:00:00.000Z", "id": "local-sdlc-agent", "name": "Local SDLC Agent"}]}