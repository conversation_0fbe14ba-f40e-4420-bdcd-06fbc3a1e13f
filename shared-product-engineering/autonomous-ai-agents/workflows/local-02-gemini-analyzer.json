{"name": "Local 02 - Gemini Task Analyzer", "nodes": [{"parameters": {}, "id": "webhook-trigger", "name": "Tasks Found Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [100, 300], "webhookId": "tasks-found"}, {"parameters": {"mode": "runOnceForEachItem", "code": "// Local Gemini Task Analyzer - Analyzes each task with Gemini AI\nconst task = $json.tasks ? $json.tasks[0] : $json; // Handle both single task and task array\n\nif (!task || !task.id) {\n  console.log('⚠️ No valid task found for analysis');\n  return { error: 'No valid task provided' };\n}\n\nconsole.log(`🧠 Analyzing task: ${task.type} in ${task.file}:${task.line}`);\n\n// Prepare detailed analysis prompt for Gemini\nconst analysisPrompt = `You are an expert software architect analyzing a task in the Beauty CRM codebase.\n\nTASK DETAILS:\n- ID: ${task.id}\n- Type: ${task.type}\n- Priority: ${task.priority}\n- File: ${task.file}\n- Line: ${task.line}\n- Content: ${task.content}\n- Description: ${task.description}\n- SDLC Phase: ${task.sdlcPhase}\n- Estimated Complexity: ${task.complexity}/5\n\nPROJECT CONTEXT:\n- Beauty CRM system built with TypeScript, React, Node.js, Prisma\n- Microservices architecture with Docker\n- Uses n8n for workflow automation\n- Follows Domain-Driven Design patterns\n\nANALYZE FOR AUTONOMOUS EXECUTION:\n\nProvide analysis in this EXACT JSON format:\n{\n  \"taskId\": \"${task.id}\",\n  \"feasibilityScore\": 0.85,\n  \"riskLevel\": \"low\",\n  \"estimatedMinutes\": 15,\n  \"canAutomate\": true,\n  \"requiredActions\": [\n    \"Read file content\",\n    \"Apply specific change\",\n    \"Validate syntax\"\n  ],\n  \"codeChanges\": {\n    \"changeType\": \"comment-update\",\n    \"linesAffected\": 1,\n    \"backupRequired\": true\n  },\n  \"qualityChecks\": [\"typescript-check\", \"eslint\"],\n  \"gitCommitMessage\": \"Fix TODO: description\",\n  \"recommendation\": \"proceed\",\n  \"reasoning\": \"Low risk change with clear implementation path\",\n  \"localExecution\": {\n    \"fileOperations\": [\"read\", \"write\"],\n    \"shellCommands\": [\"tsc --noEmit\", \"eslint\"],\n    \"gitOperations\": [\"add\", \"commit\"]\n  }\n}\n\nFeasibilityScore: 0.0-1.0 (confidence in autonomous execution)\nRiskLevel: low/medium/high\nRecommendation: proceed/review/defer\nCanAutomate: true/false (can this be done automatically?)\n\nONLY return the JSON object, no other text.`;\n\nreturn {\n  taskId: task.id,\n  originalTask: task,\n  analysisPrompt: analysisPrompt,\n  preparedAt: new Date().toISOString(),\n  agent: 'local-gemini-analyzer'\n};"}, "id": "prepare-analysis", "name": "Prepare Task Analysis", "type": "n8n-nodes-langchain.code", "typeVersion": 1, "position": [300, 300]}, {"parameters": {"url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "x-goog-api-key", "value": "={{ $env.GEMINI_API_KEY }}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "contents", "value": "={{ [{\"parts\": [{\"text\": $json.analysisPrompt}]}] }}"}, {"name": "generationConfig", "value": {"temperature": 0.1, "maxOutputTokens": 2000, "topP": 0.9}}]}, "options": {"timeout": 30000}}, "id": "gemini-analysis", "name": "Gemini AI Analysis", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [500, 300]}, {"parameters": {"mode": "runOnceForEachItem", "code": "// Parse Gemini response and create analyzed task\nconst originalData = $('Prepare Task Analysis').item.json;\nconst geminiResponse = $json;\n\nconsole.log(`🔍 Processing Gemini analysis for task: ${originalData.taskId}`);\n\ntry {\n  // Extract JSON from Gemini response\n  let responseText = '';\n  if (geminiResponse.candidates && geminiResponse.candidates[0]) {\n    responseText = geminiResponse.candidates[0].content.parts[0].text;\n  } else {\n    throw new Error('Invalid Gemini response structure');\n  }\n  \n  // Clean and parse JSON\n  const jsonMatch = responseText.match(/{[\\s\\S]*}/);\n  if (!jsonMatch) {\n    throw new Error('No JSON found in Gemini response');\n  }\n  \n  const analysis = JSON.parse(jsonMatch[0]);\n  \n  // Validate analysis structure\n  const requiredFields = ['feasibilityScore', 'riskLevel', 'estimatedMinutes', 'recommendation', 'canAutomate'];\n  const missingFields = requiredFields.filter(field => !(field in analysis));\n  \n  if (missingFields.length > 0) {\n    throw new Error(`Missing required fields: ${missingFields.join(', ')}`);\n  }\n  \n  // Create analyzed task with local execution focus\n  const analyzedTask = {\n    ...originalData.originalTask,\n    analysis: analysis,\n    analyzedAt: new Date().toISOString(),\n    status: 'analyzed',\n    agent: 'local-gemini-analyzer',\n    localExecution: analysis.localExecution || {\n      fileOperations: ['read', 'write'],\n      shellCommands: [],\n      gitOperations: ['add', 'commit']\n    }\n  };\n  \n  console.log(`✅ Analysis complete:`);\n  console.log(`   Feasibility: ${(analysis.feasibilityScore * 100).toFixed(1)}%`);\n  console.log(`   Risk Level: ${analysis.riskLevel}`);\n  console.log(`   Can Automate: ${analysis.canAutomate}`);\n  console.log(`   Estimated Time: ${analysis.estimatedMinutes} minutes`);\n  console.log(`   Recommendation: ${analysis.recommendation}`);\n  \n  return analyzedTask;\n  \n} catch (error) {\n  console.error(`❌ Error parsing Gemini analysis: ${error.message}`);\n  \n  // Return task with fallback analysis for local execution\n  return {\n    ...originalData.originalTask,\n    analysis: {\n      feasibilityScore: 0.3,\n      riskLevel: 'medium',\n      estimatedMinutes: 5,\n      canAutomate: false,\n      recommendation: 'review',\n      error: error.message,\n      reasoning: 'Failed to analyze with AI - requires manual review',\n      localExecution: {\n        fileOperations: [],\n        shellCommands: [],\n        gitOperations: []\n      }\n    },\n    analyzedAt: new Date().toISOString(),\n    status: 'analysis-failed',\n    agent: 'local-gemini-analyzer'\n  };\n}"}, "id": "parse-analysis", "name": "Parse Gemini Analysis", "type": "n8n-nodes-langchain.code", "typeVersion": 1, "position": [700, 300]}, {"parameters": {"mode": "runOnceForEachItem", "code": "// Route analyzed task for local execution\nconst analyzedTask = $json;\nconst analysis = analyzedTask.analysis;\n\nconsole.log(`🎯 Routing task ${analyzedTask.id} for local execution`);\nconsole.log(`   Recommendation: ${analysis.recommendation}`);\nconsole.log(`   Can Automate: ${analysis.canAutomate}`);\nconsole.log(`   Risk Level: ${analysis.riskLevel}`);\n\nlet routingDecision = {\n  taskId: analyzedTask.id,\n  originalTask: analyzedTask,\n  routedAt: new Date().toISOString(),\n  agent: 'local-gemini-analyzer'\n};\n\n// Determine local execution route\nif (analysis.recommendation === 'proceed' && analysis.canAutomate && analysis.feasibilityScore >= 0.6 && analysis.riskLevel === 'low') {\n  routingDecision.route = 'local-autonomous-execution';\n  routingDecision.webhook = 'http://localhost:5678/webhook/execute-local-task';\n  routingDecision.priority = 'high';\n  console.log(`✅ Routing to local autonomous execution`);\n  \n} else if (analysis.recommendation === 'proceed' && analysis.feasibilityScore >= 0.4) {\n  routingDecision.route = 'local-guided-execution';\n  routingDecision.webhook = 'http://localhost:5678/webhook/guided-local-task';\n  routingDecision.priority = 'medium';\n  console.log(`⚡ Routing to local guided execution`);\n  \n} else {\n  routingDecision.route = 'local-manual-review';\n  routingDecision.webhook = 'http://localhost:5678/webhook/manual-review-local';\n  routingDecision.priority = 'low';\n  console.log(`👀 Routing to local manual review`);\n}\n\nreturn routingDecision;"}, "id": "route-task", "name": "Route for Local Execution", "type": "n8n-nodes-langchain.code", "typeVersion": 1, "position": [900, 300]}, {"parameters": {"url": "={{ $json.webhook }}", "sendBody": true, "specifyHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "bodyParameters": {"parameters": [{"name": "task", "value": "={{ $json.originalTask }}"}, {"name": "route", "value": "={{ $json.route }}"}, {"name": "priority", "value": "={{ $json.priority }}"}, {"name": "routedAt", "value": "={{ $json.routedAt }}"}, {"name": "source", "value": "local-gemini-analyzer"}]}, "options": {"timeout": 10000}}, "id": "send-to-executor", "name": "Send to Local Executor", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [1100, 300]}], "connections": {"Tasks Found Webhook": {"main": [[{"node": "Prepare Task Analysis", "type": "main", "index": 0}]]}, "Prepare Task Analysis": {"main": [[{"node": "Gemini AI Analysis", "type": "main", "index": 0}]]}, "Gemini AI Analysis": {"main": [[{"node": "Parse Gemini Analysis", "type": "main", "index": 0}]]}, "Parse Gemini Analysis": {"main": [[{"node": "Route for Local Execution", "type": "main", "index": 0}]]}, "Route for Local Execution": {"main": [[{"node": "Send to Local Executor", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "1", "meta": {"templateCredsSetupCompleted": true}, "id": "local-gemini-analyzer", "tags": [{"createdAt": "2025-01-08T00:00:00.000Z", "updatedAt": "2025-01-08T00:00:00.000Z", "id": "local-sdlc-agent", "name": "Local SDLC Agent"}]}