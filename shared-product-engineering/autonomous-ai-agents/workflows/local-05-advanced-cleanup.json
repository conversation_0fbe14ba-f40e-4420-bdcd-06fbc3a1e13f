{"name": "Local 05 - Advanced Cleanup & Optimization Agent", "nodes": [{"parameters": {}, "id": "webhook-trigger", "name": "File Monitor Report Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [100, 300], "webhookId": "file-monitor-report"}, {"parameters": {"mode": "runOnceForEachItem", "code": "// Advanced Cleanup Agent - Handles complex cleanup and optimization\nconst fs = require('fs');\nconst path = require('path');\nconst { execSync } = require('child_process');\n\nconst monitorResult = $json.monitorResult;\nconst fixResult = $json.fixResult;\n\nconsole.log('🧹 Advanced Cleanup Agent - Processing complex issues...');\n\nconst advancedActions = [];\nconst optimizations = [];\nconst maintenanceTasks = [];\n\nconst agentsPath = '/private/var/www/2025/ollamar1/beauty-crm/shared-product-engineering/autonomous-ai-agents';\n\n// Only proceed if there are medium priority issues or optimization opportunities\nif (monitorResult.status === 'healthy' && fixResult.status === 'fixed') {\n  console.log('✅ System is healthy, checking for optimization opportunities...');\n} else if (monitorResult.issues.medium.length === 0) {\n  console.log('⏸️ No medium priority issues to handle');\n  return {\n    status: 'no-action-needed',\n    advancedActions: [],\n    optimizations: [],\n    maintenanceTasks: [],\n    processedAt: new Date().toISOString()\n  };\n}\n\n// Process medium priority issues that need advanced handling\nfor (const issue of monitorResult.issues.medium) {\n  console.log(`🔧 Advanced handling for: ${issue.type}`);\n  \n  switch (issue.type) {\n    case 'large-file':\n      // Analyze large files and suggest compression or archival\n      advancedActions.push({\n        type: 'analyze-large-file',\n        path: issue.path,\n        sizeMB: issue.sizeMB,\n        recommendation: issue.sizeMB > 500 ? 'archive' : 'compress',\n        description: `Large file analysis: ${path.basename(issue.path)} (${issue.sizeMB.toFixed(1)}MB)`\n      });\n      break;\n      \n    case 'permission-issue':\n      // Advanced permission analysis\n      advancedActions.push({\n        type: 'fix-advanced-permissions',\n        path: issue.path,\n        description: `Advanced permission fix for ${issue.itemName}`\n      });\n      break;\n      \n    case 'docker-check-failed':\n      // Docker system analysis and cleanup\n      advancedActions.push({\n        type: 'docker-system-cleanup',\n        description: 'Analyze and cleanup Docker system'\n      });\n      break;\n  }\n}\n\n// Optimization opportunities\nconsole.log('🚀 Checking for optimization opportunities...');\n\n// Check for duplicate files\ntry {\n  const workflowsPath = path.join(agentsPath, 'workflows');\n  if (fs.existsSync(workflowsPath)) {\n    const workflowFiles = fs.readdirSync(workflowsPath).filter(f => f.endsWith('.json'));\n    const fileSizes = new Map();\n    const duplicateCandidates = [];\n    \n    workflowFiles.forEach(file => {\n      const filePath = path.join(workflowsPath, file);\n      const stats = fs.statSync(filePath);\n      const size = stats.size;\n      \n      if (fileSizes.has(size)) {\n        duplicateCandidates.push({ file1: fileSizes.get(size), file2: file, size });\n      } else {\n        fileSizes.set(size, file);\n      }\n    });\n    \n    if (duplicateCandidates.length > 0) {\n      optimizations.push({\n        type: 'potential-duplicates',\n        count: duplicateCandidates.length,\n        candidates: duplicateCandidates,\n        description: `Found ${duplicateCandidates.length} potential duplicate workflow files`\n      });\n    }\n  }\n} catch (error) {\n  console.log(`⚠️ Could not check for duplicates: ${error.message}`);\n}\n\n// Check for outdated backup files\ntry {\n  const dataPath = path.join(agentsPath, 'data');\n  if (fs.existsSync(dataPath)) {\n    const backupFiles = [];\n    \n    function findBackups(dir) {\n      const entries = fs.readdirSync(dir, { withFileTypes: true });\n      entries.forEach(entry => {\n        const fullPath = path.join(dir, entry.name);\n        if (entry.isFile() && entry.name.includes('backup')) {\n          const stats = fs.statSync(fullPath);\n          const ageHours = (Date.now() - stats.mtime.getTime()) / (1000 * 60 * 60);\n          backupFiles.push({ path: fullPath, ageHours, size: stats.size });\n        } else if (entry.isDirectory() && !entry.name.startsWith('.')) {\n          findBackups(fullPath);\n        }\n      });\n    }\n    \n    findBackups(dataPath);\n    \n    const oldBackups = backupFiles.filter(b => b.ageHours > 168); // Older than 1 week\n    if (oldBackups.length > 0) {\n      optimizations.push({\n        type: 'cleanup-old-backups',\n        count: oldBackups.length,\n        totalSizeMB: oldBackups.reduce((sum, b) => sum + b.size, 0) / (1024 * 1024),\n        files: oldBackups,\n        description: `${oldBackups.length} backup files older than 1 week`\n      });\n    }\n  }\n} catch (error) {\n  console.log(`⚠️ Could not check backups: ${error.message}`);\n}\n\n// Docker system optimization\ntry {\n  const dockerImages = execSync('docker images --format \"{{.Repository}}:{{.Tag}}\\t{{.Size}}\"', { \n    encoding: 'utf8',\n    timeout: 10000\n  });\n  \n  const unusedImages = dockerImages.split('\\n')\n    .filter(line => line.includes('<none>') || line.includes('months ago'))\n    .length;\n    \n  if (unusedImages > 0) {\n    optimizations.push({\n      type: 'docker-cleanup',\n      unusedImages,\n      description: `${unusedImages} unused Docker images can be cleaned up`\n    });\n  }\n} catch (error) {\n  console.log(`⚠️ Could not check Docker images: ${error.message}`);\n}\n\n// Maintenance tasks\nmaintenanceTasks.push({\n  type: 'log-rotation',\n  description: 'Rotate and compress old log files',\n  frequency: 'weekly'\n});\n\nmaintenanceTasks.push({\n  type: 'workflow-validation',\n  description: 'Validate all workflow JSON files for integrity',\n  frequency: 'daily'\n});\n\nmaintenanceTasks.push({\n  type: 'dependency-check',\n  description: 'Check for outdated npm dependencies',\n  frequency: 'weekly'\n});\n\nconst totalOptimizations = advancedActions.length + optimizations.length;\n\nconsole.log(`🧹 Advanced Cleanup Results:`);\nconsole.log(`   Advanced actions: ${advancedActions.length}`);\nconsole.log(`   Optimizations: ${optimizations.length}`);\nconsole.log(`   Maintenance tasks: ${maintenanceTasks.length}`);\n\nreturn {\n  status: totalOptimizations > 0 ? 'optimizations-available' : 'optimized',\n  advancedActions,\n  optimizations,\n  maintenanceTasks,\n  totalOptimizations,\n  processedAt: new Date().toISOString(),\n  agent: 'advanced-cleanup'\n};"}, "id": "advanced-analysis", "name": "Advanced Analysis", "type": "n8n-nodes-langchain.code", "typeVersion": 1, "position": [300, 300]}, {"parameters": {"mode": "runOnceForEachItem", "code": "// Execute Advanced Cleanup Actions\nconst fs = require('fs');\nconst path = require('path');\nconst { execSync } = require('child_process');\n\nconst analysisResult = $json;\nconst executionResults = [];\n\nconsole.log(`🚀 Executing ${analysisResult.totalOptimizations} optimization actions...`);\n\nif (analysisResult.status === 'optimized') {\n  console.log('✅ System already optimized');\n  return {\n    status: 'no-actions-executed',\n    executionResults: [],\n    executedAt: new Date().toISOString()\n  };\n}\n\n// Execute advanced actions\nfor (const action of analysisResult.advancedActions) {\n  console.log(`🔧 Executing: ${action.type}`);\n  \n  try {\n    switch (action.type) {\n      case 'analyze-large-file':\n        // Analyze large file and provide recommendations\n        if (fs.existsSync(action.path)) {\n          const stats = fs.statSync(action.path);\n          const ext = path.extname(action.path).toLowerCase();\n          \n          let recommendation = 'review';\n          if (['.log', '.txt'].includes(ext) && action.sizeMB > 100) {\n            recommendation = 'compress';\n          } else if (['.backup', '.tmp'].includes(ext)) {\n            recommendation = 'archive-or-delete';\n          }\n          \n          executionResults.push({\n            action: action.type,\n            path: action.path,\n            success: true,\n            recommendation,\n            fileType: ext,\n            sizeMB: action.sizeMB,\n            lastModified: stats.mtime.toISOString()\n          });\n          \n          console.log(`✅ Analyzed large file: ${path.basename(action.path)} - Recommend: ${recommendation}`);\n        }\n        break;\n        \n      case 'fix-advanced-permissions':\n        // Fix advanced permission issues\n        if (fs.existsSync(action.path)) {\n          const stats = fs.statSync(action.path);\n          const isDirectory = stats.isDirectory();\n          \n          if (isDirectory) {\n            execSync(`chmod 755 \"${action.path}\"`);\n          } else {\n            execSync(`chmod 644 \"${action.path}\"`);\n            // Make scripts executable\n            if (action.path.endsWith('.sh') || action.path.endsWith('.js')) {\n              execSync(`chmod +x \"${action.path}\"`);\n            }\n          }\n          \n          executionResults.push({\n            action: action.type,\n            path: action.path,\n            success: true,\n            permissionsSet: isDirectory ? '755' : '644'\n          });\n          \n          console.log(`✅ Fixed permissions: ${path.basename(action.path)}`);\n        }\n        break;\n        \n      case 'docker-system-cleanup':\n        // Docker system cleanup\n        try {\n          const pruneOutput = execSync('docker system prune -f', { \n            encoding: 'utf8',\n            timeout: 30000\n          });\n          \n          executionResults.push({\n            action: action.type,\n            success: true,\n            output: pruneOutput,\n            description: 'Docker system pruned successfully'\n          });\n          \n          console.log(`✅ Docker system cleanup completed`);\n        } catch (dockerError) {\n          executionResults.push({\n            action: action.type,\n            success: false,\n            error: dockerError.message\n          });\n        }\n        break;\n    }\n  } catch (error) {\n    executionResults.push({\n      action: action.type,\n      success: false,\n      error: error.message\n    });\n    console.log(`❌ Failed to execute ${action.type}: ${error.message}`);\n  }\n}\n\n// Execute optimizations\nfor (const optimization of analysisResult.optimizations) {\n  console.log(`🚀 Optimization: ${optimization.type}`);\n  \n  try {\n    switch (optimization.type) {\n      case 'cleanup-old-backups':\n        // Clean up old backup files\n        let deletedCount = 0;\n        let freedSpaceMB = 0;\n        \n        for (const backup of optimization.files) {\n          if (fs.existsSync(backup.path) && backup.ageHours > 168) {\n            const sizeMB = backup.size / (1024 * 1024);\n            fs.unlinkSync(backup.path);\n            deletedCount++;\n            freedSpaceMB += sizeMB;\n          }\n        }\n        \n        executionResults.push({\n          action: optimization.type,\n          success: true,\n          deletedFiles: deletedCount,\n          freedSpaceMB: freedSpaceMB.toFixed(2),\n          description: `Deleted ${deletedCount} old backup files, freed ${freedSpaceMB.toFixed(2)}MB`\n        });\n        \n        console.log(`✅ Cleaned up ${deletedCount} old backups, freed ${freedSpaceMB.toFixed(2)}MB`);\n        break;\n        \n      case 'docker-cleanup':\n        // Clean up unused Docker images\n        try {\n          const cleanupOutput = execSync('docker image prune -f', { \n            encoding: 'utf8',\n            timeout: 30000\n          });\n          \n          executionResults.push({\n            action: optimization.type,\n            success: true,\n            output: cleanupOutput,\n            description: 'Cleaned up unused Docker images'\n          });\n          \n          console.log(`✅ Docker image cleanup completed`);\n        } catch (dockerError) {\n          executionResults.push({\n            action: optimization.type,\n            success: false,\n            error: dockerError.message\n          });\n        }\n        break;\n        \n      case 'potential-duplicates':\n        // Report potential duplicates (don't auto-delete)\n        executionResults.push({\n          action: optimization.type,\n          success: true,\n          duplicateCandidates: optimization.candidates,\n          description: `Identified ${optimization.count} potential duplicate files for manual review`\n        });\n        \n        console.log(`📋 Identified ${optimization.count} potential duplicates for review`);\n        break;\n    }\n  } catch (error) {\n    executionResults.push({\n      action: optimization.type,\n      success: false,\n      error: error.message\n    });\n    console.log(`❌ Failed optimization ${optimization.type}: ${error.message}`);\n  }\n}\n\nconst successfulActions = executionResults.filter(r => r.success).length;\nconst failedActions = executionResults.filter(r => !r.success).length;\n\nconsole.log(`🏁 Advanced Cleanup Execution Results:`);\nconsole.log(`   Successful actions: ${successfulActions}`);\nconsole.log(`   Failed actions: ${failedActions}`);\n\nreturn {\n  status: failedActions === 0 ? 'optimizations-completed' : 'partial-optimization',\n  executionResults,\n  successfulActions,\n  failedActions,\n  maintenanceTasks: analysisResult.maintenanceTasks,\n  executedAt: new Date().toISOString(),\n  agent: 'advanced-cleanup-executor'\n};"}, "id": "execute-cleanup", "name": "Execute Advanced Cleanup", "type": "n8n-nodes-langchain.code", "typeVersion": 1, "position": [500, 300]}, {"parameters": {"url": "http://localhost:5678/webhook/advanced-cleanup-complete", "sendBody": true, "specifyHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "bodyParameters": {"parameters": [{"name": "analysisResult", "value": "={{ $('Advanced Analysis').item.json }}"}, {"name": "executionResult", "value": "={{ $json }}"}, {"name": "status", "value": "={{ $json.status }}"}, {"name": "successfulActions", "value": "={{ $json.successfulActions }}"}, {"name": "source", "value": "advanced-cleanup-agent"}]}}, "id": "notify-completion", "name": "Notify Cleanup Complete", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [700, 300]}, {"parameters": {"mode": "runOnceForAllItems", "code": "const analysisResult = $('Advanced Analysis').item.json;\nconst executionResult = $json.executionResult;\n\nconst summary = {\n  agent: 'advanced-cleanup-optimization',\n  systemStatus: executionResult.status,\n  analysis: {\n    totalOptimizations: analysisResult.totalOptimizations,\n    advancedActions: analysisResult.advancedActions.length,\n    optimizations: analysisResult.optimizations.length,\n    maintenanceTasks: analysisResult.maintenanceTasks.length\n  },\n  execution: {\n    successfulActions: executionResult.successfulActions,\n    failedActions: executionResult.failedActions,\n    totalExecuted: executionResult.executionResults.length\n  },\n  processedAt: analysisResult.processedAt,\n  executedAt: executionResult.executedAt\n};\n\nconsole.log('✅ Advanced Cleanup & Optimization Complete!');\nconsole.log(`🚀 System Status: ${summary.systemStatus.toUpperCase()}`);\nconsole.log(`📊 Total Optimizations: ${summary.analysis.totalOptimizations}`);\nconsole.log(`🔧 Successful Actions: ${summary.execution.successfulActions}`);\nconsole.log(`❌ Failed Actions: ${summary.execution.failedActions}`);\nconsole.log(`🛠️ Maintenance Tasks: ${summary.analysis.maintenanceTasks}`);\n\n// Log specific achievements\nconst achievements = [];\nexecutionResult.executionResults.forEach(result => {\n  if (result.success) {\n    switch (result.action) {\n      case 'cleanup-old-backups':\n        achievements.push(`🗑️ Freed ${result.freedSpaceMB}MB by cleaning ${result.deletedFiles} old backups`);\n        break;\n      case 'docker-cleanup':\n        achievements.push(`🐳 Cleaned up unused Docker images`);\n        break;\n      case 'analyze-large-file':\n        achievements.push(`📊 Analyzed large file: ${path.basename(result.path)} (${result.sizeMB.toFixed(1)}MB)`);\n        break;\n      case 'fix-advanced-permissions':\n        achievements.push(`🔐 Fixed permissions: ${path.basename(result.path)}`);\n        break;\n    }\n  }\n});\n\nif (achievements.length > 0) {\n  console.log('🎯 Key Achievements:');\n  achievements.forEach(achievement => {\n    console.log(`   ${achievement}`);\n  });\n}\n\nreturn summary;"}, "id": "cleanup-summary", "name": "Cleanup Summary", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 300]}], "connections": {"File Monitor Report Webhook": {"main": [[{"node": "Advanced Analysis", "type": "main", "index": 0}]]}, "Advanced Analysis": {"main": [[{"node": "Execute Advanced Cleanup", "type": "main", "index": 0}]]}, "Execute Advanced Cleanup": {"main": [[{"node": "Notify Cleanup Complete", "type": "main", "index": 0}]]}, "Notify Cleanup Complete": {"main": [[{"node": "Cleanup Summary", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "1", "meta": {"templateCredsSetupCompleted": true}, "id": "advanced-cleanup-optimization", "tags": [{"createdAt": "2025-01-08T00:00:00.000Z", "updatedAt": "2025-01-08T00:00:00.000Z", "id": "local-sdlc-agent", "name": "Local SDLC Agent"}]}