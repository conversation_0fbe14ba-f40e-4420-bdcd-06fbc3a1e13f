{"name": "Local 04 - File Monitor & Cleanup Agent", "nodes": [{"parameters": {"rule": {"interval": [{"field": "cronExpression", "value": "0 */2 * * * *"}]}}, "id": "cron-trigger", "name": "Every 2 Minutes", "type": "n8n-nodes-base.cron", "typeVersion": 1, "position": [100, 300]}, {"parameters": {"mode": "runOnceForAllItems", "code": "// File Monitor Agent - Monitors autonomous-ai-agents directory\nconst fs = require('fs');\nconst path = require('path');\nconst { execSync } = require('child_process');\n\nconst monitorPath = '/private/var/www/2025/ollamar1/beauty-crm/shared-product-engineering/autonomous-ai-agents';\nconst issues = [];\nconst cleanupActions = [];\n\nconsole.log('🔍 File Monitor - Scanning autonomous-ai-agents directory...');\n\n// Check if directory exists and is accessible\nif (!fs.existsSync(monitorPath)) {\n  issues.push({\n    type: 'missing-directory',\n    severity: 'critical',\n    path: monitorPath,\n    description: 'Autonomous agents directory not found',\n    action: 'create-directory'\n  });\n  return { issues, cleanupActions, status: 'critical' };\n}\n\n// Monitor key files and directories\nconst requiredStructure = {\n  'workflows': { type: 'directory', required: true },\n  'data': { type: 'directory', required: true },\n  'docker-compose.yml': { type: 'file', required: true },\n  'quick-start.sh': { type: 'file', required: true, executable: true },\n  'import-local-workflows.js': { type: 'file', required: true, executable: true },\n  '.env.example': { type: 'file', required: true }\n};\n\n// Check required structure\nfor (const [itemName, config] of Object.entries(requiredStructure)) {\n  const itemPath = path.join(monitorPath, itemName);\n  \n  if (!fs.existsSync(itemPath)) {\n    issues.push({\n      type: 'missing-item',\n      severity: config.required ? 'high' : 'medium',\n      path: itemPath,\n      description: `Missing ${config.type}: ${itemName}`,\n      action: config.type === 'directory' ? 'create-directory' : 'restore-file',\n      itemName: itemName,\n      itemType: config.type\n    });\n  } else {\n    // Check file permissions\n    if (config.executable) {\n      try {\n        const stats = fs.statSync(itemPath);\n        const isExecutable = (stats.mode & parseInt('111', 8)) !== 0;\n        if (!isExecutable) {\n          issues.push({\n            type: 'permission-issue',\n            severity: 'medium',\n            path: itemPath,\n            description: `File not executable: ${itemName}`,\n            action: 'fix-permissions',\n            itemName: itemName\n          });\n        }\n      } catch (error) {\n        issues.push({\n          type: 'permission-error',\n          severity: 'high',\n          path: itemPath,\n          description: `Cannot check permissions: ${error.message}`,\n          action: 'fix-permissions',\n          itemName: itemName\n        });\n      }\n    }\n  }\n}\n\n// Check workflows directory content\nconst workflowsPath = path.join(monitorPath, 'workflows');\nif (fs.existsSync(workflowsPath)) {\n  const workflowFiles = fs.readdirSync(workflowsPath).filter(f => f.endsWith('.json'));\n  const expectedWorkflows = [\n    'local-01-task-scanner.json',\n    'local-02-gemini-analyzer.json', \n    'local-03-file-executor.json'\n  ];\n  \n  expectedWorkflows.forEach(expectedFile => {\n    if (!workflowFiles.includes(expectedFile)) {\n      issues.push({\n        type: 'missing-workflow',\n        severity: 'high',\n        path: path.join(workflowsPath, expectedFile),\n        description: `Missing workflow file: ${expectedFile}`,\n        action: 'restore-workflow',\n        workflowName: expectedFile\n      });\n    }\n  });\n  \n  // Check for corrupted workflow files\n  workflowFiles.forEach(workflowFile => {\n    const workflowPath = path.join(workflowsPath, workflowFile);\n    try {\n      const content = fs.readFileSync(workflowPath, 'utf8');\n      JSON.parse(content); // Validate JSON\n      \n      // Check for required workflow properties\n      const workflow = JSON.parse(content);\n      if (!workflow.name || !workflow.nodes || !workflow.connections) {\n        issues.push({\n          type: 'corrupted-workflow',\n          severity: 'high',\n          path: workflowPath,\n          description: `Corrupted workflow file: ${workflowFile}`,\n          action: 'fix-workflow',\n          workflowName: workflowFile\n        });\n      }\n    } catch (error) {\n      issues.push({\n        type: 'invalid-json',\n        severity: 'high',\n        path: workflowPath,\n        description: `Invalid JSON in workflow: ${workflowFile} - ${error.message}`,\n        action: 'fix-workflow',\n        workflowName: workflowFile\n      });\n    }\n  });\n}\n\n// Check for temporary/backup files that should be cleaned up\nfunction scanForCleanup(dir, depth = 0) {\n  if (depth > 3) return; // Limit recursion depth\n  \n  try {\n    const entries = fs.readdirSync(dir, { withFileTypes: true });\n    \n    entries.forEach(entry => {\n      const fullPath = path.join(dir, entry.name);\n      \n      if (entry.isFile()) {\n        // Check for files that should be cleaned up\n        if (entry.name.endsWith('.backup') || \n            entry.name.endsWith('.tmp') ||\n            entry.name.endsWith('.log') ||\n            entry.name.startsWith('.DS_Store') ||\n            entry.name.endsWith('~')) {\n          \n          const stats = fs.statSync(fullPath);\n          const ageHours = (Date.now() - stats.mtime.getTime()) / (1000 * 60 * 60);\n          \n          if (ageHours > 24) { // Older than 24 hours\n            cleanupActions.push({\n              type: 'delete-old-file',\n              path: fullPath,\n              description: `Delete old ${entry.name} (${ageHours.toFixed(1)}h old)`,\n              ageHours: ageHours\n            });\n          }\n        }\n        \n        // Check for large files\n        const stats = fs.statSync(fullPath);\n        const sizeMB = stats.size / (1024 * 1024);\n        if (sizeMB > 100) { // Files larger than 100MB\n          issues.push({\n            type: 'large-file',\n            severity: 'medium',\n            path: fullPath,\n            description: `Large file detected: ${entry.name} (${sizeMB.toFixed(1)}MB)`,\n            action: 'review-large-file',\n            sizeMB: sizeMB\n          });\n        }\n      } else if (entry.isDirectory() && !entry.name.startsWith('.')) {\n        scanForCleanup(fullPath, depth + 1);\n      }\n    });\n  } catch (error) {\n    console.log(`Error scanning ${dir}: ${error.message}`);\n  }\n}\n\nscanForCleanup(monitorPath);\n\n// Check Docker container status\ntry {\n  const dockerPs = execSync('docker compose ps --format json', { \n    cwd: monitorPath, \n    encoding: 'utf8' \n  });\n  \n  const containers = dockerPs.trim().split('\\n').map(line => {\n    try {\n      return JSON.parse(line);\n    } catch {\n      return null;\n    }\n  }).filter(Boolean);\n  \n  const n8nContainer = containers.find(c => c.Service === 'n8n');\n  if (!n8nContainer || n8nContainer.State !== 'running') {\n    issues.push({\n      type: 'container-issue',\n      severity: 'critical',\n      description: 'n8n container not running',\n      action: 'restart-container',\n      containerName: 'n8n'\n    });\n  }\n} catch (error) {\n  issues.push({\n    type: 'docker-check-failed',\n    severity: 'medium',\n    description: `Could not check Docker status: ${error.message}`,\n    action: 'manual-check'\n  });\n}\n\n// Categorize issues by severity\nconst criticalIssues = issues.filter(i => i.severity === 'critical');\nconst highIssues = issues.filter(i => i.severity === 'high');\nconst mediumIssues = issues.filter(i => i.severity === 'medium');\n\nconsole.log(`🔍 File Monitor Results:`);\nconsole.log(`   Critical issues: ${criticalIssues.length}`);\nconsole.log(`   High priority: ${highIssues.length}`);\nconsole.log(`   Medium priority: ${mediumIssues.length}`);\nconsole.log(`   Cleanup actions: ${cleanupActions.length}`);\n\nconst status = criticalIssues.length > 0 ? 'critical' :\n              highIssues.length > 0 ? 'high' :\n              mediumIssues.length > 0 ? 'medium' : 'healthy';\n\nreturn {\n  monitorPath,\n  status,\n  totalIssues: issues.length,\n  issues: {\n    critical: criticalIssues,\n    high: highIssues,\n    medium: mediumIssues,\n    all: issues\n  },\n  cleanupActions,\n  scannedAt: new Date().toISOString(),\n  agent: 'file-monitor'\n};"}, "id": "file-monitor", "name": "Monitor Files & Structure", "type": "n8n-nodes-langchain.code", "typeVersion": 1, "position": [300, 300]}, {"parameters": {"mode": "runOnceForEachItem", "code": "// Auto-Fix Agent - Automatically fixes detected issues\nconst fs = require('fs');\nconst path = require('path');\nconst { execSync } = require('child_process');\n\nconst monitorResult = $json;\nconst fixResults = [];\nconst skippedActions = [];\n\nconsole.log(`🔧 Auto-Fix Agent - Processing ${monitorResult.totalIssues} issues...`);\n\nif (monitorResult.status === 'healthy') {\n  console.log('✅ No issues to fix - system is healthy');\n  return {\n    status: 'healthy',\n    fixResults: [],\n    skippedActions: [],\n    fixedAt: new Date().toISOString()\n  };\n}\n\n// Process critical and high priority issues first\nconst issuesToFix = [...monitorResult.issues.critical, ...monitorResult.issues.high];\n\nfor (const issue of issuesToFix) {\n  console.log(`🔧 Fixing ${issue.type}: ${issue.description}`);\n  \n  try {\n    switch (issue.action) {\n      case 'create-directory':\n        if (!fs.existsSync(issue.path)) {\n          fs.mkdirSync(issue.path, { recursive: true });\n          fixResults.push({\n            issue: issue.type,\n            action: 'created-directory',\n            path: issue.path,\n            success: true\n          });\n          console.log(`✅ Created directory: ${issue.path}`);\n        }\n        break;\n        \n      case 'fix-permissions':\n        if (fs.existsSync(issue.path)) {\n          execSync(`chmod +x \"${issue.path}\"`);\n          fixResults.push({\n            issue: issue.type,\n            action: 'fixed-permissions',\n            path: issue.path,\n            success: true\n          });\n          console.log(`✅ Fixed permissions: ${issue.itemName}`);\n        }\n        break;\n        \n      case 'restore-file':\n        // Create basic file templates\n        if (issue.itemName === '.env.example' && !fs.existsSync(issue.path)) {\n          const envTemplate = `# SDLC Monster Environment Configuration\\n\\n# Required: Gemini AI API Key\\nGEMINI_API_KEY=your_gemini_api_key_here\\n\\n# Workspace Configuration\\nWORKSPACE_ROOT=/private/var/www/2025/ollamar1/beauty-crm\\n`;\n          fs.writeFileSync(issue.path, envTemplate, 'utf8');\n          fixResults.push({\n            issue: issue.type,\n            action: 'restored-env-example',\n            path: issue.path,\n            success: true\n          });\n          console.log(`✅ Restored .env.example`);\n        }\n        break;\n        \n      case 'restart-container':\n        if (issue.containerName === 'n8n') {\n          try {\n            execSync('docker compose restart n8n', { \n              cwd: monitorResult.monitorPath,\n              timeout: 30000\n            });\n            fixResults.push({\n              issue: issue.type,\n              action: 'restarted-container',\n              container: issue.containerName,\n              success: true\n            });\n            console.log(`✅ Restarted n8n container`);\n          } catch (restartError) {\n            fixResults.push({\n              issue: issue.type,\n              action: 'restart-failed',\n              container: issue.containerName,\n              success: false,\n              error: restartError.message\n            });\n            console.log(`❌ Failed to restart container: ${restartError.message}`);\n          }\n        }\n        break;\n        \n      default:\n        skippedActions.push({\n          issue: issue.type,\n          reason: `No auto-fix available for action: ${issue.action}`,\n          path: issue.path\n        });\n        console.log(`⏸️ Skipped ${issue.type}: No auto-fix available`);\n    }\n  } catch (error) {\n    fixResults.push({\n      issue: issue.type,\n      action: issue.action,\n      path: issue.path,\n      success: false,\n      error: error.message\n    });\n    console.log(`❌ Failed to fix ${issue.type}: ${error.message}`);\n  }\n}\n\n// Process cleanup actions\nfor (const cleanup of monitorResult.cleanupActions) {\n  console.log(`🧹 Cleanup: ${cleanup.description}`);\n  \n  try {\n    switch (cleanup.type) {\n      case 'delete-old-file':\n        if (fs.existsSync(cleanup.path)) {\n          fs.unlinkSync(cleanup.path);\n          fixResults.push({\n            issue: 'cleanup',\n            action: 'deleted-old-file',\n            path: cleanup.path,\n            success: true,\n            ageHours: cleanup.ageHours\n          });\n          console.log(`✅ Deleted old file: ${path.basename(cleanup.path)}`);\n        }\n        break;\n        \n      default:\n        skippedActions.push({\n          issue: 'cleanup',\n          reason: `Unknown cleanup type: ${cleanup.type}`,\n          path: cleanup.path\n        });\n    }\n  } catch (error) {\n    fixResults.push({\n      issue: 'cleanup',\n      action: cleanup.type,\n      path: cleanup.path,\n      success: false,\n      error: error.message\n    });\n    console.log(`❌ Cleanup failed: ${error.message}`);\n  }\n}\n\nconst successfulFixes = fixResults.filter(f => f.success).length;\nconst failedFixes = fixResults.filter(f => !f.success).length;\n\nconsole.log(`🏁 Auto-Fix Results:`);\nconsole.log(`   Successful fixes: ${successfulFixes}`);\nconsole.log(`   Failed fixes: ${failedFixes}`);\nconsole.log(`   Skipped actions: ${skippedActions.length}`);\n\nreturn {\n  status: failedFixes === 0 ? 'fixed' : 'partial-fix',\n  originalIssues: monitorResult.totalIssues,\n  fixResults,\n  skippedActions,\n  successfulFixes,\n  failedFixes,\n  fixedAt: new Date().toISOString(),\n  agent: 'auto-fix'\n};"}, "id": "auto-fix", "name": "Auto-Fix Issues", "type": "n8n-nodes-langchain.code", "typeVersion": 1, "position": [500, 300]}, {"parameters": {"url": "http://localhost:5678/webhook/file-monitor-report", "sendBody": true, "specifyHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "bodyParameters": {"parameters": [{"name": "monitorResult", "value": "={{ $('Monitor Files & Structure').item.json }}"}, {"name": "fixResult", "value": "={{ $json }}"}, {"name": "status", "value": "={{ $json.status }}"}, {"name": "successfulFixes", "value": "={{ $json.successfulFixes }}"}, {"name": "source", "value": "file-monitor-agent"}]}}, "id": "notify-results", "name": "Notify Monitor Results", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [700, 300]}, {"parameters": {"mode": "runOnceForAllItems", "code": "const monitorResult = $('Monitor Files & Structure').item.json;\nconst fixResult = $json.fixResult;\n\nconst summary = {\n  agent: 'file-monitor-cleanup',\n  monitoringPath: monitorResult.monitorPath,\n  systemStatus: fixResult.status,\n  monitoring: {\n    totalIssuesFound: monitorResult.totalIssues,\n    criticalIssues: monitorResult.issues.critical.length,\n    highPriorityIssues: monitorResult.issues.high.length,\n    cleanupActionsFound: monitorResult.cleanupActions.length\n  },\n  fixes: {\n    successfulFixes: fixResult.successfulFixes,\n    failedFixes: fixResult.failedFixes,\n    skippedActions: fixResult.skippedActions.length\n  },\n  scannedAt: monitorResult.scannedAt,\n  fixedAt: fixResult.fixedAt,\n  nextScan: new Date(Date.now() + 2 * 60 * 1000).toISOString() // Next scan in 2 minutes\n};\n\nconsole.log('✅ File Monitor & Cleanup Complete!');\nconsole.log(`🔍 System Status: ${summary.systemStatus.toUpperCase()}`);\nconsole.log(`📊 Issues Found: ${summary.monitoring.totalIssuesFound}`);\nconsole.log(`🔧 Successful Fixes: ${summary.fixes.successfulFixes}`);\nconsole.log(`⏸️ Skipped Actions: ${summary.fixes.skippedActions}`);\nconsole.log(`⏰ Next scan: ${summary.nextScan}`);\n\nif (summary.monitoring.criticalIssues > 0) {\n  console.log(`🚨 CRITICAL: ${summary.monitoring.criticalIssues} critical issues detected!`);\n}\n\nreturn summary;"}, "id": "monitor-summary", "name": "Monitor Summary", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 300]}], "connections": {"Every 2 Minutes": {"main": [[{"node": "Monitor Files & Structure", "type": "main", "index": 0}]]}, "Monitor Files & Structure": {"main": [[{"node": "Auto-Fix Issues", "type": "main", "index": 0}]]}, "Auto-Fix Issues": {"main": [[{"node": "Notify Monitor Results", "type": "main", "index": 0}]]}, "Notify Monitor Results": {"main": [[{"node": "Monitor Summary", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "1", "meta": {"templateCredsSetupCompleted": true}, "id": "file-monitor-cleanup", "tags": [{"createdAt": "2025-01-08T00:00:00.000Z", "updatedAt": "2025-01-08T00:00:00.000Z", "id": "local-sdlc-agent", "name": "Local SDLC Agent"}]}