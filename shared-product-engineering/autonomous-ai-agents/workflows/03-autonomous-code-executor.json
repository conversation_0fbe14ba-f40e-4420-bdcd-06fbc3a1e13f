{"name": "03 - Autonomous Code Executor", "nodes": [{"parameters": {}, "id": "webhook-trigger", "name": "Execute Task Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [100, 300], "webhookId": "execute-task"}, {"parameters": {"mode": "runOnceForEachItem", "jsCode": "// Autonomous Code Executor - Makes real file changes\nconst fs = require('fs');\nconst path = require('path');\nconst { execSync } = require('child_process');\n\nconst taskData = $json.task;\nconst analysis = taskData.analysis;\n\nconsole.log(`🚀 Autonomous Code Executor - Processing task: ${taskData.id}`);\nconsole.log(`📁 File: ${taskData.file}`);\nconsole.log(`🎯 Type: ${taskData.type}`);\nconsole.log(`⚡ Feasibility: ${(analysis.feasibilityScore * 100).toFixed(1)}%`);\n\nconst workspaceRoot = '/workspace';\nconst filePath = path.join(workspaceRoot, taskData.file);\n\nlet executionResult = {\n  taskId: taskData.id,\n  status: 'in-progress',\n  startedAt: new Date().toISOString(),\n  changes: [],\n  errors: [],\n  gitChanges: [],\n  agent: 'autonomous-code-executor'\n};\n\ntry {\n  // Check if file exists\n  if (!fs.existsSync(filePath)) {\n    throw new Error(`File not found: ${taskData.file}`);\n  }\n  \n  // Read current file content\n  const originalContent = fs.readFileSync(filePath, 'utf8');\n  const lines = originalContent.split('\\n');\n  \n  console.log(`📖 Read file: ${lines.length} lines`);\n  \n  // Execute based on task type\n  let newContent = originalContent;\n  let changesMade = [];\n  \n  switch (taskData.type) {\n    case 'todo':\n    case 'fixme':\n      // Add completion timestamp and mark as reviewed\n      const targetLineIndex = taskData.line - 1;\n      if (targetLineIndex >= 0 && targetLineIndex < lines.length) {\n        const originalLine = lines[targetLineIndex];\n        const reviewedLine = `${originalLine} // ✅ AI-reviewed: ${new Date().toISOString().split('T')[0]}`;\n        lines[targetLineIndex] = reviewedLine;\n        newContent = lines.join('\\n');\n        changesMade.push(`Added AI review timestamp to line ${taskData.line}`);\n      }\n      break;\n      \n    case 'deprecated':\n      // Add deprecation notice and suggest replacement\n      const deprecatedLineIndex = taskData.line - 1;\n      if (deprecatedLineIndex >= 0 && deprecatedLineIndex < lines.length) {\n        const originalLine = lines[deprecatedLineIndex];\n        const warningLine = `${originalLine} // ⚠️ DEPRECATED - Flagged for replacement ${new Date().toISOString().split('T')[0]}`;\n        lines[deprecatedLineIndex] = warningLine;\n        newContent = lines.join('\\n');\n        changesMade.push(`Added deprecation warning to line ${taskData.line}`);\n      }\n      break;\n      \n    case 'optimize':\n      // Add optimization note\n      const optimizeLineIndex = taskData.line - 1;\n      if (optimizeLineIndex >= 0 && optimizeLineIndex < lines.length) {\n        const originalLine = lines[optimizeLineIndex];\n        const optimizeNote = `${originalLine} // 🚀 Optimization candidate - Analyzed ${new Date().toISOString().split('T')[0]}`;\n        lines[optimizeLineIndex] = optimizeNote;\n        newContent = lines.join('\\n');\n        changesMade.push(`Added optimization note to line ${taskData.line}`);\n      }\n      break;\n      \n    case 'security':\n      // Add security review flag\n      const securityLineIndex = taskData.line - 1;\n      if (securityLineIndex >= 0 && securityLineIndex < lines.length) {\n        const originalLine = lines[securityLineIndex];\n        const securityFlag = `${originalLine} // 🔒 SECURITY REVIEW REQUIRED - Flagged ${new Date().toISOString().split('T')[0]}`;\n        lines[securityLineIndex] = securityFlag;\n        newContent = lines.join('\\n');\n        changesMade.push(`Added security review flag to line ${taskData.line}`);\n      }\n      break;\n      \n    case 'bug':\n      // Add bug tracking comment\n      const bugLineIndex = taskData.line - 1;\n      if (bugLineIndex >= 0 && bugLineIndex < lines.length) {\n        const originalLine = lines[bugLineIndex];\n        const bugTracker = `${originalLine} // 🐛 BUG TRACKED - Needs investigation ${new Date().toISOString().split('T')[0]}`;\n        lines[bugLineIndex] = bugTracker;\n        newContent = lines.join('\\n');\n        changesMade.push(`Added bug tracking comment to line ${taskData.line}`);\n      }\n      break;\n      \n    default:\n      // Generic task processing - add analysis timestamp\n      const genericLineIndex = taskData.line - 1;\n      if (genericLineIndex >= 0 && genericLineIndex < lines.length) {\n        const originalLine = lines[genericLineIndex];\n        const analysisNote = `${originalLine} // 📝 AI-analyzed: ${new Date().toISOString().split('T')[0]}`;\n        lines[genericLineIndex] = analysisNote;\n        newContent = lines.join('\\n');\n        changesMade.push(`Added AI analysis timestamp to line ${taskData.line}`);\n      }\n  }\n  \n  // Write changes if any were made\n  if (newContent !== originalContent) {\n    // Create backup\n    const backupPath = `${filePath}.backup.${Date.now()}`;\n    fs.writeFileSync(backupPath, originalContent, 'utf8');\n    \n    // Write new content\n    fs.writeFileSync(filePath, newContent, 'utf8');\n    \n    console.log(`✅ File updated: ${taskData.file}`);\n    console.log(`💾 Backup created: ${path.basename(backupPath)}`);\n    \n    executionResult.changes = changesMade;\n    executionResult.backupFile = backupPath;\n    \n    // Check git status\n    try {\n      const gitStatus = execSync('git status --porcelain', { \n        cwd: workspaceRoot, \n        encoding: 'utf8' \n      });\n      \n      if (gitStatus.trim()) {\n        executionResult.gitChanges = gitStatus.trim().split('\\n');\n        console.log(`📝 Git changes detected: ${executionResult.gitChanges.length} files`);\n      }\n    } catch (gitError) {\n      console.log(`⚠️ Could not check git status: ${gitError.message}`);\n    }\n    \n    executionResult.status = 'completed';\n  } else {\n    executionResult.status = 'no-changes';\n    executionResult.changes = ['No changes were necessary'];\n  }\n  \n} catch (error) {\n  console.error(`❌ Execution error: ${error.message}`);\n  executionResult.status = 'failed';\n  executionResult.errors.push(error.message);\n}\n\nexecutionResult.completedAt = new Date().toISOString();\nexecutionResult.duration = new Date() - new Date(executionResult.startedAt);\n\nconsole.log(`🏁 Execution ${executionResult.status}: ${executionResult.changes.length} changes made`);\n\nreturn executionResult;"}, "id": "execute-code-changes", "name": "Execute Code Changes", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [300, 300]}, {"parameters": {"mode": "runOnceForEachItem", "jsCode": "// Quality Gate - Run checks on the changes\nconst { execSync } = require('child_process');\nconst executionResult = $json;\n\nconsole.log(`🔍 Quality Gate - Checking changes for task: ${executionResult.taskId}`);\n\nlet qualityResult = {\n  taskId: executionResult.taskId,\n  executionResult: executionResult,\n  qualityChecks: [],\n  passed: true,\n  checkedAt: new Date().toISOString(),\n  agent: 'quality-gate'\n};\n\nif (executionResult.status === 'completed' && executionResult.changes.length > 0) {\n  const workspaceRoot = '/workspace';\n  \n  // Run TypeScript check if it's a TS file\n  const taskData = $('Execute Task Webhook').item.json.task;\n  if (taskData.file.endsWith('.ts') || taskData.file.endsWith('.tsx')) {\n    try {\n      console.log('🔧 Running TypeScript check...');\n      execSync('npx tsc --noEmit --skipLibCheck', { \n        cwd: workspaceRoot, \n        encoding: 'utf8',\n        timeout: 30000\n      });\n      qualityResult.qualityChecks.push({ check: 'typescript', status: 'passed' });\n      console.log('✅ TypeScript check passed');\n    } catch (tsError) {\n      qualityResult.qualityChecks.push({ \n        check: 'typescript', \n        status: 'failed', \n        error: tsError.message \n      });\n      qualityResult.passed = false;\n      console.log('❌ TypeScript check failed');\n    }\n  }\n  \n  // Run ESLint check\n  try {\n    console.log('🔧 Running ESLint check...');\n    execSync(`npx eslint ${taskData.file} --format=json`, { \n      cwd: workspaceRoot, \n      encoding: 'utf8',\n      timeout: 20000\n    });\n    qualityResult.qualityChecks.push({ check: 'eslint', status: 'passed' });\n    console.log('✅ ESLint check passed');\n  } catch (lintError) {\n    // ESLint returns non-zero exit code for warnings/errors\n    const isWarningOnly = lintError.message.includes('warning');\n    qualityResult.qualityChecks.push({ \n      check: 'eslint', \n      status: isWarningOnly ? 'warning' : 'failed',\n      error: lintError.message \n    });\n    if (!isWarningOnly) {\n      qualityResult.passed = false;\n    }\n    console.log(`${isWarningOnly ? '⚠️' : '❌'} ESLint check ${isWarningOnly ? 'warnings' : 'failed'}`);\n  }\n  \n  // File size check\n  const fs = require('fs');\n  const path = require('path');\n  const filePath = path.join(workspaceRoot, taskData.file);\n  \n  try {\n    const stats = fs.statSync(filePath);\n    const fileSizeMB = stats.size / (1024 * 1024);\n    \n    if (fileSizeMB > 1) { // Files larger than 1MB\n      qualityResult.qualityChecks.push({ \n        check: 'file-size', \n        status: 'warning',\n        message: `File size: ${fileSizeMB.toFixed(2)}MB` \n      });\n    } else {\n      qualityResult.qualityChecks.push({ check: 'file-size', status: 'passed' });\n    }\n  } catch (sizeError) {\n    qualityResult.qualityChecks.push({ \n      check: 'file-size', \n      status: 'failed',\n      error: sizeError.message \n    });\n  }\n  \n} else {\n  qualityResult.qualityChecks.push({ \n    check: 'execution', \n    status: executionResult.status === 'completed' ? 'passed' : 'failed',\n    message: 'No code changes to validate'\n  });\n}\n\nconst passedChecks = qualityResult.qualityChecks.filter(c => c.status === 'passed').length;\nconst totalChecks = qualityResult.qualityChecks.length;\n\nconsole.log(`🎯 Quality Gate Result: ${passedChecks}/${totalChecks} checks passed`);\nconsole.log(`✅ Overall status: ${qualityResult.passed ? 'PASSED' : 'FAILED'}`);\n\nreturn qualityResult;"}, "id": "quality-gate", "name": "Quality Gate Checks", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [500, 300]}, {"parameters": {"url": "http://localhost:5678/webhook/task-completed", "sendBody": true, "specifyHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "bodyParameters": {"parameters": [{"name": "taskId", "value": "={{ $json.taskId }}"}, {"name": "executionResult", "value": "={{ $json.executionResult }}"}, {"name": "qualityResult", "value": "={{ $json }}"}, {"name": "completedAt", "value": "={{ $json.checkedAt }}"}, {"name": "source", "value": "autonomous-code-executor"}]}, "options": {"timeout": 10000}}, "id": "notify-completion", "name": "Notify Task Completion", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [700, 300]}, {"parameters": {"mode": "runOnceForAllItems", "jsCode": "// Execution Summary\nconst qualityResult = $json;\nconst executionResult = qualityResult.executionResult;\n\nconst summary = {\n  taskId: qualityResult.taskId,\n  executionStatus: executionResult.status,\n  qualityStatus: qualityResult.passed ? 'passed' : 'failed',\n  changesCount: executionResult.changes.length,\n  gitChangesCount: executionResult.gitChanges.length,\n  qualityChecksCount: qualityResult.qualityChecks.length,\n  duration: executionResult.duration,\n  completedAt: qualityResult.checkedAt,\n  agent: 'autonomous-code-executor'\n};\n\nconsole.log('🎉 AUTONOMOUS CODE EXECUTION COMPLETE!');\nconsole.log('=====================================');\nconsole.log(`📋 Task ID: ${summary.taskId}`);\nconsole.log(`⚡ Execution: ${summary.executionStatus}`);\nconsole.log(`🔍 Quality Gate: ${summary.qualityStatus}`);\nconsole.log(`📝 Changes Made: ${summary.changesCount}`);\nconsole.log(`📊 Git Changes: ${summary.gitChangesCount}`);\nconsole.log(`⏱️ Duration: ${summary.duration}ms`);\nconsole.log('');\nconsole.log('🚀 Real file changes have been made to the Beauty CRM codebase!');\nconsole.log('💡 Check git status to see the modifications');\n\nreturn summary;"}, "id": "execution-summary", "name": "Execution Summary", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 300]}], "connections": {"Execute Task Webhook": {"main": [[{"node": "Execute Code Changes", "type": "main", "index": 0}]]}, "Execute Code Changes": {"main": [[{"node": "Quality Gate Checks", "type": "main", "index": 0}]]}, "Quality Gate Checks": {"main": [[{"node": "Notify Task Completion", "type": "main", "index": 0}]]}, "Notify Task Completion": {"main": [[{"node": "Execution Summary", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "1", "meta": {"templateCredsSetupCompleted": true}, "id": "autonomous-code-executor", "tags": [{"createdAt": "2025-01-08T00:00:00.000Z", "updatedAt": "2025-01-08T00:00:00.000Z", "id": "sdlc-agent", "name": "SDLC Agent"}]}