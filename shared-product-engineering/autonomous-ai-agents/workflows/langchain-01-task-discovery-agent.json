{"name": "Lang<PERSON><PERSON><PERSON> 01 - Task Discovery Agent", "nodes": [{"parameters": {"rule": {"interval": [{"field": "cronExpression", "value": "0 */10 * * * *"}]}}, "id": "cron-trigger", "name": "Every 10 Minutes", "type": "n8n-nodes-base.cron", "typeVersion": 1, "position": [100, 300]}, {"parameters": {"mode": "runOnceForAllItems", "code": "// LangChain Task Discovery Agent\nimport { Tool } from 'langchain/tools';\nimport { ChatOpenAI } from 'langchain/chat_models/openai';\nimport { AgentExecutor, createReactAgent } from 'langchain/agents';\nimport { pull } from 'langchain/hub';\nconst fs = require('fs');\nconst path = require('path');\n\n// Custom Tool for extracting tasks from codebase\nclass TaskExtractionTool extends Tool {\n  name = 'extract_tasks_from_codebase';\n  description = 'Extracts TODO, FIXME, BUG, and other task comments from codebase files';\n  \n  async _call(input) {\n    const workspaceRoot = '/workspace';\n    const discoveredTasks = [];\n    \n    // Task patterns to discover\n    const taskPatterns = [\n      { pattern: /TODO:/gi, type: 'todo', priority: 'medium' },\n      { pattern: /FIXME:/gi, type: 'fixme', priority: 'high' },\n      { pattern: /BUG:/gi, type: 'bug', priority: 'high' },\n      { pattern: /HACK:/gi, type: 'hack', priority: 'medium' },\n      { pattern: /XXX:/gi, type: 'urgent', priority: 'high' },\n      { pattern: /NOTE:/gi, type: 'note', priority: 'low' },\n      { pattern: /OPTIMIZE:/gi, type: 'optimize', priority: 'medium' },\n      { pattern: /REFACTOR:/gi, type: 'refactor', priority: 'medium' },\n      { pattern: /DEPRECATED:/gi, type: 'deprecated', priority: 'high' },\n      { pattern: /SECURITY:/gi, type: 'security', priority: 'critical' }\n    ];\n    \n    // File extensions to scan\n    const scanExtensions = ['.ts', '.js', '.tsx', '.jsx', '.json', '.md', '.yml', '.yaml', '.prisma'];\n    const ignoreDirs = ['node_modules', 'dist', 'build', '.git', '.next', 'coverage'];\n    \n    function scanDirectory(dir, maxDepth = 4, currentDepth = 0) {\n      if (currentDepth > maxDepth) return;\n      \n      try {\n        const entries = fs.readdirSync(dir, { withFileTypes: true });\n        \n        for (const entry of entries) {\n          const fullPath = path.join(dir, entry.name);\n          \n          if (entry.isDirectory()) {\n            if (!entry.name.startsWith('.') && !ignoreDirs.includes(entry.name)) {\n              scanDirectory(fullPath, maxDepth, currentDepth + 1);\n            }\n          } else if (entry.isFile()) {\n            const ext = path.extname(entry.name);\n            if (scanExtensions.includes(ext)) {\n              scanFileForTasks(fullPath);\n            }\n          }\n        }\n      } catch (error) {\n        console.log(`Error scanning directory ${dir}: ${error.message}`);\n      }\n    }\n    \n    function scanFileForTasks(filePath) {\n      try {\n        const content = fs.readFileSync(filePath, 'utf8');\n        const lines = content.split('\\n');\n        \n        lines.forEach((line, index) => {\n          taskPatterns.forEach(({ pattern, type, priority }) => {\n            if (pattern.test(line)) {\n              const task = {\n                id: `task-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n                type: type,\n                priority: priority,\n                file: filePath.replace(workspaceRoot, '.'),\n                line: index + 1,\n                content: line.trim(),\n                description: extractTaskDescription(line),\n                discoveredAt: new Date().toISOString(),\n                status: 'discovered',\n                complexity: estimateComplexity(line),\n                sdlcPhase: determineSdlcPhase(filePath, line)\n              };\n              \n              discoveredTasks.push(task);\n            }\n          });\n        });\n      } catch (error) {\n        console.log(`Error scanning file ${filePath}: ${error.message}`);\n      }\n    }\n    \n    function extractTaskDescription(line) {\n      const markers = ['TODO:', 'FIXME:', 'BUG:', 'HACK:', 'XXX:', 'NOTE:', 'OPTIMIZE:', 'REFACTOR:', 'DEPRECATED:', 'SECURITY:'];\n      let description = line;\n      \n      markers.forEach(marker => {\n        if (line.includes(marker)) {\n          description = line.split(marker)[1]?.trim() || line;\n        }\n      });\n      \n      return description.replace(/^\\/\\/\\s*/, '').replace(/^\\/\\*\\s*/, '').replace(/\\*\\/\\s*$/, '').trim();\n    }\n    \n    function estimateComplexity(line) {\n      const lowerLine = line.toLowerCase();\n      \n      if (lowerLine.includes('refactor') || lowerLine.includes('rewrite') || lowerLine.includes('architecture')) {\n        return 5;\n      }\n      if (lowerLine.includes('optimize') || lowerLine.includes('performance') || lowerLine.includes('security')) {\n        return 4;\n      }\n      if (lowerLine.includes('implement') || lowerLine.includes('add') || lowerLine.includes('create')) {\n        return 3;\n      }\n      if (lowerLine.includes('fix') || lowerLine.includes('update') || lowerLine.includes('change')) {\n        return 2;\n      }\n      return 1;\n    }\n    \n    function determineSdlcPhase(filePath, line) {\n      const lowerPath = filePath.toLowerCase();\n      const lowerLine = line.toLowerCase();\n      \n      if (lowerPath.includes('test') || lowerLine.includes('test')) return 'testing';\n      if (lowerPath.includes('doc') || lowerLine.includes('document')) return 'documentation';\n      if (lowerLine.includes('deploy') || lowerLine.includes('build')) return 'deployment';\n      if (lowerLine.includes('design') || lowerLine.includes('architecture')) return 'design';\n      if (lowerLine.includes('requirement') || lowerLine.includes('spec')) return 'requirements';\n      return 'development';\n    }\n    \n    console.log('🔍 LangChain Task Discovery Agent - Scanning Beauty CRM codebase...');\n    scanDirectory(workspaceRoot);\n    \n    const tasksByType = {};\n    const tasksByPriority = {};\n    \n    discoveredTasks.forEach(task => {\n      tasksByType[task.type] = (tasksByType[task.type] || 0) + 1;\n      tasksByPriority[task.priority] = (tasksByPriority[task.priority] || 0) + 1;\n    });\n    \n    console.log(`📋 Discovered ${discoveredTasks.length} tasks`);\n    console.log('📊 By Type:', tasksByType);\n    console.log('🎯 By Priority:', tasksByPriority);\n    \n    return JSON.stringify({\n      totalTasks: discoveredTasks.length,\n      tasksByType,\n      tasksByPriority,\n      tasks: discoveredTasks.slice(0, 20), // Return first 20 tasks\n      discoveredAt: new Date().toISOString()\n    });\n  }\n}\n\n// Create LangChain Agent with Task Extraction Tool\nconst tools = [new TaskExtractionTool()];\n\n// Use Gemini AI as the LLM (if available) or fallback to a simple execution\nlet llm;\ntry {\n  // Try to use Gemini via OpenAI-compatible interface\n  llm = new ChatOpenAI({\n    modelName: 'gpt-3.5-turbo',\n    temperature: 0.1,\n    openAIApiKey: process.env.GEMINI_API_KEY || process.env.OPENAI_API_KEY\n  });\n} catch (error) {\n  console.log('⚠️ LLM not available, using direct tool execution');\n  llm = null;\n}\n\nif (llm) {\n  // Use full LangChain agent\n  const prompt = await pull('hwchase17/react');\n  const agent = await createReactAgent({ llm, tools, prompt });\n  const agentExecutor = new AgentExecutor({ agent, tools });\n  \n  const result = await agentExecutor.invoke({\n    input: 'Extract all tasks from the Beauty CRM codebase and provide a summary of discovered tasks by type and priority.'\n  });\n  \n  console.log('🤖 LangChain Agent Result:', result.output);\n  return JSON.parse(result.output);\n} else {\n  // Direct tool execution\n  const taskTool = new TaskExtractionTool();\n  const result = await taskTool._call('');\n  return JSON.parse(result);\n}"}, "id": "langchain-discovery", "name": "Lang<PERSON>hain Task Discovery", "type": "n8n-nodes-langchain.code", "typeVersion": 1, "position": [300, 300]}, {"parameters": {"url": "http://localhost:5678/webhook/tasks-discovered", "sendBody": true, "specifyHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "bodyParameters": {"parameters": [{"name": "discoveryResult", "value": "={{ $json }}"}, {"name": "totalTasks", "value": "={{ $json.totalTasks }}"}, {"name": "tasksByType", "value": "={{ $json.tasksByType }}"}, {"name": "tasksByPriority", "value": "={{ $json.tasksByPriority }}"}, {"name": "tasks", "value": "={{ $json.tasks }}"}, {"name": "source", "value": "langchain-task-discovery-agent"}]}}, "id": "notify-discovery", "name": "Notify Task Discovery", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [500, 300]}, {"parameters": {"mode": "runOnceForAllItems", "jsCode": "const discoveryResult = $json;\n\nconst summary = {\n  agent: 'langchain-task-discovery-agent',\n  discoveryComplete: true,\n  totalTasksFound: discoveryResult.totalTasks,\n  taskBreakdown: {\n    byType: discoveryResult.tasksByType,\n    byPriority: discoveryResult.tasksByPriority\n  },\n  discoveredAt: discoveryResult.discoveredAt,\n  nextDiscovery: new Date(Date.now() + 10 * 60 * 1000).toISOString(), // Next run in 10 minutes\n  tasksForAnalysis: discoveryResult.tasks.length\n};\n\nconsole.log('✅ LangChain Task Discovery Complete!');\nconsole.log(`📋 Total tasks discovered: ${summary.totalTasksFound}`);\nconsole.log(`🔄 Tasks sent for analysis: ${summary.tasksForAnalysis}`);\nconsole.log(`⏰ Next discovery: ${summary.nextDiscovery}`);\n\nreturn summary;"}, "id": "discovery-summary", "name": "Discovery Summary", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [700, 300]}], "connections": {"Every 10 Minutes": {"main": [[{"node": "Lang<PERSON>hain Task Discovery", "type": "main", "index": 0}]]}, "LangChain Task Discovery": {"main": [[{"node": "Notify Task Discovery", "type": "main", "index": 0}]]}, "Notify Task Discovery": {"main": [[{"node": "Discovery Summary", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "1", "meta": {"templateCredsSetupCompleted": true}, "id": "langchain-task-discovery-agent", "tags": [{"createdAt": "2025-01-08T00:00:00.000Z", "updatedAt": "2025-01-08T00:00:00.000Z", "id": "langchain-sdlc-agent", "name": "LangChain SDLC Agent"}]}