{"name": "02 - Gemini Task Analyzer", "nodes": [{"parameters": {}, "id": "webhook-trigger", "name": "Task Discovered Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [100, 300], "webhookId": "task-discovered"}, {"parameters": {"mode": "runOnceForEachItem", "jsCode": "// Prepare task for Gemini analysis\nconst task = $json;\n\nconsole.log(`🤖 Analyzing task: ${task.type} in ${task.file}:${task.line}`);\n\nconst analysisPrompt = `You are an expert software architect and developer working on the Beauty CRM project.\n\nANALYZE THIS TASK FOR AUTONOMOUS EXECUTION:\n\nTask Details:\n- Type: ${task.type}\n- Priority: ${task.priority}\n- File: ${task.file}\n- Line: ${task.line}\n- Content: ${task.content}\n- Description: ${task.description}\n- Current SDLC Phase: ${task.sdlcPhase}\n- Estimated Complexity: ${task.complexity}/5\n\nProject Context:\n- Beauty CRM system built with TypeScript, React, Node.js, Prisma\n- Microservices architecture with Docker\n- Uses n8n for workflow automation\n- Follows DDD patterns\n\nProvide analysis in this EXACT JSON format:\n{\n  \"taskId\": \"${task.id}\",\n  \"feasibilityScore\": 0.85,\n  \"riskLevel\": \"low\",\n  \"estimatedMinutes\": 15,\n  \"requiredSkills\": [\"typescript\", \"react\"],\n  \"dependencies\": [\"file1.ts\", \"service2\"],\n  \"automationStrategy\": \"direct-implementation\",\n  \"codeChanges\": {\n    \"filesToModify\": [\"${task.file}\"],\n    \"changeType\": \"modification\",\n    \"linesAffected\": 5\n  },\n  \"testingRequired\": true,\n  \"qualityChecks\": [\"typescript-check\", \"lint\"],\n  \"executionPlan\": [\n    \"Read current file content\",\n    \"Implement the required change\",\n    \"Update related documentation\",\n    \"Run quality checks\"\n  ],\n  \"recommendation\": \"proceed\",\n  \"reasoning\": \"Low risk change with clear implementation path\"\n}\n\nFeasibilityScore: 0.0-1.0 (confidence in autonomous execution)\nRiskLevel: low/medium/high\nAutomationStrategy: direct-implementation/guided-implementation/manual-review\nRecommendation: proceed/review/defer\n\nONLY return the JSON object, no other text.`;\n\nreturn {\n  taskId: task.id,\n  originalTask: task,\n  analysisPrompt: analysisPrompt,\n  timestamp: new Date().toISOString()\n};"}, "id": "prepare-analysis", "name": "Prepare for Gemini Analysis", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [300, 300]}, {"parameters": {"url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "x-goog-api-key", "value": "={{ $env.GEMINI_API_KEY }}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "contents", "value": "={{ [{\"parts\": [{\"text\": $json.analysisPrompt}]}] }}"}, {"name": "generationConfig", "value": {"temperature": 0.1, "maxOutputTokens": 2000, "topP": 0.9}}]}, "options": {"timeout": 30000}}, "id": "gemini-analysis", "name": "Gemini AI Analysis", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [500, 300]}, {"parameters": {"mode": "runOnceForEachItem", "jsCode": "// Parse Gemini response and create analyzed task\nconst originalData = $('Prepare for Gemini Analysis').item.json;\nconst geminiResponse = $json;\n\nconsole.log(`🧠 Processing Gemini analysis for task: ${originalData.taskId}`);\n\ntry {\n  // Extract JSON from Gemini response\n  let responseText = '';\n  if (geminiResponse.candidates && geminiResponse.candidates[0]) {\n    responseText = geminiResponse.candidates[0].content.parts[0].text;\n  } else {\n    throw new Error('Invalid Gemini response structure');\n  }\n  \n  // Clean and parse JSON\n  const jsonMatch = responseText.match(/{[\\s\\S]*}/); \n  if (!jsonMatch) {\n    throw new Error('No JSON found in Gemini response');\n  }\n  \n  const analysis = JSON.parse(jsonMatch[0]);\n  \n  // Validate analysis structure\n  const requiredFields = ['feasibilityScore', 'riskLevel', 'estimatedMinutes', 'recommendation'];\n  const missingFields = requiredFields.filter(field => !(field in analysis));\n  \n  if (missingFields.length > 0) {\n    throw new Error(`Missing required fields: ${missingFields.join(', ')}`);\n  }\n  \n  // Create analyzed task\n  const analyzedTask = {\n    ...originalData.originalTask,\n    analysis: analysis,\n    analyzedAt: new Date().toISOString(),\n    status: 'analyzed',\n    agent: 'gemini-task-analyzer'\n  };\n  \n  console.log(`✅ Analysis complete:`);\n  console.log(`   Feasibility: ${(analysis.feasibilityScore * 100).toFixed(1)}%`);\n  console.log(`   Risk Level: ${analysis.riskLevel}`);\n  console.log(`   Estimated Time: ${analysis.estimatedMinutes} minutes`);\n  console.log(`   Recommendation: ${analysis.recommendation}`);\n  \n  return analyzedTask;\n  \n} catch (error) {\n  console.error(`❌ Error parsing Gemini analysis: ${error.message}`);\n  \n  // Return task with error analysis\n  return {\n    ...originalData.originalTask,\n    analysis: {\n      feasibilityScore: 0.0,\n      riskLevel: 'high',\n      estimatedMinutes: 0,\n      recommendation: 'manual-review',\n      error: error.message,\n      reasoning: 'Failed to analyze with AI - requires manual review'\n    },\n    analyzedAt: new Date().toISOString(),\n    status: 'analysis-failed',\n    agent: 'gemini-task-analyzer'\n  };\n}"}, "id": "parse-analysis", "name": "Parse Gemini Analysis", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [700, 300]}, {"parameters": {"mode": "runOnceForEachItem", "jsCode": "// Route analyzed task based on recommendation\nconst analyzedTask = $json;\nconst analysis = analyzedTask.analysis;\n\nconsole.log(`🎯 Routing task ${analyzedTask.id} based on recommendation: ${analysis.recommendation}`);\n\nlet routingDecision = {\n  taskId: analyzedTask.id,\n  originalTask: analyzedTask,\n  routedAt: new Date().toISOString(),\n  agent: 'gemini-task-analyzer'\n};\n\n// Determine routing based on analysis\nif (analysis.recommendation === 'proceed' && analysis.feasibilityScore >= 0.7 && analysis.riskLevel === 'low') {\n  routingDecision.route = 'autonomous-execution';\n  routingDecision.webhook = 'http://localhost:5678/webhook/execute-task';\n  routingDecision.priority = 'high';\n  console.log(`✅ Routing to autonomous execution`);\n  \n} else if (analysis.recommendation === 'proceed' && analysis.feasibilityScore >= 0.5) {\n  routingDecision.route = 'guided-execution';\n  routingDecision.webhook = 'http://localhost:5678/webhook/guided-task';\n  routingDecision.priority = 'medium';\n  console.log(`⚡ Routing to guided execution`);\n  \n} else if (analysis.recommendation === 'review' || analysis.riskLevel === 'high') {\n  routingDecision.route = 'manual-review';\n  routingDecision.webhook = 'http://localhost:5678/webhook/manual-review';\n  routingDecision.priority = 'low';\n  console.log(`👀 Routing to manual review`);\n  \n} else {\n  routingDecision.route = 'deferred';\n  routingDecision.webhook = 'http://localhost:5678/webhook/deferred-task';\n  routingDecision.priority = 'low';\n  console.log(`⏸️ Deferring task`);\n}\n\nreturn routingDecision;"}, "id": "route-task", "name": "Route Analyzed Task", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 300]}, {"parameters": {"url": "={{ $json.webhook }}", "sendBody": true, "specifyHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "bodyParameters": {"parameters": [{"name": "task", "value": "={{ $json.originalTask }}"}, {"name": "route", "value": "={{ $json.route }}"}, {"name": "priority", "value": "={{ $json.priority }}"}, {"name": "routedAt", "value": "={{ $json.routedAt }}"}, {"name": "source", "value": "gemini-task-analyzer"}]}, "options": {"timeout": 10000}}, "id": "send-to-executor", "name": "Send to Task Executor", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [1100, 300]}], "connections": {"Task Discovered Webhook": {"main": [[{"node": "Prepare for Gemini Analysis", "type": "main", "index": 0}]]}, "Prepare for Gemini Analysis": {"main": [[{"node": "Gemini AI Analysis", "type": "main", "index": 0}]]}, "Gemini AI Analysis": {"main": [[{"node": "Parse Gemini Analysis", "type": "main", "index": 0}]]}, "Parse Gemini Analysis": {"main": [[{"node": "Route Analyzed Task", "type": "main", "index": 0}]]}, "Route Analyzed Task": {"main": [[{"node": "Send to Task Executor", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "1", "meta": {"templateCredsSetupCompleted": true}, "id": "gemini-task-analyzer", "tags": [{"createdAt": "2025-01-08T00:00:00.000Z", "updatedAt": "2025-01-08T00:00:00.000Z", "id": "sdlc-agent", "name": "SDLC Agent"}]}