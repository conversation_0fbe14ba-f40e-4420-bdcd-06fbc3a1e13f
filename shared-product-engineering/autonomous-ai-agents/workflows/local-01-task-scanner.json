{"name": "Local 01 - Task Scanner Agent", "nodes": [{"parameters": {"rule": {"interval": [{"field": "cronExpression", "value": "0 */5 * * * *"}]}}, "id": "cron-trigger", "name": "Every 5 Minutes", "type": "n8n-nodes-base.cron", "typeVersion": 1, "position": [100, 300]}, {"parameters": {"mode": "runOnceForAllItems", "code": "// Local Task Scanner Agent - Scans local files for tasks\nconst fs = require('fs');\nconst path = require('path');\n\nconst workspaceRoot = '/workspace';\nconst discoveredTasks = [];\n\n// Task patterns to discover\nconst taskPatterns = [\n  { pattern: /TODO:/gi, type: 'todo', priority: 'medium' },\n  { pattern: /FIXME:/gi, type: 'fixme', priority: 'high' },\n  { pattern: /BUG:/gi, type: 'bug', priority: 'high' },\n  { pattern: /HACK:/gi, type: 'hack', priority: 'medium' },\n  { pattern: /XXX:/gi, type: 'urgent', priority: 'high' },\n  { pattern: /NOTE:/gi, type: 'note', priority: 'low' },\n  { pattern: /OPTIMIZE:/gi, type: 'optimize', priority: 'medium' },\n  { pattern: /REFACTOR:/gi, type: 'refactor', priority: 'medium' },\n  { pattern: /DEPRECATED:/gi, type: 'deprecated', priority: 'high' },\n  { pattern: /SECURITY:/gi, type: 'security', priority: 'critical' }\n];\n\n// File extensions to scan\nconst scanExtensions = ['.ts', '.js', '.tsx', '.jsx', '.json', '.md', '.yml', '.yaml', '.prisma'];\nconst ignoreDirs = ['node_modules', 'dist', 'build', '.git', '.next', 'coverage', '.nyc_output'];\n\nfunction scanDirectory(dir, maxDepth = 4, currentDepth = 0) {\n  if (currentDepth > maxDepth) return;\n  \n  try {\n    const entries = fs.readdirSync(dir, { withFileTypes: true });\n    \n    for (const entry of entries) {\n      const fullPath = path.join(dir, entry.name);\n      \n      if (entry.isDirectory()) {\n        if (!entry.name.startsWith('.') && !ignoreDirs.includes(entry.name)) {\n          scanDirectory(fullPath, maxDepth, currentDepth + 1);\n        }\n      } else if (entry.isFile()) {\n        const ext = path.extname(entry.name);\n        if (scanExtensions.includes(ext)) {\n          scanFileForTasks(fullPath);\n        }\n      }\n    }\n  } catch (error) {\n    console.log(`Error scanning directory ${dir}: ${error.message}`);\n  }\n}\n\nfunction scanFileForTasks(filePath) {\n  try {\n    const content = fs.readFileSync(filePath, 'utf8');\n    const lines = content.split('\\n');\n    \n    lines.forEach((line, index) => {\n      taskPatterns.forEach(({ pattern, type, priority }) => {\n        if (pattern.test(line)) {\n          const task = {\n            id: `task-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n            type: type,\n            priority: priority,\n            file: filePath.replace(workspaceRoot, '.'),\n            line: index + 1,\n            content: line.trim(),\n            description: extractTaskDescription(line),\n            discoveredAt: new Date().toISOString(),\n            status: 'discovered',\n            complexity: estimateComplexity(line),\n            sdlcPhase: determineSdlcPhase(filePath, line),\n            fileSize: content.length,\n            lineCount: lines.length\n          };\n          \n          discoveredTasks.push(task);\n        }\n      });\n    });\n  } catch (error) {\n    console.log(`Error scanning file ${filePath}: ${error.message}`);\n  }\n}\n\nfunction extractTaskDescription(line) {\n  const markers = ['TODO:', 'FIXME:', 'BUG:', 'HACK:', 'XXX:', 'NOTE:', 'OPTIMIZE:', 'REFACTOR:', 'DEPRECATED:', 'SECURITY:'];\n  let description = line;\n  \n  markers.forEach(marker => {\n    if (line.includes(marker)) {\n      description = line.split(marker)[1]?.trim() || line;\n    }\n  });\n  \n  return description.replace(/^\\/\\/\\s*/, '').replace(/^\\/\\*\\s*/, '').replace(/\\*\\/\\s*$/, '').trim();\n}\n\nfunction estimateComplexity(line) {\n  const lowerLine = line.toLowerCase();\n  \n  if (lowerLine.includes('refactor') || lowerLine.includes('rewrite') || lowerLine.includes('architecture')) {\n    return 5;\n  }\n  if (lowerLine.includes('optimize') || lowerLine.includes('performance') || lowerLine.includes('security')) {\n    return 4;\n  }\n  if (lowerLine.includes('implement') || lowerLine.includes('add') || lowerLine.includes('create')) {\n    return 3;\n  }\n  if (lowerLine.includes('fix') || lowerLine.includes('update') || lowerLine.includes('change')) {\n    return 2;\n  }\n  return 1;\n}\n\nfunction determineSdlcPhase(filePath, line) {\n  const lowerPath = filePath.toLowerCase();\n  const lowerLine = line.toLowerCase();\n  \n  if (lowerPath.includes('test') || lowerLine.includes('test')) return 'testing';\n  if (lowerPath.includes('doc') || lowerLine.includes('document')) return 'documentation';\n  if (lowerLine.includes('deploy') || lowerLine.includes('build')) return 'deployment';\n  if (lowerLine.includes('design') || lowerLine.includes('architecture')) return 'design';\n  if (lowerLine.includes('requirement') || lowerLine.includes('spec')) return 'requirements';\n  return 'development';\n}\n\nconsole.log('🔍 Local Task Scanner - Scanning Beauty CRM codebase...');\nscanDirectory(workspaceRoot);\n\nconst tasksByType = {};\nconst tasksByPriority = {};\nconst tasksByPhase = {};\n\ndiscoveredTasks.forEach(task => {\n  tasksByType[task.type] = (tasksByType[task.type] || 0) + 1;\n  tasksByPriority[task.priority] = (tasksByPriority[task.priority] || 0) + 1;\n  tasksByPhase[task.sdlcPhase] = (tasksByPhase[task.sdlcPhase] || 0) + 1;\n});\n\nconsole.log(`📋 Discovered ${discoveredTasks.length} tasks`);\nconsole.log('📊 By Type:', tasksByType);\nconsole.log('🎯 By Priority:', tasksByPriority);\nconsole.log('🏗️ By SDLC Phase:', tasksByPhase);\n\n// Return results for next node\nreturn {\n  totalTasks: discoveredTasks.length,\n  tasksByType,\n  tasksByPriority,\n  tasksByPhase,\n  tasks: discoveredTasks,\n  discoveredAt: new Date().toISOString(),\n  agent: 'local-task-scanner'\n};"}, "id": "local-scanner", "name": "Local File Scanner", "type": "n8n-nodes-langchain.code", "typeVersion": 1, "position": [300, 300]}, {"parameters": {"url": "http://localhost:5678/webhook/tasks-found", "sendBody": true, "specifyHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "bodyParameters": {"parameters": [{"name": "scanResult", "value": "={{ $json }}"}, {"name": "totalTasks", "value": "={{ $json.totalTasks }}"}, {"name": "highPriorityTasks", "value": "={{ $json.tasks.filter(t => t.priority === 'high' || t.priority === 'critical').length }}"}, {"name": "source", "value": "local-task-scanner"}]}}, "id": "notify-tasks", "name": "Notify Tasks Found", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [500, 300]}, {"parameters": {"mode": "runOnceForAllItems", "jsCode": "const scanResult = $json.scanResult;\n\nconst summary = {\n  agent: 'local-task-scanner',\n  scanComplete: true,\n  totalTasksFound: scanResult.totalTasks,\n  taskBreakdown: {\n    byType: scanResult.tasksByType,\n    byPriority: scanResult.tasksByPriority,\n    byPhase: scanResult.tasksByPhase\n  },\n  scannedAt: scanResult.discoveredAt,\n  nextScan: new Date(Date.now() + 5 * 60 * 1000).toISOString(), // Next run in 5 minutes\n  tasksReadyForAnalysis: scanResult.tasks.length\n};\n\nconsole.log('✅ Local Task Scan Complete!');\nconsole.log(`📋 Total tasks found: ${summary.totalTasksFound}`);\nconsole.log(`🔄 Tasks ready for analysis: ${summary.tasksReadyForAnalysis}`);\nconsole.log(`⏰ Next scan: ${summary.nextScan}`);\n\nreturn summary;"}, "id": "scan-summary", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [700, 300]}], "connections": {"Every 5 Minutes": {"main": [[{"node": "Local File Scanner", "type": "main", "index": 0}]]}, "Local File Scanner": {"main": [[{"node": "Notify Tasks Found", "type": "main", "index": 0}]]}, "Notify Tasks Found": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "1", "meta": {"templateCredsSetupCompleted": true}, "id": "local-task-scanner", "tags": [{"createdAt": "2025-01-08T00:00:00.000Z", "updatedAt": "2025-01-08T00:00:00.000Z", "id": "local-sdlc-agent", "name": "Local SDLC Agent"}]}