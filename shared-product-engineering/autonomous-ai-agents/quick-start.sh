#!/bin/bash

# Quick Start SDLC Monster
# Simple script to start the autonomous development pipeline

set -e

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${BLUE}🚀 Quick Start SDLC Monster${NC}"
echo "================================"

# Check if we're in the right directory
if [[ ! -f "docker-compose.yml" ]]; then
    echo -e "${RED}❌ docker-compose.yml not found${NC}"
    echo "Please run from autonomous-ai-agents directory"
    exit 1
fi

# Set Gemini API key if not set
if [[ -z "$GEMINI_API_KEY" ]]; then
    echo -e "${YELLOW}⚠️  GEMINI_API_KEY not set${NC}"
    echo "Please export your Gemini API key:"
    echo "export GEMINI_API_KEY=your_api_key_here"
    echo ""
    echo "Or create .env file with:"
    echo "GEMINI_API_KEY=your_api_key_here"
    exit 1
fi

echo -e "${GREEN}✅ Gemini API key found${NC}"

# Create directories
mkdir -p data/n8n workflows credentials nodes

# Start services
echo -e "${BLUE}🔄 Starting services...${NC}"
docker compose down --remove-orphans 2>/dev/null || true
docker compose up -d

# Wait for n8n
echo -e "${YELLOW}⏳ Waiting for n8n...${NC}"
for i in {1..30}; do
    if curl -s http://localhost:5678/healthz > /dev/null 2>&1; then
        echo -e "${GREEN}✅ n8n is ready!${NC}"
        break
    fi
    if [[ $i -eq 30 ]]; then
        echo -e "${RED}❌ n8n failed to start${NC}"
        exit 1
    fi
    sleep 2
    echo -n "."
done

# Import local SDLC workflows
echo -e "${BLUE}📥 Importing local SDLC workflows...${NC}"
if [[ -f "import-local-workflows.js" ]]; then
    node import-local-workflows.js
    if [[ $? -eq 0 ]]; then
        echo -e "${GREEN}✅ Local SDLC workflows imported successfully!${NC}"
    else
        echo -e "${YELLOW}⚠️ Some workflows may have failed to import${NC}"
    fi
else
    echo -e "${YELLOW}⚠️ import-local-workflows.js not found${NC}"
fi

echo ""
echo -e "${GREEN}🎉 Local SDLC Monster is running!${NC}"
echo ""
echo -e "${BLUE}📊 Access Points:${NC}"
echo "  🔧 n8n Editor: http://localhost:5678"
echo "  📋 Executions: http://localhost:5678/executions"
echo "  🔍 Workflows: http://localhost:5678/workflows"
echo ""
echo -e "${BLUE}🤖 Active Local SDLC Agents:${NC}"
echo "  1. 🔍 Task Scanner - Scans files every 5 minutes"
echo "  2. 🧠 Gemini Analyzer - Analyzes tasks with AI"
echo "  3. 📝 File Executor - Makes real file changes"
echo "  4. ✅ Quality Gate - Runs TypeScript/ESLint"
echo "  5. 📊 Git Operations - Creates real commits"
echo ""
echo -e "${BLUE}🔍 What Happens Next:${NC}"
echo "  • Agents scan /workspace for TODO/FIXME/BUG comments"
echo "  • Gemini AI analyzes each task for feasibility"
echo "  • Low-risk tasks get executed automatically"
echo "  • Real file changes appear in git status"
echo "  • Quality checks ensure code integrity"
echo ""
echo -e "${YELLOW}💡 The workspace /workspace is your Beauty CRM codebase${NC}"
echo -e "${YELLOW}💡 All changes are real and show up in git status${NC}"
echo -e "${YELLOW}💡 Agents work locally with LangChain code nodes${NC}"
echo ""
echo -e "${GREEN}✨ Local autonomous development is now active!${NC}"
