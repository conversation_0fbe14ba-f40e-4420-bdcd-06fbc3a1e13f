#!/bin/bash

# SDLC Monster Setup Script
# Sets up the autonomous development pipeline with n8n and Gemini AI

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

echo -e "${BLUE}🏗️  SDLC Monster Setup${NC}"
echo -e "${BLUE}=====================================${NC}"
echo ""

# Check prerequisites
echo -e "${YELLOW}📋 Checking prerequisites...${NC}"

# Check Docker
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker is not installed${NC}"
    echo "Please install Docker: https://docs.docker.com/get-docker/"
    exit 1
fi

# Check Docker Compose
if ! docker compose version &> /dev/null; then
    echo -e "${RED}❌ Docker Compose is not available${NC}"
    echo "Please install Docker Compose v2"
    exit 1
fi

echo -e "${GREEN}✅ Docker and Docker Compose are available${NC}"

# Check environment file
if [[ ! -f "$SCRIPT_DIR/.env" ]]; then
    echo -e "${YELLOW}⚠️  .env file not found, creating from template...${NC}"
    cp "$SCRIPT_DIR/.env.example" "$SCRIPT_DIR/.env"
    echo -e "${RED}❗ Please edit .env file with your API keys before continuing${NC}"
    echo "Required keys:"
    echo "  - GEMINI_API_KEY"
    echo "  - AUGMENT_CODE_API_KEY (optional)"
    echo "  - GITHUB_TOKEN (for git operations)"
    echo ""
    read -p "Press Enter after updating .env file..."
fi

# Load environment variables
echo -e "${YELLOW}🔧 Loading environment variables...${NC}"
set -a
source "$SCRIPT_DIR/.env"
set +a

# Validate required environment variables
if [[ -z "$GEMINI_API_KEY" ]]; then
    echo -e "${RED}❌ GEMINI_API_KEY not found in .env file${NC}"
    echo "Please add your Gemini 2.5 API key to .env file:"
    echo "GEMINI_API_KEY=your_api_key_here"
    exit 1
fi

echo -e "${GREEN}✅ Environment variables loaded${NC}"

# Create necessary directories
echo -e "${YELLOW}📁 Creating directories...${NC}"
mkdir -p "$SCRIPT_DIR/workflows"
mkdir -p "$SCRIPT_DIR/credentials"
mkdir -p "$SCRIPT_DIR/nodes"
mkdir -p "$SCRIPT_DIR/data/n8n"
mkdir -p "$SCRIPT_DIR/data/postgres"
mkdir -p "$SCRIPT_DIR/data/redis"

# Set permissions for workspace
echo -e "${YELLOW}🔐 Setting workspace permissions...${NC}"
if [[ -n "$WORKSPACE_ROOT" && -d "$WORKSPACE_ROOT" ]]; then
    chmod -R 755 "$WORKSPACE_ROOT" || echo -e "${YELLOW}⚠️  Could not set workspace permissions${NC}"
else
    echo -e "${YELLOW}⚠️  WORKSPACE_ROOT not set or directory doesn't exist${NC}"
fi

# Start services
echo -e "${YELLOW}🚀 Starting SDLC Monster services...${NC}"
cd "$SCRIPT_DIR"

# Pull latest images
echo -e "${BLUE}📥 Pulling Docker images...${NC}"
docker compose pull

# Start services
echo -e "${BLUE}🔄 Starting services...${NC}"
docker compose up -d

# Wait for services to be ready
echo -e "${YELLOW}⏳ Waiting for services to start...${NC}"
sleep 10

# Check service health
echo -e "${YELLOW}🏥 Checking service health...${NC}"

# Check n8n
if curl -s http://localhost:5678/healthz > /dev/null; then
    echo -e "${GREEN}✅ n8n is running${NC}"
else
    echo -e "${RED}❌ n8n is not responding${NC}"
fi

# Check markdown analyzer
if curl -s http://localhost:3003/health > /dev/null; then
    echo -e "${GREEN}✅ Markdown Analyzer is running${NC}"
else
    echo -e "${YELLOW}⚠️  Markdown Analyzer is not responding${NC}"
fi

# Check supervisor UI
if curl -s http://localhost:3001/health > /dev/null; then
    echo -e "${GREEN}✅ Supervisor UI is running${NC}"
else
    echo -e "${YELLOW}⚠️  Supervisor UI is not responding${NC}"
fi

# Import workflows
echo -e "${YELLOW}📥 Importing SDLC Monster workflows...${NC}"
if [[ -f "$SCRIPT_DIR/import-workflows.cjs" ]]; then
    node "$SCRIPT_DIR/import-workflows.cjs" || echo -e "${YELLOW}⚠️  Could not import workflows automatically${NC}"
else
    echo -e "${YELLOW}⚠️  import-workflows.cjs not found${NC}"
fi

# Create SDLC agents
echo -e "${YELLOW}🤖 Creating SDLC agents...${NC}"
if [[ -f "$SCRIPT_DIR/create-sdlc-agents.cjs" ]]; then
    node "$SCRIPT_DIR/create-sdlc-agents.cjs" || echo -e "${YELLOW}⚠️  Could not create agents automatically${NC}"
else
    echo -e "${YELLOW}⚠️  create-sdlc-agents.cjs not found${NC}"
fi

# Activate workflows
echo -e "${YELLOW}⚡ Activating workflows...${NC}"
if [[ -f "$SCRIPT_DIR/activate-all-workflows.cjs" ]]; then
    node "$SCRIPT_DIR/activate-all-workflows.cjs" || echo -e "${YELLOW}⚠️  Could not activate workflows automatically${NC}"
else
    echo -e "${YELLOW}⚠️  activate-all-workflows.cjs not found${NC}"
fi

echo ""
echo -e "${GREEN}🎉 SDLC Monster Setup Complete!${NC}"
echo ""
echo -e "${BLUE}📊 Access Points:${NC}"
echo "  🔧 n8n Workflow Editor: http://localhost:5678"
echo "  📋 Supervisor UI: http://localhost:3001"
echo "  📈 Task Analytics: http://localhost:3003"
echo "  🔍 Quality Gate: http://localhost:3002"
echo ""
echo -e "${BLUE}🔑 Default Credentials:${NC}"
echo "  n8n: admin / secure_password_change_me"
echo ""
echo -e "${BLUE}📖 Next Steps:${NC}"
echo "  1. Open n8n UI and verify workflows are imported"
echo "  2. Check that SDLC Monster workflow is active"
echo "  3. Monitor execution in Supervisor UI"
echo "  4. Review logs: docker compose logs -f"
echo ""
echo -e "${YELLOW}💡 Tip: The SDLC Monster will start processing tasks automatically every 30 seconds${NC}"
echo ""

# Show running containers
echo -e "${BLUE}🐳 Running Containers:${NC}"
docker compose ps

echo ""
echo -e "${GREEN}✨ The SDLC Monster is now autonomous and ready to process your tasks!${NC}"
