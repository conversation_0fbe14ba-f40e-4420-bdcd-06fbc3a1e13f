<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Beauty CRM - AI Agent Supervisor</title>
    <script src="/socket.io/socket.io.js"></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            color: #333;
        }
        .header {
            background: #2c3e50;
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .emergency-stop {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
        }
        .emergency-stop:hover { background: #c0392b; }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
        }
        .panel {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .panel-header {
            background: #34495e;
            color: white;
            padding: 1rem;
            font-weight: bold;
        }
        .panel-content {
            padding: 1rem;
            max-height: 600px;
            overflow-y: auto;
        }
        .approval-item, .agent-item {
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 1rem;
            padding: 1rem;
        }
        .approval-item.high-risk { border-left: 4px solid #e74c3c; }
        .approval-item.medium-risk { border-left: 4px solid #f39c12; }
        .approval-item.low-risk { border-left: 4px solid #27ae60; }
        .confidence-bar {
            width: 100%;
            height: 8px;
            background: #ecf0f1;
            border-radius: 4px;
            overflow: hidden;
            margin: 0.5rem 0;
        }
        .confidence-fill {
            height: 100%;
            background: linear-gradient(90deg, #e74c3c 0%, #f39c12 50%, #27ae60 100%);
            transition: width 0.3s ease;
        }
        .actions {
            display: flex;
            gap: 0.5rem;
            margin-top: 1rem;
        }
        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
        }
        .btn-approve { background: #27ae60; color: white; }
        .btn-reject { background: #e74c3c; color: white; }
        .btn-approve:hover { background: #229954; }
        .btn-reject:hover { background: #c0392b; }
        .agent-status {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        .status-active { background: #d5f4e6; color: #27ae60; }
        .status-idle { background: #fef9e7; color: #f39c12; }
        .status-error { background: #fadbd8; color: #e74c3c; }
        .status-stopped { background: #eaecee; color: #7f8c8d; }
        .progress-bar {
            width: 100%;
            height: 6px;
            background: #ecf0f1;
            border-radius: 3px;
            overflow: hidden;
            margin: 0.5rem 0;
        }
        .progress-fill {
            height: 100%;
            background: #3498db;
            transition: width 0.3s ease;
        }
        .file-list {
            max-height: 100px;
            overflow-y: auto;
            background: #f8f9fa;
            padding: 0.5rem;
            border-radius: 4px;
            margin: 0.5rem 0;
        }
        .file-item {
            font-size: 0.8rem;
            color: #666;
            margin: 0.2rem 0;
        }
        .timestamp {
            font-size: 0.8rem;
            color: #7f8c8d;
            margin-top: 0.5rem;
        }
        .feedback-input {
            width: 100%;
            min-height: 60px;
            margin: 0.5rem 0;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            resize: vertical;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        .stat-card {
            background: white;
            padding: 1rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #2c3e50;
        }
        .stat-label {
            color: #7f8c8d;
            font-size: 0.9rem;
        }
        .no-items {
            text-align: center;
            color: #7f8c8d;
            padding: 2rem;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🤖 AI Agent Supervisor - Beauty CRM</h1>
        <button class="emergency-stop" onclick="emergencyStop()">🛑 EMERGENCY STOP</button>
    </div>

    <div class="stats">
        <div class="stat-card">
            <div class="stat-number" id="pending-count">0</div>
            <div class="stat-label">Pending Approvals</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" id="active-agents">0</div>
            <div class="stat-label">Active Agents</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" id="confidence-avg">0%</div>
            <div class="stat-label">Avg Confidence</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" id="high-risk-count">0</div>
            <div class="stat-label">High Risk Actions</div>
        </div>
    </div>

    <div class="container">
        <div class="panel">
            <div class="panel-header">⚠️ Pending Approvals</div>
            <div class="panel-content" id="approvals-list">
                <div class="no-items">No pending approvals</div>
            </div>
        </div>

        <div class="panel">
            <div class="panel-header">🤖 Agent Status</div>
            <div class="panel-content" id="agents-list">
                <div class="no-items">No active agents</div>
            </div>
        </div>
    </div>

    <script>
        const socket = io();
        let pendingApprovals = [];
        let agentActivities = [];

        // Socket event listeners
        socket.on('initial-state', (data) => {
            pendingApprovals = data.pendingApprovals || [];
            agentActivities = data.agentActivities || [];
            updateUI();
        });

        socket.on('new-approval-request', (approval) => {
            pendingApprovals.push(approval);
            updateUI();
            showNotification(`New approval request: ${approval.action}`, 'warning');
        });

        socket.on('approval-decided', (data) => {
            pendingApprovals = pendingApprovals.filter(a => a.id !== data.id);
            updateUI();
            showNotification(`Action ${data.decision}d`, 'success');
        });

        socket.on('agent-status-update', (agent) => {
            const index = agentActivities.findIndex(a => a.id === agent.id);
            if (index >= 0) {
                agentActivities[index] = agent;
            } else {
                agentActivities.push(agent);
            }
            updateUI();
        });

        socket.on('emergency-stop', (data) => {
            showNotification(`Emergency stop activated: ${data.reason}`, 'error');
            pendingApprovals = [];
            agentActivities = agentActivities.map(a => ({ ...a, status: 'stopped' }));
            updateUI();
        });

        // UI Update Functions
        function updateUI() {
            updateStats();
            updateApprovalsList();
            updateAgentsList();
        }

        function updateStats() {
            document.getElementById('pending-count').textContent = pendingApprovals.length;
            document.getElementById('active-agents').textContent = 
                agentActivities.filter(a => a.status === 'active').length;
            
            const avgConfidence = pendingApprovals.length > 0 
                ? Math.round(pendingApprovals.reduce((sum, a) => sum + (a.confidence || 0), 0) / pendingApprovals.length * 100)
                : 0;
            document.getElementById('confidence-avg').textContent = avgConfidence + '%';
            
            document.getElementById('high-risk-count').textContent = 
                pendingApprovals.filter(a => a.riskLevel === 'high').length;
        }

        function updateApprovalsList() {
            const container = document.getElementById('approvals-list');
            
            if (pendingApprovals.length === 0) {
                container.innerHTML = '<div class="no-items">No pending approvals</div>';
                return;
            }

            container.innerHTML = pendingApprovals.map(approval => `
                <div class="approval-item ${approval.riskLevel}-risk">
                    <h3>${approval.action}</h3>
                    <p>${approval.description}</p>
                    
                    <div class="confidence-bar">
                        <div class="confidence-fill" style="width: ${(approval.confidence || 0) * 100}%"></div>
                    </div>
                    <small>Confidence: ${Math.round((approval.confidence || 0) * 100)}%</small>
                    
                    ${approval.affectedFiles && approval.affectedFiles.length > 0 ? `
                        <div class="file-list">
                            <strong>Affected Files:</strong>
                            ${approval.affectedFiles.map(file => `<div class="file-item">${file}</div>`).join('')}
                        </div>
                    ` : ''}
                    
                    <textarea class="feedback-input" id="feedback-${approval.id}" 
                              placeholder="Optional feedback for the agent..."></textarea>
                    
                    <div class="actions">
                        <button class="btn btn-approve" onclick="approveAction('${approval.id}')">
                            ✅ Approve
                        </button>
                        <button class="btn btn-reject" onclick="rejectAction('${approval.id}')">
                            ❌ Reject
                        </button>
                    </div>
                    
                    <div class="timestamp">
                        Agent: ${approval.agentId} | Risk: ${approval.riskLevel} | 
                        ${new Date(approval.createdAt).toLocaleString()}
                    </div>
                </div>
            `).join('');
        }

        function updateAgentsList() {
            const container = document.getElementById('agents-list');
            
            if (agentActivities.length === 0) {
                container.innerHTML = '<div class="no-items">No active agents</div>';
                return;
            }

            container.innerHTML = agentActivities.map(agent => `
                <div class="agent-item">
                    <h3>${agent.id}</h3>
                    <span class="agent-status status-${agent.status}">${agent.status.toUpperCase()}</span>
                    
                    ${agent.currentTask ? `<p><strong>Current Task:</strong> ${agent.currentTask}</p>` : ''}
                    
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${agent.progress || 0}%"></div>
                    </div>
                    <small>Progress: ${Math.round(agent.progress || 0)}%</small>
                    
                    <div class="timestamp">
                        Last Activity: ${new Date(agent.lastActivity).toLocaleString()}
                    </div>
                </div>
            `).join('');
        }

        // Action Functions
        async function approveAction(approvalId) {
            const feedback = document.getElementById(`feedback-${approvalId}`).value;
            await makeDecision(approvalId, 'approve', feedback);
        }

        async function rejectAction(approvalId) {
            const feedback = document.getElementById(`feedback-${approvalId}`).value;
            await makeDecision(approvalId, 'reject', feedback);
        }

        async function makeDecision(approvalId, decision, feedback) {
            try {
                const response = await fetch(`/api/approvals/${approvalId}/decision`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ decision, feedback })
                });
                
                if (!response.ok) {
                    throw new Error('Failed to process decision');
                }
                
                showNotification(`Action ${decision}d successfully`, 'success');
            } catch (error) {
                showNotification(`Error: ${error.message}`, 'error');
            }
        }

        async function emergencyStop() {
            if (!confirm('Are you sure you want to stop all AI agents? This will halt all ongoing operations.')) {
                return;
            }
            
            const reason = prompt('Reason for emergency stop (optional):') || 'Manual emergency stop';
            
            try {
                const response = await fetch('/api/emergency-stop', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ reason })
                });
                
                if (!response.ok) {
                    throw new Error('Failed to initiate emergency stop');
                }
                
                showNotification('Emergency stop initiated', 'success');
            } catch (error) {
                showNotification(`Error: ${error.message}`, 'error');
            }
        }

        function showNotification(message, type) {
            // Simple notification system
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 1rem;
                border-radius: 4px;
                color: white;
                font-weight: bold;
                z-index: 1000;
                background: ${type === 'error' ? '#e74c3c' : type === 'warning' ? '#f39c12' : '#27ae60'};
            `;
            notification.textContent = message;
            document.body.appendChild(notification);
            
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 5000);
        }

        // Initialize
        updateUI();
    </script>
</body>
</html>
