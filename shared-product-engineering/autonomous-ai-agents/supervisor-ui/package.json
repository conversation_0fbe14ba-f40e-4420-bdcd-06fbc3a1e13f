{"name": "beauty-crm-ai-supervisor", "version": "1.0.0", "description": "Human supervision interface for autonomous AI coding agents", "main": "server.js", "scripts": {"dev": "node server.js", "start": "node server.js", "test": "echo \"No tests yet\" && exit 0"}, "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.4", "redis": "^4.6.10", "axios": "^1.6.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "multer": "^1.4.5-lts.1", "ws": "^8.14.2"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["ai", "automation", "supervision", "coding-agents", "n8n"], "author": "Beauty CRM Team", "license": "MIT"}