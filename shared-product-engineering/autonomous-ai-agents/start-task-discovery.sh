#!/bin/bash

# Beauty CRM Task Discovery System Startup Script
# Scans markdown files and creates tickets for AI agents

set -e

# Colors for output
RED='\033[0;31m'
YELLOW='\033[1;33m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
WORKSPACE_ROOT="/private/var/www/2025/ollamar1/beauty-crm"

echo -e "${BLUE}🔍 BEAUTY CRM TASK DISCOVERY SYSTEM 🔍${NC}"
echo -e "${YELLOW}Scanning markdown files for tasks and creating AI agent tickets${NC}"
echo ""

# Safety checks
echo -e "${YELLOW}Performing safety checks...${NC}"

# Check if we're in the right directory
if [[ ! -f "$SCRIPT_DIR/docker-compose.yml" ]]; then
    echo -e "${RED}Error: docker-compose.yml not found. Run from autonomous-ai-agents directory.${NC}"
    exit 1
fi

# Check if workspace exists
if [[ ! -d "$WORKSPACE_ROOT" ]]; then
    echo -e "${RED}Error: Workspace not found at $WORKSPACE_ROOT${NC}"
    exit 1
fi

# Check if .env file exists and load it
if [[ ! -f "$SCRIPT_DIR/.env" ]]; then
    echo -e "${YELLOW}Creating .env file from template...${NC}"
    cp "$SCRIPT_DIR/.env.example" "$SCRIPT_DIR/.env"
    echo -e "${RED}Please edit .env file with your API keys before continuing!${NC}"
    exit 1
fi

# Load environment variables from .env file
echo -e "${BLUE}Loading environment variables from .env file...${NC}"
set -a  # automatically export all variables
source "$SCRIPT_DIR/.env"
set +a  # stop automatically exporting

# Return to script directory
cd "$SCRIPT_DIR"

echo ""
echo -e "${BLUE}🚀 Starting Task Discovery System...${NC}"

# Start Docker services (only the ones needed for task discovery)
echo -e "${BLUE}Starting required services...${NC}"
docker compose up -d n8n-postgres redis n8n markdown-analyzer

# Wait for services to be ready
echo -e "${BLUE}Waiting for services to start...${NC}"
sleep 15

# Health checks
echo -e "${BLUE}Performing health checks...${NC}"

# Check n8n
if curl -s http://localhost:5678/health > /dev/null; then
    echo -e "${GREEN}✓ n8n is running${NC}"
else
    echo -e "${RED}✗ n8n health check failed${NC}"
fi

# Check markdown analyzer
if curl -s http://localhost:3003/health > /dev/null; then
    echo -e "${GREEN}✓ Markdown Analyzer is running${NC}"
else
    echo -e "${RED}✗ Markdown Analyzer health check failed${NC}"
fi

echo ""
echo -e "${GREEN}🎯 Task Discovery System Started!${NC}"
echo ""
echo -e "${BLUE}Access Points:${NC}"
echo -e "${GREEN}• Task Dashboard: http://tasks.beauty-crm.localhost${NC}"
echo -e "${GREEN}• Task Dashboard (direct): http://localhost:3003${NC}"
echo -e "${GREEN}• n8n Workflows: http://n8n.beauty-crm.localhost${NC}"
echo ""

# Initial scan
echo -e "${BLUE}🔍 Performing initial markdown scan...${NC}"
SCAN_RESULT=$(curl -s -X POST http://localhost:3003/api/tasks/scan)
echo -e "${GREEN}Initial scan completed${NC}"

# Show scan results
if command -v jq &> /dev/null; then
    echo -e "${BLUE}Scan Results:${NC}"
    echo "$SCAN_RESULT" | jq '.results'
else
    echo -e "${YELLOW}Install 'jq' to see formatted scan results${NC}"
fi

echo ""
echo -e "${YELLOW}What the system does:${NC}"
echo "📋 Scans all markdown files in your Beauty CRM project"
echo "🎯 Identifies tasks using patterns like:"
echo "   • [ ] Checkbox items"
echo "   • TODO: comments"
echo "   • FIXME: items"
echo "   • Action items"
echo "🤖 Creates tickets that AI agents can pick up"
echo "📊 Categorizes by priority, risk, and complexity"
echo "✅ Updates checkboxes when tasks are completed"
echo ""
echo -e "${YELLOW}Next Steps:${NC}"
echo "1. Open Task Dashboard to see discovered tasks"
echo "2. Import task-discovery-workflow.json into n8n"
echo "3. Configure AI model connections in n8n"
echo "4. Tasks will automatically be assigned to agents based on priority/confidence"
echo ""
echo -e "${BLUE}Task Patterns Recognized:${NC}"
echo "• [x] Completed checkbox items"
echo "• [ ] Pending checkbox items"
echo "• TODO: Something to do"
echo "• FIXME: Something to fix"
echo "• Action: Something to action"
echo "• Need to: Something needed"
echo "• Should: Something that should be done"
echo "• Must: Something that must be done"
echo ""
echo -e "${GREEN}System is now monitoring your markdown files for changes!${NC}"
echo -e "${YELLOW}Any new tasks added to README files will be automatically discovered.${NC}"

# Show live logs for a few seconds
echo ""
echo -e "${BLUE}Monitoring task discovery (Ctrl+C to exit monitoring)...${NC}"
echo "Press Ctrl+C to exit monitoring and continue with manual supervision."

# Show live logs for markdown analyzer
timeout 30 docker compose logs -f --tail=10 markdown-analyzer || true

echo ""
echo -e "${GREEN}Task Discovery System is running and monitoring!${NC}"
echo -e "${YELLOW}Check the Task Dashboard for discovered tasks.${NC}"
