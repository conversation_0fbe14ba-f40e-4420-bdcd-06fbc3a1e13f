# AI Model API Keys
GEMINI_API_KEY=your_gemini_2_5_api_key_here
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Augment Code Integration
AUGMENT_CODE_API_KEY=your_augment_code_api_key_here
AUGMENT_CODE_WORKSPACE_ID=beauty-crm-workspace

# n8n Configuration
N8N_ENCRYPTION_KEY=your_32_character_encryption_key_here
N8N_USER_MANAGEMENT_JWT_SECRET=your_jwt_secret_here

# Database Passwords (Change these!)
N8N_POSTGRES_PASSWORD=change_this_secure_password
REDIS_PASSWORD=change_this_redis_password

# GitHub Integration (for repository operations)
GITHUB_TOKEN=your_github_personal_access_token
GITHUB_REPO_OWNER=your_github_username
GITHUB_REPO_NAME=beauty-crm

# Slack Integration (for notifications and supervision)
SLACK_BOT_TOKEN=xoxb-your-slack-bot-token
SLACK_CHANNEL_ALERTS=#ai-agent-alerts
SLACK_CHANNEL_APPROVALS=#ai-agent-approvals

# Email Notifications
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
NOTIFICATION_EMAIL=<EMAIL>

# Security Settings
SUPERVISOR_AUTH_SECRET=your_supervisor_auth_secret
QUALITY_GATE_API_KEY=your_quality_gate_api_key

# Workspace Configuration
WORKSPACE_ROOT=/private/var/www/2025/ollamar1/beauty-crm
MAX_FILE_SIZE_MB=10
MAX_FILES_PER_OPERATION=50
ALLOWED_FILE_EXTENSIONS=.ts,.js,.tsx,.jsx,.json,.md,.yml,.yaml,.prisma

# AI Agent Behavior Settings
CONFIDENCE_THRESHOLD=0.1
MAX_RETRIES=3
HUMAN_APPROVAL_REQUIRED=true
AUTO_DEPLOY_ENABLED=false
SANDBOX_MODE=true

# Monitoring and Logging
LOG_LEVEL=debug
METRICS_ENABLED=true
AUDIT_LOG_RETENTION_DAYS=90
