#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const axios = require('axios');

// Configuration
const N8N_API_URL = process.env.N8N_API_URL || 'http://localhost:5678';
const WORKFLOWS_DIR = path.join(__dirname, 'workflows');

// Import workflow to n8n
async function importWorkflow(workflowFile) {
    try {
        console.log(`📥 Importing workflow: ${workflowFile}`);
        
        const workflowPath = path.join(WORKFLOWS_DIR, workflowFile);
        const workflowData = JSON.parse(fs.readFileSync(workflowPath, 'utf8'));
        
        // Import workflow
        const response = await axios.post(`${N8N_API_URL}/api/v1/workflows`, workflowData, {
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        if (response.status === 200 || response.status === 201) {
            console.log(`✅ Successfully imported: ${workflowFile}`);
            console.log(`   Workflow ID: ${response.data.id}`);
            console.log(`   Name: ${response.data.name}`);
            
            // Activate the workflow
            try {
                await axios.patch(`${N8N_API_URL}/api/v1/workflows/${response.data.id}/activate`, {
                    active: true
                });
                console.log(`🟢 Activated workflow: ${response.data.name}`);
            } catch (activateError) {
                console.log(`⚠️  Could not activate workflow: ${activateError.message}`);
            }
            
            return response.data;
        } else {
            throw new Error(`Unexpected response status: ${response.status}`);
        }
    } catch (error) {
        console.error(`❌ Failed to import ${workflowFile}:`, error.message);
        if (error.response) {
            console.error(`   Status: ${error.response.status}`);
            console.error(`   Data:`, error.response.data);
        }
        return null;
    }
}

// Check n8n connection
async function checkN8nConnection() {
    try {
        console.log('🔍 Checking n8n connection...');
        const response = await axios.get(`${N8N_API_URL}/api/v1/workflows`);
        console.log(`✅ Connected to n8n (${response.data.length} existing workflows)`);
        return true;
    } catch (error) {
        console.error('❌ Cannot connect to n8n:', error.message);
        console.error('   Make sure n8n is running on', N8N_API_URL);
        return false;
    }
}

// Create test agents
async function createTestAgents() {
    console.log('\n🤖 Creating test AI agents...');
    
    const testAgents = [
        {
            id: 'agent-intern-001',
            name: 'Alex (Documentation Intern)',
            specialization: 'documentation',
            experience: 'beginner',
            maxComplexity: 1
        },
        {
            id: 'agent-intern-002', 
            name: 'Sam (Frontend Intern)',
            specialization: 'frontend',
            experience: 'beginner',
            maxComplexity: 2
        },
        {
            id: 'agent-intern-003',
            name: 'Jordan (Backend Intern)', 
            specialization: 'backend',
            experience: 'intermediate',
            maxComplexity: 3
        }
    ];
    
    for (const agent of testAgents) {
        try {
            // Initialize agent in supervisor
            await axios.post('http://localhost:3001/webhook/agent-status', {
                agentId: agent.id,
                status: 'idle',
                currentTask: null,
                progress: 0,
                lastActivity: new Date().toISOString(),
                agentInfo: agent
            });
            
            console.log(`✅ Created agent: ${agent.name} (${agent.specialization})`);
        } catch (error) {
            console.log(`⚠️  Could not register agent ${agent.name}: ${error.message}`);
        }
    }
}

// Assign test tasks to agents
async function assignTestTasks() {
    console.log('\n📋 Assigning test tasks to agents...');
    
    const testTasks = [
        {
            agentId: 'agent-intern-001',
            task: 'Add documentation comment to README file',
            description: 'Simple documentation improvement task',
            priority: 'low',
            riskLevel: 'low',
            complexity: 1
        },
        {
            agentId: 'agent-intern-002',
            task: 'Consider adding loading state animations',
            description: 'UI improvement suggestion from markdown analysis',
            priority: 'low', 
            riskLevel: 'low',
            complexity: 1
        }
    ];
    
    for (const task of testTasks) {
        try {
            // Trigger task assignment via n8n webhook
            await axios.post(`${N8N_API_URL}/webhook/task-assignment`, task);
            console.log(`✅ Assigned task to ${task.agentId}: ${task.task}`);
        } catch (error) {
            console.log(`⚠️  Could not assign task: ${error.message}`);
        }
    }
}

// Main function
async function main() {
    console.log('🚀 Starting n8n workflow import and agent setup...\n');
    
    // Check n8n connection
    const connected = await checkN8nConnection();
    if (!connected) {
        console.log('\n❌ Cannot proceed without n8n connection');
        process.exit(1);
    }
    
    // Get workflow files
    const workflowFiles = fs.readdirSync(WORKFLOWS_DIR)
        .filter(file => file.endsWith('.json'));
    
    if (workflowFiles.length === 0) {
        console.log('⚠️  No workflow files found in', WORKFLOWS_DIR);
        return;
    }
    
    console.log(`\n📁 Found ${workflowFiles.length} workflow files:`);
    workflowFiles.forEach(file => console.log(`   - ${file}`));
    
    // Import workflows
    console.log('\n📥 Importing workflows...');
    const importedWorkflows = [];
    
    for (const workflowFile of workflowFiles) {
        const workflow = await importWorkflow(workflowFile);
        if (workflow) {
            importedWorkflows.push(workflow);
        }
        
        // Small delay between imports
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    console.log(`\n📊 Import Summary:`);
    console.log(`   ✅ Successfully imported: ${importedWorkflows.length}`);
    console.log(`   ❌ Failed: ${workflowFiles.length - importedWorkflows.length}`);
    
    if (importedWorkflows.length > 0) {
        console.log('\n🎯 Imported workflows:');
        importedWorkflows.forEach(wf => {
            console.log(`   - ${wf.name} (ID: ${wf.id})`);
        });
        
        // Create test agents and assign tasks
        await createTestAgents();
        
        // Wait a bit for agents to initialize
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        await assignTestTasks();
        
        console.log('\n🎉 Setup complete! Check the supervisor dashboard:');
        console.log('   📊 Supervisor: http://localhost:3001');
        console.log('   📋 Tasks: http://localhost:3003');
        console.log('   🔧 n8n: http://localhost:5678');
    }
}

// Run if called directly
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { importWorkflow, checkN8nConnection };
