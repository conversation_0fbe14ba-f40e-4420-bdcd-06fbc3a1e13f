{"name": "beauty-crm-quality-gate", "version": "1.0.0", "description": "Automated code quality validation for AI agents", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "jest", "lint": "eslint .", "format": "prettier --write ."}, "dependencies": {"express": "^4.18.2", "axios": "^1.6.2", "fs-extra": "^11.2.0", "glob": "^10.3.10", "simple-git": "^3.20.0", "eslint": "^8.55.0", "prettier": "^3.1.1", "typescript": "^5.3.3", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "jest": "^29.7.0", "helmet": "^7.1.0", "cors": "^2.8.5", "morgan": "^1.10.0", "dotenv": "^16.3.1", "chokidar": "^3.5.3", "semver": "^7.5.4", "yaml": "^2.3.4"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["quality-gate", "code-validation", "ai-agents", "automation"], "author": "Beauty CRM Team", "license": "MIT"}