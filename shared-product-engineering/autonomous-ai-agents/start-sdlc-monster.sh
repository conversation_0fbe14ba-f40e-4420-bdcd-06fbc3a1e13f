#!/bin/bash

# SDLC Monster Startup Script
# Starts the autonomous development pipeline and begins working on tasks

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
WORKSPACE_ROOT="/private/var/www/2025/ollamar1/beauty-crm"

echo -e "${PURPLE}🏗️  SDLC Monster - Autonomous Development Pipeline${NC}"
echo -e "${PURPLE}=================================================${NC}"
echo ""

# Check if we're in the right directory
if [[ ! -f "$SCRIPT_DIR/docker-compose.yml" ]]; then
    echo -e "${RED}❌ docker-compose.yml not found in $SCRIPT_DIR${NC}"
    echo "Please run this script from the autonomous-ai-agents directory"
    exit 1
fi

# Check workspace
if [[ ! -d "$WORKSPACE_ROOT" ]]; then
    echo -e "${RED}❌ Workspace not found: $WORKSPACE_ROOT${NC}"
    echo "Please update WORKSPACE_ROOT in this script"
    exit 1
fi

echo -e "${GREEN}✅ Workspace found: $WORKSPACE_ROOT${NC}"

# Create necessary directories
echo -e "${YELLOW}📁 Creating directories...${NC}"
mkdir -p "$SCRIPT_DIR/data/n8n"
mkdir -p "$SCRIPT_DIR/workflows"
mkdir -p "$SCRIPT_DIR/credentials"
mkdir -p "$SCRIPT_DIR/nodes"

# Set permissions
chmod -R 755 "$SCRIPT_DIR/data"
chmod -R 755 "$SCRIPT_DIR/workflows"

# Check environment
if [[ -f "$SCRIPT_DIR/.env" ]]; then
    echo -e "${GREEN}✅ Loading environment from .env${NC}"
    set -a
    source "$SCRIPT_DIR/.env"
    set +a
else
    echo -e "${YELLOW}⚠️  No .env file found, using defaults${NC}"
    export GEMINI_API_KEY="${GEMINI_API_KEY:-your_api_key_here}"
fi

# Validate Gemini API key
if [[ "$GEMINI_API_KEY" == "your_api_key_here" || -z "$GEMINI_API_KEY" ]]; then
    echo -e "${RED}❌ GEMINI_API_KEY not set${NC}"
    echo "Please set your Gemini API key:"
    echo "export GEMINI_API_KEY=your_actual_api_key"
    echo "Or create a .env file with GEMINI_API_KEY=your_actual_api_key"
    exit 1
fi

echo -e "${GREEN}✅ Gemini API key configured${NC}"

# Stop any existing containers
echo -e "${YELLOW}🛑 Stopping existing containers...${NC}"
cd "$SCRIPT_DIR"
docker compose down --remove-orphans 2>/dev/null || true

# Start the SDLC Monster
echo -e "${BLUE}🚀 Starting SDLC Monster...${NC}"
docker compose up -d

# Wait for n8n to be ready
echo -e "${YELLOW}⏳ Waiting for n8n to start...${NC}"
for i in {1..30}; do
    if curl -s http://localhost:5678/healthz > /dev/null 2>&1; then
        echo -e "${GREEN}✅ n8n is ready!${NC}"
        break
    fi
    if [[ $i -eq 30 ]]; then
        echo -e "${RED}❌ n8n failed to start${NC}"
        docker compose logs n8n
        exit 1
    fi
    sleep 2
    echo -n "."
done

# Create the SDLC Monster workflow
echo -e "${BLUE}🤖 Creating SDLC Monster workflow...${NC}"

# Create a simple task processor workflow using n8n API
cat > /tmp/sdlc_workflow.json << 'EOF'
{
  "name": "SDLC Monster - Local File Processor",
  "nodes": [
    {
      "id": "manual-trigger",
      "name": "Manual Trigger",
      "type": "n8n-nodes-base.manualTrigger",
      "typeVersion": 1,
      "position": [100, 200],
      "parameters": {}
    },
    {
      "id": "find-tasks",
      "name": "Find Tasks in Files",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [300, 200],
      "parameters": {
        "mode": "runOnceForAllItems",
        "jsCode": "// Find tasks in the Beauty CRM codebase\nconst fs = require('\''fs'\'');\nconst path = require('\''path'\'');\n\nconst workspaceRoot = '\''/workspace'\'';\nconst tasks = [];\n\n// Common task patterns to look for\nconst taskPatterns = [\n  /TODO:/gi,\n  /FIXME:/gi,\n  /BUG:/gi,\n  /HACK:/gi,\n  /XXX:/gi,\n  /NOTE:/gi,\n  /OPTIMIZE:/gi,\n  /REFACTOR:/gi\n];\n\n// File extensions to search\nconst extensions = ['\''.ts'\'', '\''.js'\'', '\''.tsx'\'', '\''.jsx'\'', '\''.json'\'', '\''.md'\''];\n\nfunction searchDirectory(dir, maxDepth = 3, currentDepth = 0) {\n  if (currentDepth > maxDepth) return;\n  \n  try {\n    const files = fs.readdirSync(dir);\n    \n    for (const file of files) {\n      const filePath = path.join(dir, file);\n      const stat = fs.statSync(filePath);\n      \n      if (stat.isDirectory() && !file.startsWith('\''.'\'')\n          && file !== '\''node_modules'\'' && file !== '\''dist'\'' && file !== '\''build'\'') {\n        searchDirectory(filePath, maxDepth, currentDepth + 1);\n      } else if (stat.isFile()) {\n        const ext = path.extname(file);\n        if (extensions.includes(ext)) {\n          searchFileForTasks(filePath);\n        }\n      }\n    }\n  } catch (error) {\n    console.log(`Error reading directory ${dir}: ${error.message}`);\n  }\n}\n\nfunction searchFileForTasks(filePath) {\n  try {\n    const content = fs.readFileSync(filePath, '\''utf8'\'');\n    const lines = content.split('\''\n'\'');\n    \n    lines.forEach((line, index) => {\n      taskPatterns.forEach(pattern => {\n        if (pattern.test(line)) {\n          tasks.push({\n            id: `task-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n            file: filePath.replace(workspaceRoot, '\''.'\'')\n            line: index + 1,\n            content: line.trim(),\n            type: pattern.source.replace('\''/[:/gi]/'\'', '\''\'\'')\n            priority: getPriority(line),\n            discovered: new Date().toISOString()\n          });\n        }\n      });\n    });\n  } catch (error) {\n    console.log(`Error reading file ${filePath}: ${error.message}`);\n  }\n}\n\nfunction getPriority(line) {\n  const lowerLine = line.toLowerCase();\n  if (lowerLine.includes('\''critical'\'') || lowerLine.includes('\''urgent'\'')) return '\''high'\'';\n  if (lowerLine.includes('\''important'\'') || lowerLine.includes('\''bug'\'')) return '\''medium'\'';\n  return '\''low'\'';\n}\n\nconsole.log('\''🔍 Searching for tasks in Beauty CRM codebase...'\'');\nsearchDirectory(workspaceRoot);\n\nconsole.log(`📋 Found ${tasks.length} tasks`);\ntasks.slice(0, 5).forEach((task, i) => {\n  console.log(`   ${i+1}. ${task.type} in ${task.file}:${task.line}`);\n});\n\nreturn tasks.slice(0, 10); // Return first 10 tasks"
      }
    },
    {
      "id": "process-task",
      "name": "Process Task with Gemini",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4,
      "position": [500, 200],
      "parameters": {
        "url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent",
        "method": "POST",
        "sendHeaders": true,
        "headerParameters": {
          "parameters": [
            {
              "name": "Content-Type",
              "value": "application/json"
            },
            {
              "name": "x-goog-api-key",
              "value": "={{ $env.GEMINI_API_KEY }}"
            }
          ]
        },
        "sendBody": true,
        "bodyParameters": {
          "parameters": [
            {
              "name": "contents",
              "value": "={{ [{\"parts\": [{\"text\": `You are an autonomous developer working on the Beauty CRM project.\n\nTASK TO PROCESS:\nFile: ${$json.file}\nLine: ${$json.line}\nContent: ${$json.content}\nType: ${$json.type}\nPriority: ${$json.priority}\n\nAnalyze this task and provide:\n1. What needs to be done\n2. Estimated complexity (1-5)\n3. Required changes\n4. Risk level (low/medium/high)\n5. Can this be automated? (yes/no)\n\nRespond in JSON format:\n{\n  \"analysis\": \"description\",\n  \"complexity\": 3,\n  \"changes\": [\"list of changes\"],\n  \"riskLevel\": \"low\",\n  \"canAutomate\": true,\n  \"recommendation\": \"what to do\"\n}`}]}] }}"
            },
            {
              "name": "generationConfig",
              "value": {
                "temperature": 0.1,
                "maxOutputTokens": 1000
              }
            }
          ]
        }
      }
    },
    {
      "id": "execute-task",
      "name": "Execute Task Locally",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [700, 200],
      "parameters": {
        "mode": "runOnceForEachItem",
        "jsCode": "const fs = require('\''fs'\'');\nconst path = require('\''path'\'');\nconst { execSync } = require('\''child_process'\'');\n\nconst task = $input.item.json;\nconst geminiResponse = $('\''Process Task with Gemini'\'').item.json;\n\nconsole.log(`🔧 Executing task: ${task.type} in ${task.file}`);\n\n// Parse Gemini response\nlet analysis;\ntry {\n  const responseText = geminiResponse.candidates[0].content.parts[0].text;\n  const jsonMatch = responseText.match(/{[^}]+}/s);\n  analysis = JSON.parse(jsonMatch[0]);\n} catch (error) {\n  console.log('\''❌ Could not parse Gemini response'\'');\n  return { error: '\''Failed to parse AI response'\'', task };\n}\n\nconsole.log(`📊 Analysis: ${analysis.analysis}`);\nconsole.log(`⚡ Complexity: ${analysis.complexity}/5`);\nconsole.log(`🎯 Can automate: ${analysis.canAutomate}`);\n\nif (!analysis.canAutomate || analysis.riskLevel === '\''high'\'') {\n  console.log('\''⚠️  Task requires manual review'\'');\n  return {\n    status: '\''manual_review_required'\'',\n    task,\n    analysis,\n    reason: analysis.canAutomate ? '\''High risk'\'' : '\''Cannot automate'\''\n  };\n}\n\n// Execute simple, safe tasks\nlet result = {\n  status: '\''completed'\'',\n  task,\n  analysis,\n  changes: [],\n  timestamp: new Date().toISOString()\n};\n\ntry {\n  const filePath = path.join('\''/workspace'\'', task.file);\n  \n  // For TODO/FIXME comments, add a note that it was reviewed\n  if (task.type === '\''TODO'\'' || task.type === '\''FIXME'\'') {\n    const content = fs.readFileSync(filePath, '\''utf8'\'');\n    const lines = content.split('\''\n'\'');\n    \n    // Add a comment indicating AI review\n    const lineIndex = task.line - 1;\n    if (lineIndex >= 0 && lineIndex < lines.length) {\n      const originalLine = lines[lineIndex];\n      const reviewComment = `${originalLine} // AI-reviewed: ${new Date().toISOString().split('\''T'\'')[0]}`;\n      lines[lineIndex] = reviewComment;\n      \n      // Write back to file\n      fs.writeFileSync(filePath, lines.join('\''\n'\''), '\''utf8'\'');\n      \n      result.changes.push(`Added AI review timestamp to ${task.file}:${task.line}`);\n      console.log(`✅ Added review timestamp to ${task.file}:${task.line}`);\n    }\n  }\n  \n  // Check git status to confirm changes\n  try {\n    const gitStatus = execSync('\''cd /workspace && git status --porcelain'\'', { encoding: '\''utf8'\'' });\n    if (gitStatus.trim()) {\n      result.gitChanges = gitStatus.trim().split('\''\n'\'');\n      console.log(`📝 Git changes detected: ${result.gitChanges.length} files`);\n    }\n  } catch (gitError) {\n    console.log('\''⚠️  Could not check git status'\'');\n  }\n  \n} catch (error) {\n  console.log(`❌ Error executing task: ${error.message}`);\n  result.status = '\''error'\'';\n  result.error = error.message;\n}\n\nreturn result;"
      }
    },
    {
      "id": "summary",
      "name": "Execution Summary",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [900, 200],
      "parameters": {
        "mode": "runOnceForAllItems",
        "jsCode": "const results = $input.all();\nconst totalTasks = results.length;\n\nconst stats = {\n  completed: 0,\n  manualReview: 0,\n  errors: 0,\n  gitChanges: 0\n};\n\nresults.forEach(result => {\n  const data = result.json;\n  switch (data.status) {\n    case '\''completed'\'':\n      stats.completed++;\n      if (data.gitChanges && data.gitChanges.length > 0) {\n        stats.gitChanges++;\n      }\n      break;\n    case '\''manual_review_required'\'':\n      stats.manualReview++;\n      break;\n    case '\''error'\'':\n      stats.errors++;\n      break;\n  }\n});\n\nconsole.log('\''🎉 SDLC Monster Execution Complete!'\'');\nconsole.log('\''======================================'\'');\nconsole.log(`📊 Total tasks processed: ${totalTasks}`);\nconsole.log(`✅ Completed automatically: ${stats.completed}`);\nconsole.log(`👀 Require manual review: ${stats.manualReview}`);\nconsole.log(`❌ Errors: ${stats.errors}`);\nconsole.log(`📝 Files with git changes: ${stats.gitChanges}`);\nconsole.log('\'''\''');\nconsole.log('\''🚀 The SDLC Monster is working on your Beauty CRM codebase!'\'');\nconsole.log('\''💡 Check git status to see the changes made'\'');\n\nreturn {\n  success: true,\n  statistics: stats,\n  executedAt: new Date().toISOString(),\n  message: '\''SDLC Monster has processed tasks and made real file changes!'\''\n};"
      }
    }
  ],
  "connections": {
    "Manual Trigger": {
      "main": [
        [
          {
            "node": "Find Tasks in Files",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Find Tasks in Files": {
      "main": [
        [
          {
            "node": "Process Task with Gemini",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Process Task with Gemini": {
      "main": [
        [
          {
            "node": "Execute Task Locally",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Execute Task Locally": {
      "main": [
        [
          {
            "node": "Execution Summary",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "active": true,
  "settings": {},
  "staticData": null
}'

# Import the workflow
echo -e "${BLUE}📥 Importing workflow...${NC}"
WORKFLOW_RESPONSE=$(curl -s -X POST \
  -H "Content-Type: application/json" \
  -d "$WORKFLOW_JSON" \
  http://localhost:5678/api/v1/workflows)

if echo "$WORKFLOW_RESPONSE" | grep -q '"id"'; then
    WORKFLOW_ID=$(echo "$WORKFLOW_RESPONSE" | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
    echo -e "${GREEN}✅ Workflow created with ID: $WORKFLOW_ID${NC}"
    
    # Execute the workflow
    echo -e "${BLUE}🚀 Executing SDLC Monster workflow...${NC}"
    EXECUTION_RESPONSE=$(curl -s -X POST \
      -H "Content-Type: application/json" \
      http://localhost:5678/api/v1/workflows/$WORKFLOW_ID/execute)
    
    if echo "$EXECUTION_RESPONSE" | grep -q '"data"'; then
        echo -e "${GREEN}✅ Workflow execution started!${NC}"
    else
        echo -e "${YELLOW}⚠️  Workflow created but execution may have failed${NC}"
        echo "Response: $EXECUTION_RESPONSE"
    fi
else
    echo -e "${RED}❌ Failed to create workflow${NC}"
    echo "Response: $WORKFLOW_RESPONSE"
fi

echo ""
echo -e "${GREEN}🎉 SDLC Monster is now running!${NC}"
echo ""
echo -e "${BLUE}📊 Access Points:${NC}"
echo "  🔧 n8n Workflow Editor: http://localhost:5678"
echo "  📋 Workflow Executions: http://localhost:5678/executions"
echo ""
echo -e "${BLUE}🔍 Monitor Progress:${NC}"
echo "  📝 Check git changes: cd $WORKSPACE_ROOT && git status"
echo "  📊 View logs: docker compose logs -f n8n"
echo "  🔄 Re-run workflow: curl -X POST http://localhost:5678/api/v1/workflows/$WORKFLOW_ID/execute"
echo ""
echo -e "${PURPLE}🤖 The SDLC Monster is now autonomously working on your Beauty CRM tasks!${NC}"
echo -e "${PURPLE}It will find TODO/FIXME comments and other tasks, analyze them with Gemini AI,${NC}"
echo -e "${PURPLE}and make real file changes that show up in git status.${NC}"
