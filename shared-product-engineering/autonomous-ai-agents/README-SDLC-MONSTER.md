# SDLC Monster - Autonomous Development Pipeline

## 🎯 Overview

The SDLC Monster is an autonomous development pipeline that uses n8n workflows with Gemini AI to automatically process, analyze, and execute development tasks across the entire Software Development Life Cycle (SDLC).

## 🏗️ Architecture

### Core Components

1. **Priority Queue System** - Fetches high-priority tasks every 30 seconds
2. **Gemini AI Analysis** - Uses Gemini 2.0 Flash for intelligent task analysis
3. **SDLC Phase Router** - Routes tasks to specialized workflows based on SDLC phase
4. **Execution Tracking** - Monitors progress and provides real-time feedback
5. **Git Integration** - Makes real file changes that show up in git status

### Workflow Integration

```mermaid
graph TD
    A[Priority Queue Trigger] --> B[Fetch High Priority Tasks]
    B --> C[Gemini AI Analysis]
    C --> D[Parse & Filter Tasks]
    D --> E[Execute SDLC Task]
    E --> F[Route to Specialist]
    F --> G[Track Execution]
    G --> H[Update Task Status]
    H --> I[SDLC Monster Summary]
    
    F --> J[Autonomous Coding Workflow]
    F --> K[Autonomous Testing Workflow]
    F --> L[Autonomous Documentation Workflow]
    F --> M[Autonomous Refactoring Workflow]
```

## 🔧 Requirements

### Local File Operations Setup

The SDLC Monster works **locally** on your actual files, making real changes that show up in `git status`. No remote APIs needed for file operations.

### Environment Variables

```bash
# Required API Keys
GEMINI_API_KEY=your_gemini_2_5_api_key_here

# Local Workspace Configuration (CRITICAL)
WORKSPACE_ROOT=/private/var/www/2025/ollamar1/beauty-crm
N8N_FILES_MODE=local
N8N_SECURE_COOKIE=false
N8N_HOST=0.0.0.0

# File Operation Settings
MAX_FILE_SIZE_MB=10
MAX_FILES_PER_OPERATION=50
ALLOWED_FILE_EXTENSIONS=.ts,.js,.tsx,.jsx,.json,.md,.yml,.yaml,.prisma
BACKUP_BEFORE_CHANGES=true

# Local Git Configuration
GIT_AUTO_COMMIT=false
GIT_AUTO_PUSH=false
GIT_BRANCH_PREFIX=sdlc-monster/

# n8n Local Configuration
N8N_ENCRYPTION_KEY=local_development_key_32_chars
N8N_USER_MANAGEMENT_DISABLED=true
N8N_DIAGNOSTICS_ENABLED=false
N8N_VERSION_NOTIFICATIONS_ENABLED=false
N8N_TEMPLATES_ENABLED=false
N8N_ONBOARDING_FLOW_DISABLED=true

# Local Database (SQLite for simplicity)
DB_TYPE=sqlite
DB_SQLITE_DATABASE=/data/database.sqlite

# Task Processing Configuration
TASK_BATCH_SIZE=5
TASK_PROCESSING_INTERVAL=30
AUTO_START_WORKFLOWS=true
ENABLE_QUALITY_CHECKS=true
```

### Local Docker Configuration

```yaml
services:
  n8n:
    image: n8nio/n8n:latest
    environment:
      - N8N_HOST=0.0.0.0
      - N8N_PORT=5678
      - N8N_PROTOCOL=http
      - N8N_USER_MANAGEMENT_DISABLED=true
      - N8N_DIAGNOSTICS_ENABLED=false
      - N8N_VERSION_NOTIFICATIONS_ENABLED=false
      - N8N_TEMPLATES_ENABLED=false
      - N8N_ONBOARDING_FLOW_DISABLED=true
      - N8N_ENCRYPTION_KEY=local_development_key_32_chars
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - WORKSPACE_ROOT=/workspace
      - DB_TYPE=sqlite
      - DB_SQLITE_DATABASE=/data/database.sqlite
    volumes:
      - ${WORKSPACE_ROOT}:/workspace:rw
      - ./data/n8n:/data:rw
      - ./workflows:/home/<USER>/.n8n/workflows:rw
      - ./credentials:/home/<USER>/.n8n/credentials:rw
    ports:
      - "5678:5678"
    user: "1000:1000"
    working_dir: /workspace
    command: ["n8n", "start"]

  task-processor:
    build:
      context: .
      dockerfile: Dockerfile.task-processor
    environment:
      - WORKSPACE_ROOT=/workspace
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - N8N_WEBHOOK_URL=http://n8n:5678/webhook
    volumes:
      - ${WORKSPACE_ROOT}:/workspace:rw
    depends_on:
      - n8n
```

## 🚀 SDLC Monster Workflow Specifications

### 1. Priority Queue Trigger
- **Type**: Cron trigger
- **Schedule**: Every 30 seconds (`*/30 * * * * *`)
- **Purpose**: Continuously fetch high-priority tasks

### 2. Gemini AI Task Analysis
- **Model**: `gemini-2.0-flash-exp`
- **Temperature**: 0.1 (conservative)
- **Max Tokens**: 4000
- **Input**: Task array with metadata
- **Output**: Structured JSON analysis

#### Gemini Analysis Schema
```json
{
  "taskId": "string",
  "sdlcPhase": "requirements|design|development|testing|deployment|maintenance",
  "workType": "code|documentation|testing|refactoring|configuration",
  "complexity": 1-5,
  "autonomousFeasibility": 0.0-1.0,
  "requiredTools": ["file-editing", "git", "testing", "deployment"],
  "dependencies": ["array of dependencies"],
  "riskLevel": "low|medium|high",
  "estimatedMinutes": "number",
  "executionPlan": "step by step plan",
  "priority": "critical|high|medium|low"
}
```

### 3. Task Filtering Criteria
- **Minimum Feasibility**: 0.6 (60% confidence)
- **Maximum Risk**: medium (no high-risk tasks)
- **Maximum Complexity**: 4 (out of 5)
- **Priority Order**: critical > high > medium > low

### 4. Specialist Workflow Routing

#### Development Tasks → Autonomous Coding Workflow
- **Endpoint**: `http://localhost:5678/webhook/coding-request`
- **Handles**: Code implementation, bug fixes, feature development
- **Tools**: MCP file editing, git operations, code analysis

#### Testing Tasks → Autonomous Testing Workflow
- **Endpoint**: `http://localhost:5678/webhook/testing-request`
- **Handles**: Unit tests, integration tests, test automation
- **Tools**: Test runners, coverage analysis, assertion libraries

#### Documentation Tasks → Autonomous Documentation Workflow
- **Endpoint**: `http://localhost:5678/webhook/docs-request`
- **Handles**: README updates, API docs, code comments
- **Tools**: Markdown editing, documentation generators

#### Refactoring Tasks → Autonomous Refactoring Workflow
- **Endpoint**: `http://localhost:5678/webhook/refactor-request`
- **Handles**: Code cleanup, optimization, restructuring
- **Tools**: AST analysis, code quality metrics, refactoring tools

## 🔄 Integration with Existing Workflows

### Task Discovery Workflow Integration
- **Source**: `task-discovery-workflow.json`
- **Connection**: Feeds discovered tasks to SDLC Monster
- **Webhook**: `http://localhost:5678/webhook/new-task-discovered`

### Autonomous Coding Workflow Integration
- **Source**: `autonomous-coding-workflow.json`
- **Connection**: Receives routed development tasks
- **Features**: Gemini code generation, quality gate validation

## 📊 Execution Tracking & Monitoring

### Real-time Metrics
- Tasks processed per minute
- Success/failure rates by SDLC phase
- Average execution time by complexity
- Autonomous feasibility trends
- Git changes made per task

### Supervisor UI Integration
- **Endpoint**: `http://supervisor-ui:3001/webhook/execution-tracking`
- **Features**: Real-time dashboard, approval workflows, manual overrides

### Quality Gate Integration
- **Endpoint**: `http://quality-gate:3002/webhook/pre-commit`
- **Features**: Code quality validation, automated testing, security checks

## 🎯 Key Features

### 1. Priority-Based Queue Processing
- Fetches tasks by priority (critical → high → medium → low)
- Processes 5 tasks per cycle (every 30 seconds)
- Maintains execution order based on business value

### 2. Intelligent Task Analysis
- Uses Gemini AI for sophisticated task understanding
- Considers complexity, risk, and feasibility
- Provides detailed execution plans

### 3. SDLC Phase Awareness
- Routes tasks to appropriate specialist workflows
- Maintains SDLC best practices
- Ensures proper phase dependencies

### 4. Real Git Changes
- Makes actual file modifications
- Shows up in `git status` and `git diff`
- Integrates with existing development workflow

### 5. Autonomous Execution
- Minimal human intervention required
- Self-healing and error recovery
- Continuous learning and improvement

## 🚦 Getting Started

1. **Setup Environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your API keys
   ```

2. **Start Services**:
   ```bash
   docker compose up -d
   ```

3. **Import Workflows**:
   ```bash
   node import-workflows.cjs
   ```

4. **Activate SDLC Monster**:
   ```bash
   node activate-all-workflows.cjs
   ```

5. **Monitor Execution**:
   - n8n UI: http://localhost:5678
   - Supervisor UI: http://localhost:3001
   - Task Analytics: http://localhost:3003

## 🔍 Troubleshooting

### Common Issues
- **No git changes**: Check file permissions and workspace mounting
- **Gemini API errors**: Verify API key and rate limits
- **Task routing failures**: Check specialist workflow endpoints
- **Quality gate failures**: Review code quality standards

### Debug Commands
```bash
# Check n8n logs
docker logs beauty-crm-n8n

# Check task queue
curl http://localhost:3003/api/tasks?completed=false&priority=high

# Check execution status
curl http://localhost:3001/api/executions
```

## 📈 Performance Optimization

- **Batch Processing**: Process multiple tasks per cycle
- **Caching**: Cache Gemini responses for similar tasks
- **Parallel Execution**: Run specialist workflows concurrently
- **Resource Management**: Monitor CPU/memory usage

## 🔒 Security Considerations

- API key rotation and secure storage
- Workspace access controls
- Code execution sandboxing
- Audit logging for all changes

## 🛠️ Technical Implementation Details

### n8n MCP Integration

The SDLC Monster leverages n8n's Model Context Protocol (MCP) for advanced AI capabilities:

```javascript
// MCP File Editing Node Configuration
{
  "type": "n8n-nodes-base.httpRequest",
  "parameters": {
    "url": "http://localhost:3001/mcp",
    "method": "POST",
    "sendBody": true,
    "bodyParameters": {
      "parameters": [
        {
          "name": "method",
          "value": "tools/call"
        },
        {
          "name": "params",
          "value": "={{ JSON.stringify({name: 'str-replace-editor', arguments: {command: 'str_replace', path: $json.filePath, instruction_reminder: 'ALWAYS BREAK DOWN EDITS INTO SMALLER CHUNKS OF AT MOST 150 LINES EACH.', old_str: $json.oldContent, new_str: $json.newContent, old_str_start_line_number: $json.startLine, old_str_end_line_number: $json.endLine}}) }}"
        }
      ]
    }
  }
}
```

### Gemini AI Prompt Engineering

#### Task Analysis Prompt Template
```
You are the SDLC Monster - an autonomous development pipeline AI.

ANALYZE THESE TASKS FOR AUTONOMOUS EXECUTION:
${JSON.stringify(tasks, null, 2)}

For each task, determine:
1. SDLC Phase: (requirements, design, development, testing, deployment, maintenance)
2. Work Type: (code, documentation, testing, refactoring, configuration)
3. Complexity: (1-5 scale)
4. Autonomous Feasibility: (0.0-1.0 confidence)
5. Required Tools: (file-editing, git, testing, deployment)
6. Dependencies: (other tasks, files, services)
7. Risk Level: (low, medium, high)
8. Estimated Time: (minutes)

Return JSON array with structured analysis.
```

#### Code Generation Prompt Template
```
You are an expert ${specialization} working on the Beauty CRM project.

TASK: ${task}
SDLC PHASE: ${sdlcPhase}
COMPLEXITY: ${complexity}/5
EXECUTION PLAN: ${executionPlan}

CONTEXT:
- Project uses TypeScript, React, Node.js, Prisma
- Follow existing code patterns and conventions
- Make minimal, focused changes
- Ensure type safety and error handling

Generate production-ready code that:
1. Follows project conventions
2. Includes proper error handling
3. Has appropriate TypeScript types
4. Includes relevant comments
5. Is testable and maintainable

Provide code changes as structured JSON:
{
  "files": [
    {
      "path": "relative/path/to/file.ts",
      "action": "create|modify|delete",
      "content": "file content",
      "changes": [
        {
          "type": "add|modify|remove",
          "startLine": 1,
          "endLine": 10,
          "content": "new content"
        }
      ]
    }
  ],
  "summary": "Brief description of changes",
  "testingNotes": "How to test these changes"
}
```

### Workflow Node Specifications

#### 1. Priority Queue Trigger Node
```json
{
  "id": "priority-queue-trigger",
  "name": "Priority Queue Trigger",
  "type": "n8n-nodes-base.cron",
  "typeVersion": 1,
  "parameters": {
    "rule": {
      "interval": [
        {
          "field": "cronExpression",
          "value": "*/30 * * * * *"
        }
      ]
    }
  }
}
```

#### 2. Gemini AI Analysis Node
```json
{
  "id": "gemini-task-analysis",
  "name": "Gemini AI - Task Analysis",
  "type": "n8n-nodes-base.httpRequest",
  "typeVersion": 4,
  "parameters": {
    "url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent",
    "method": "POST",
    "sendHeaders": true,
    "headerParameters": {
      "parameters": [
        {
          "name": "Content-Type",
          "value": "application/json"
        },
        {
          "name": "x-goog-api-key",
          "value": "={{ $env.GEMINI_API_KEY }}"
        }
      ]
    },
    "sendBody": true,
    "bodyParameters": {
      "parameters": [
        {
          "name": "contents",
          "value": "={{ [{\"parts\": [{\"text\": `${promptTemplate}`}]}] }}"
        },
        {
          "name": "generationConfig",
          "value": {
            "temperature": 0.1,
            "maxOutputTokens": 4000,
            "topP": 0.9
          }
        }
      ]
    },
    "options": {
      "timeout": 30000
    }
  }
}
```

### Error Handling & Recovery

#### Retry Logic
```javascript
// Exponential backoff for API calls
const retryConfig = {
  maxRetries: 3,
  baseDelay: 1000,
  maxDelay: 10000,
  backoffFactor: 2
};

// Circuit breaker for external services
const circuitBreakerConfig = {
  failureThreshold: 5,
  resetTimeout: 60000,
  monitoringPeriod: 30000
};
```

#### Fallback Strategies
1. **Gemini API Failure**: Fall back to rule-based task classification
2. **File System Errors**: Queue tasks for manual review
3. **Git Operation Failures**: Create backup branches
4. **Quality Gate Failures**: Request human approval

### Performance Metrics

#### Key Performance Indicators (KPIs)
- **Throughput**: Tasks processed per hour
- **Success Rate**: Percentage of successfully completed tasks
- **Quality Score**: Code quality metrics from quality gate
- **Time to Completion**: Average time from task discovery to completion
- **Autonomous Rate**: Percentage of tasks completed without human intervention

#### Monitoring Endpoints
```bash
# Real-time metrics
GET /api/metrics/realtime

# Historical performance
GET /api/metrics/history?period=24h

# Task queue status
GET /api/queue/status

# Agent health check
GET /api/health/agents
```

### Scaling Considerations

#### Horizontal Scaling
- Multiple n8n instances with load balancing
- Distributed task queue with Redis
- Microservice architecture for specialist workflows

#### Vertical Scaling
- Increased memory allocation for Gemini processing
- SSD storage for faster file operations
- CPU optimization for parallel task execution

### Integration Patterns

#### Event-Driven Architecture
```mermaid
graph LR
    A[Task Discovery] --> B[Event Bus]
    B --> C[SDLC Monster]
    B --> D[Quality Gate]
    B --> E[Supervisor UI]
    C --> F[Specialist Workflows]
    F --> G[Git Operations]
    G --> H[Notification System]
```

#### API Gateway Pattern
- Centralized routing for all workflow communications
- Rate limiting and authentication
- Request/response transformation
- Circuit breaker implementation

---

**The SDLC Monster represents the future of autonomous software development - intelligent, efficient, and continuously improving.**
