{"name": "beauty-crm-markdown-analyzer", "version": "1.0.0", "description": "Markdown task analyzer for autonomous AI agents", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "jest", "scan": "node scan-tasks.js"}, "dependencies": {"express": "^4.18.2", "axios": "^1.6.2", "fs-extra": "^11.2.0", "glob": "^10.3.10", "marked": "^11.1.1", "gray-matter": "^4.0.3", "chokidar": "^3.5.3", "helmet": "^7.1.0", "cors": "^2.8.5", "morgan": "^1.10.0", "dotenv": "^16.3.1", "uuid": "^9.0.1", "cron": "^3.1.6", "simple-git": "^3.20.0", "yaml": "^2.3.4"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0"}, "keywords": ["markdown", "task-analyzer", "ai-agents", "automation", "tickets"], "author": "Beauty CRM Team", "license": "MIT"}