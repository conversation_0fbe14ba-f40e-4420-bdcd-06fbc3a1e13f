<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Beauty CRM - Task Discovery Dashboard</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            color: #333;
        }
        .header {
            background: #2c3e50;
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .scan-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
        }
        .scan-btn:hover { background: #2980b9; }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 2rem;
        }
        .stat-card {
            background: white;
            padding: 1rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #2c3e50;
        }
        .stat-label {
            color: #7f8c8d;
            font-size: 0.9rem;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }
        .filters {
            background: white;
            padding: 1rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            display: flex;
            gap: 1rem;
            align-items: center;
        }
        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }
        .filter-group label {
            font-size: 0.8rem;
            color: #7f8c8d;
            font-weight: bold;
        }
        .filter-group select {
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .tasks-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            gap: 1rem;
        }
        .task-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 1rem;
            border-left: 4px solid #3498db;
        }
        .task-card.high-priority { border-left-color: #e74c3c; }
        .task-card.medium-priority { border-left-color: #f39c12; }
        .task-card.low-priority { border-left-color: #27ae60; }
        .task-card.completed { opacity: 0.6; background: #f8f9fa; }
        .task-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 0.5rem;
        }
        .task-text {
            font-weight: bold;
            margin-bottom: 0.5rem;
            line-height: 1.4;
        }
        .task-meta {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
        }
        .tag {
            display: inline-block;
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: bold;
        }
        .tag.priority-high { background: #fadbd8; color: #e74c3c; }
        .tag.priority-medium { background: #fef9e7; color: #f39c12; }
        .tag.priority-low { background: #d5f4e6; color: #27ae60; }
        .tag.risk-high { background: #fadbd8; color: #e74c3c; }
        .tag.risk-medium { background: #fef9e7; color: #f39c12; }
        .tag.risk-low { background: #d5f4e6; color: #27ae60; }
        .tag.tech { background: #ebf3fd; color: #3498db; }
        .task-location {
            font-size: 0.8rem;
            color: #7f8c8d;
            margin-bottom: 0.5rem;
        }
        .complexity-bar {
            width: 100%;
            height: 4px;
            background: #ecf0f1;
            border-radius: 2px;
            overflow: hidden;
            margin: 0.5rem 0;
        }
        .complexity-fill {
            height: 100%;
            background: linear-gradient(90deg, #27ae60 0%, #f39c12 50%, #e74c3c 100%);
            transition: width 0.3s ease;
        }
        .task-actions {
            display: flex;
            gap: 0.5rem;
            margin-top: 1rem;
        }
        .btn {
            padding: 0.25rem 0.5rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8rem;
        }
        .btn-assign { background: #3498db; color: white; }
        .btn-complete { background: #27ae60; color: white; }
        .btn-assign:hover { background: #2980b9; }
        .btn-complete:hover { background: #229954; }
        .no-tasks {
            text-align: center;
            color: #7f8c8d;
            padding: 3rem;
            font-style: italic;
            grid-column: 1 / -1;
        }
        .loading {
            text-align: center;
            padding: 2rem;
            color: #7f8c8d;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📋 Task Discovery Dashboard - Beauty CRM</h1>
        <button class="scan-btn" onclick="scanTasks()">🔍 Scan Now</button>
    </div>

    <div class="stats" id="stats">
        <!-- Stats will be loaded here -->
    </div>

    <div class="container">
        <div class="filters">
            <div class="filter-group">
                <label>Priority</label>
                <select id="priority-filter" onchange="applyFilters()">
                    <option value="">All Priorities</option>
                    <option value="high">High</option>
                    <option value="medium">Medium</option>
                    <option value="low">Low</option>
                </select>
            </div>
            <div class="filter-group">
                <label>Risk Level</label>
                <select id="risk-filter" onchange="applyFilters()">
                    <option value="">All Risk Levels</option>
                    <option value="high">High Risk</option>
                    <option value="medium">Medium Risk</option>
                    <option value="low">Low Risk</option>
                </select>
            </div>
            <div class="filter-group">
                <label>Status</label>
                <select id="status-filter" onchange="applyFilters()">
                    <option value="">All Tasks</option>
                    <option value="false">Pending</option>
                    <option value="true">Completed</option>
                </select>
            </div>
            <div class="filter-group">
                <label>Tags</label>
                <select id="tags-filter" onchange="applyFilters()">
                    <option value="">All Tags</option>
                    <option value="typescript">TypeScript</option>
                    <option value="react">React</option>
                    <option value="api">API</option>
                    <option value="database">Database</option>
                    <option value="testing">Testing</option>
                    <option value="frontend">Frontend</option>
                    <option value="backend">Backend</option>
                    <option value="docker">Docker</option>
                </select>
            </div>
        </div>

        <div class="tasks-grid" id="tasks-grid">
            <div class="loading">Loading tasks...</div>
        </div>
    </div>

    <script>
        let allTasks = [];

        async function loadStats() {
            try {
                const response = await fetch('/api/stats');
                const data = await response.json();
                
                if (data.success) {
                    const statsContainer = document.getElementById('stats');
                    statsContainer.innerHTML = `
                        <div class="stat-card">
                            <div class="stat-number">${data.stats.total}</div>
                            <div class="stat-label">Total Tasks</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">${data.stats.pending}</div>
                            <div class="stat-label">Pending Tasks</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">${data.stats.completed}</div>
                            <div class="stat-label">Completed Tasks</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">${data.stats.byPriority.high}</div>
                            <div class="stat-label">High Priority</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">${data.stats.byRisk.high}</div>
                            <div class="stat-label">High Risk</div>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Error loading stats:', error);
            }
        }

        async function loadTasks() {
            try {
                const response = await fetch('/api/tasks');
                const data = await response.json();
                
                if (data.success) {
                    allTasks = data.tasks;
                    displayTasks(allTasks);
                }
            } catch (error) {
                console.error('Error loading tasks:', error);
                document.getElementById('tasks-grid').innerHTML = 
                    '<div class="no-tasks">Error loading tasks</div>';
            }
        }

        function displayTasks(tasks) {
            const container = document.getElementById('tasks-grid');
            
            if (tasks.length === 0) {
                container.innerHTML = '<div class="no-tasks">No tasks found</div>';
                return;
            }

            container.innerHTML = tasks.map(task => `
                <div class="task-card ${task.priority}-priority ${task.isCompleted ? 'completed' : ''}">
                    <div class="task-header">
                        <div class="task-meta">
                            <span class="tag priority-${task.priority}">P: ${task.priority.toUpperCase()}</span>
                            <span class="tag risk-${task.riskLevel}">R: ${task.riskLevel.toUpperCase()}</span>
                            ${task.tags.map(tag => `<span class="tag tech">${tag}</span>`).join('')}
                        </div>
                        ${task.isCompleted ? '<span style="color: #27ae60;">✅</span>' : ''}
                    </div>
                    
                    <div class="task-text">${task.text}</div>
                    
                    <div class="task-location">
                        📁 ${task.filePath}:${task.lineNumber}
                    </div>
                    
                    <div class="complexity-bar">
                        <div class="complexity-fill" style="width: ${(task.estimatedComplexity / 5) * 100}%"></div>
                    </div>
                    <small>Complexity: ${task.estimatedComplexity}/5</small>
                    
                    ${!task.isCompleted ? `
                        <div class="task-actions">
                            <button class="btn btn-assign" onclick="assignTask('${task.id}')">
                                🤖 Assign to Agent
                            </button>
                            <button class="btn btn-complete" onclick="markCompleted('${task.id}')">
                                ✅ Mark Complete
                            </button>
                        </div>
                    ` : `
                        <div style="margin-top: 1rem; font-size: 0.8rem; color: #27ae60;">
                            ✅ Completed ${task.completedAt ? new Date(task.completedAt).toLocaleDateString() : ''}
                            ${task.completedBy ? `by ${task.completedBy}` : ''}
                        </div>
                    `}
                </div>
            `).join('');
        }

        function applyFilters() {
            const priority = document.getElementById('priority-filter').value;
            const risk = document.getElementById('risk-filter').value;
            const status = document.getElementById('status-filter').value;
            const tags = document.getElementById('tags-filter').value;

            let filtered = allTasks;

            if (priority) filtered = filtered.filter(t => t.priority === priority);
            if (risk) filtered = filtered.filter(t => t.riskLevel === risk);
            if (status !== '') filtered = filtered.filter(t => t.isCompleted === (status === 'true'));
            if (tags) filtered = filtered.filter(t => t.tags.includes(tags));

            displayTasks(filtered);
        }

        async function scanTasks() {
            const btn = document.querySelector('.scan-btn');
            btn.textContent = '🔄 Scanning...';
            btn.disabled = true;

            try {
                const response = await fetch('/api/tasks/scan', { method: 'POST' });
                const data = await response.json();
                
                if (data.success) {
                    showNotification(`Scan complete: ${data.results.newTasks} new tasks found`, 'success');
                    await loadTasks();
                    await loadStats();
                } else {
                    showNotification('Scan failed', 'error');
                }
            } catch (error) {
                showNotification('Scan error: ' + error.message, 'error');
            } finally {
                btn.textContent = '🔍 Scan Now';
                btn.disabled = false;
            }
        }

        async function assignTask(taskId) {
            try {
                // Trigger the coding workflow for this task
                const task = allTasks.find(t => t.id === taskId);
                if (!task) return;

                const response = await fetch('http://n8n.beauty-crm.localhost/webhook/coding-request', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        task: task.text,
                        description: `Task from ${task.filePath}:${task.lineNumber}`,
                        priority: task.priority,
                        riskLevel: task.riskLevel,
                        tags: task.tags,
                        source: 'task-dashboard',
                        taskId: task.id
                    })
                });

                if (response.ok) {
                    showNotification('Task assigned to AI agent', 'success');
                } else {
                    showNotification('Failed to assign task', 'error');
                }
            } catch (error) {
                showNotification('Error assigning task: ' + error.message, 'error');
            }
        }

        async function markCompleted(taskId) {
            try {
                const response = await fetch(`/api/tasks/${taskId}/complete`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ agentId: 'manual-completion' })
                });

                if (response.ok) {
                    showNotification('Task marked as completed', 'success');
                    await loadTasks();
                    await loadStats();
                } else {
                    showNotification('Failed to mark task complete', 'error');
                }
            } catch (error) {
                showNotification('Error completing task: ' + error.message, 'error');
            }
        }

        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 1rem;
                border-radius: 4px;
                color: white;
                font-weight: bold;
                z-index: 1000;
                background: ${type === 'error' ? '#e74c3c' : '#27ae60'};
            `;
            notification.textContent = message;
            document.body.appendChild(notification);
            
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 5000);
        }

        // Initialize
        async function initialize() {
            await loadStats();
            await loadTasks();
        }

        // Auto-refresh every 30 seconds
        setInterval(async () => {
            await loadStats();
            await loadTasks();
        }, 30000);

        initialize();
    </script>
</body>
</html>
