domain types for all shared packages, except for the kernel and environment-names and system-settings and features
 
Do not create types about Environment and system-settings and features, only create types about the domain types.

## Tasks

### General Tasks

- [ ] Review and update content - M1
- [ ] Add missing information - M2
- [ ] Improve structure - M3
- [ ] Add examples - M4
- [ ] Validate accuracy - M5

