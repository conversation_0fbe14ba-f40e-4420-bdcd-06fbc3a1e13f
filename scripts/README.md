# Beauty CRM Scripts

This directory contains scripts to simplify the development workflow for the Beauty CRM project.

## Script Organization

The scripts are organized by their purpose:

- `build-scripts/` - Scripts for building packages and services
- `docker.sh` - Script for managing Docker containers
- `start.sh` - Script for starting services

## Build Scripts

### Shared Packages

```bash
# Build all shared packages (includes Salon)
scripts/build-scripts/shared-packages.sh --all

# Build only platform engineering packages (includes Salon)
scripts/build-scripts/shared-packages.sh --platform

# Build only product engineering packages (includes Salon)
scripts/build-scripts/shared-packages.sh --product

# Build only system-settings package (non-NX package, includes Salon)
scripts/build-scripts/shared-packages.sh --system-settings
```

### Services

```bash
# Build all services (includes Salon)
scripts/build-scripts/services.sh --all

# Build only backend services (includes Salon)
scripts/build-scripts/services.sh --backend

# Build only frontend services (includes Salon)
scripts/build-scripts/services.sh --frontend

# Build appointment planner (includes Salon)
scripts/build-scripts/services.sh --appointment-planner
```

## Start Script

```bash
# Start minimal services (default, includes Salon)
scripts/start.sh

# Start all services (includes Salon)
scripts/start.sh --all

# Start only Salon services
scripts/start.sh --salon

# Start only inventory services (includes Salon)
scripts/start.sh --inventory

# Start only appointment services (includes Salon)
scripts/start.sh --appointment

# Start only CRM services (includes Salon)
scripts/start.sh --crm

# Start only workflow services (includes Salon)
scripts/start.sh --workflow

# Start only dashboard services (includes Salon)
scripts/start.sh --dashboard

# Start appointment planner (includes Salon)
scripts/start.sh --appointment-planner

# Start all backend services (includes Salon)
scripts/start.sh --backend

# Start all frontend services (includes Salon)
scripts/start.sh --frontend

# Start shell with remotes (includes Salon)
scripts/start.sh --shell
```

## Docker Script

```bash
# Check if Docker is running
scripts/docker.sh --check

# Start main Docker containers (includes Salon)
scripts/docker.sh --up

# Stop main Docker containers (includes Salon)
scripts/docker.sh --down

# Start identity services containers (includes Salon)
scripts/docker.sh --identity-up

# Stop identity services containers (includes Salon)
scripts/docker.sh --identity-down

# Start ELK stack containers (includes Salon)
scripts/docker.sh --elk-up

# Stop ELK stack containers (includes Salon)
scripts/docker.sh --elk-down

# Start all containers (includes Salon)
scripts/docker.sh --up --identity-up --elk-up

# Stop all containers (includes Salon)
scripts/docker.sh --down --identity-down --elk-down
```

## Package.json Integration

The scripts are integrated into `package.json` with simple aliases for common operations:

```json
{
  "scripts": {
    "build": "scripts/build-scripts/shared-packages.sh --all && scripts/build-scripts/services.sh --all",
    "build:shared": "scripts/build-scripts/shared-packages.sh --all",
    "start": "scripts/start.sh",
    "start:all": "scripts/start.sh --all",
    "docker": "scripts/docker.sh",
    "docker:up": "scripts/docker.sh --up",
    "docker:down": "scripts/docker.sh --down"
  }
}
```


## Tasks

### Extracted Tasks

- [ ] `build-scripts/` - Scripts for building packages and services - M1
- [ ] `docker.sh` - Script for managing Docker containers - M2
- [ ] `start.sh` - Script for starting services - M3

### General Tasks

- [ ] Review and update content - M1
- [ ] Add missing information - M2
- [ ] Improve structure - M3
- [ ] Add examples - M4
- [ ] Validate accuracy - M5

