## Distributed Systems Reliability Patterns (Onboarding)

All engineers must review and understand the approved reliability patterns before contributing reliability-critical code.

- See [docs/reliability-patterns.md](./reliability-patterns.md) for approved patterns, templates, and code examples.
- External resources:
  - [Microsoft Cloud Design Patterns](https://learn.microsoft.com/en-us/azure/architecture/patterns/)
  - [AWS Well-Architected Framework](https://aws.amazon.com/architecture/well-architected/)
- Example PRs: (add links as they are created) 