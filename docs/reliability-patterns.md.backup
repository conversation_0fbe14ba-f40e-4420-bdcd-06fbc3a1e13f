# Reliability Patterns: Approved Standards

This document lists approved reliability patterns for distributed systems in this codebase. All reliability-critical code must reference these patterns and use the provided templates where possible.

## Patterns

### 1. Circuit Breaker
- **Use for:** Protecting services from repeated failures of downstream dependencies.
- **Template:** [`pattern-templates/circuit-breaker.ts`](../shared-platform-engineering/pattern-templates/circuit-breaker.ts)
- **Example:**
```typescript
import { createCircuitBreaker } from '@beauty-crm/shared-platform-engineering/pattern-templates/circuit-breaker';

const breaker = createCircuitBreaker({
  failureThreshold: 5,
  recoveryTimeoutMs: 30000,
  onOpen: () => logger.warn('Circuit breaker opened'),
});

export async function fetchWithBreaker(url: string) {
  return breaker.execute(() => fetch(url));
}
```

### 2. Retry
- **Use for:** Retrying transient failures (e.g., network errors).
- **Template:** [`pattern-templates/retry.ts`](../shared-platform-engineering/pattern-templates/retry.ts)
- **Example:**
```typescript
import { retry } from '@beauty-crm/shared-platform-engineering/pattern-templates/retry';

export async function fetchWithRetry(url: string) {
  return retry(() => fetch(url), { retries: 3, delayMs: 500 });
}
```

### 3. Timeout
- **Use for:** Preventing operations from hanging indefinitely.
- **Template:** [`pattern-templates/timeout.ts`](../shared-platform-engineering/pattern-templates/timeout.ts)
- **Example:**
```typescript
import { withTimeout } from '@beauty-crm/shared-platform-engineering/pattern-templates/timeout';

export async function fetchWithTimeout(url: string) {
  return withTimeout(() => fetch(url), 2000); // 2s timeout
}
```

### 4. Idempotency
- **Use for:** Ensuring repeated requests have the same effect as a single request.
- **Template:** [`pattern-templates/idempotency.ts`](../shared-platform-engineering/pattern-templates/idempotency.ts)
- **Example:**
```typescript
import { ensureIdempotent } from '@beauty-crm/shared-platform-engineering/pattern-templates/idempotency';

export async function processRequest(req: Request) {
  return ensureIdempotent(req, async () => {
    // business logic
  });
}
```

### 5. Bulkhead
- **Use for:** Isolating failures and limiting resource usage per component.
- **Template:** [`pattern-templates/bulkhead.ts`](../shared-platform-engineering/pattern-templates/bulkhead.ts)
- **Example:**
```typescript
import { createBulkhead } from '@beauty-crm/shared-platform-engineering/pattern-templates/bulkhead';

const bulkhead = createBulkhead({ maxConcurrent: 10 });

export async function safeOperation(fn: () => Promise<any>) {
  return bulkhead.execute(fn);
}
```

### 6. Outbox Pattern
- **Use for:** Ensuring reliable, atomic publishing of events as part of a database transaction.
- **Library:** [`@beauty-crm/platform-eventing`](../shared-platform-engineering/platform-eventing/)
- **Example:**
```typescript
import {
  OutboxManager,
  createPrismaOutboxStorage,
} from '@beauty-crm/platform-eventing';

// Setup (once per service)
const outboxStorage = createPrismaOutboxStorage(prisma, 'my-service');
const outboxManager = new OutboxManager(outboxStorage, { source: 'my-service' });

// Store events atomically in a transaction
await prisma.$transaction(async (tx) => {
  // ...business logic...
  await outboxManager.storeEvents([domainEvent], tx);
});

// Relay events (in relayer/worker)
const unprocessed = await outboxManager.getUnprocessedEvents(100);
// ...publish to NATS, then...
await outboxManager.markEventsProcessed(unprocessed.map(e => e.eventId));
```
- **Benefits:** Guarantees that business data and events are committed together, preventing lost or duplicate events. Provides retries, monitoring, and health checks out of the box.

---

## References
- [Microsoft Cloud Design Patterns](https://learn.microsoft.com/en-us/azure/architecture/patterns/)
- [AWS Well-Architected Framework](https://aws.amazon.com/architecture/well-architected/)

## See Also
- Pattern templates in `shared-platform-engineering/pattern-templates/`
- PR checklist in `.github/PULL_REQUEST_TEMPLATE.md`
- Onboarding/training in `docs/development-roadmap.md` 