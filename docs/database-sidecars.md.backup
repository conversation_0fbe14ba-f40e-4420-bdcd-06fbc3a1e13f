# Database Sidecars for Beauty CRM

## Overview

Database sidecars are lightweight containers that handle database initialization, migrations, and seeding for each backend service in the Beauty CRM system. They ensure that databases are properly set up before the main application services start.

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   PostgreSQL    │    │  Database       │    │   Backend       │
│   Container     │◄───┤  Migrator       │◄───┤   Service       │
│                 │    │  Sidecar        │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Components

### 1. Database Initialization
- **File**: `services/orchestration/postgres-init/02-create-databases.sql`
- **Purpose**: Creates all required databases for microservices
- **Databases Created**:
  - `beauty_crm_appointment`
  - `beauty_crm_treatment`
  - `beauty_crm_salon`
  - `beauty_crm_staff`
  - `beauty_crm_identity`

### 2. Migration Sidecars
- **File**: `services/orchestration/docker-compose.db-sidecars.yml`
- **Purpose**: Defines migration containers for each service
- **Services**:
  - `appointment-db-migrator`
  - `treatment-db-migrator`
  - `salon-db-migrator`
  - `staff-db-migrator`
  - `identity-db-migrator`

### 3. Migrator Dockerfiles
- **Template**: `services/*/Dockerfile.migrator`
- **Purpose**: Lightweight containers that run Prisma migrations
- **Features**:
  - Wait for PostgreSQL to be ready
  - Run Prisma migrations (`prisma migrate deploy`)
  - Generate Prisma client
  - Run database seeds (if available)
  - One-time execution (restart: "no")

## Usage

### Setup
```bash
# Setup migrator Dockerfiles for all services
./scripts/setup-db-migrators.sh
```

### Start with Tilt
```bash
# Start all services including database sidecars
tilt up

# Start only database services
tilt up --only databases
```

### Manual Migration
```bash
# Run migration for specific service
docker run --rm \
  -e DATABASE_URL=********************************************************************/beauty_crm_appointment \
  appointment_db_migrator
```

## Benefits

1. **Automated Setup**: Databases are automatically initialized and migrated
2. **Dependency Management**: Backend services wait for migrations to complete
3. **Consistency**: All services use the same migration pattern
4. **Isolation**: Each service has its own database and migration process
5. **Reliability**: Migrations run before application startup
6. **Monitoring**: Migration status visible in Tilt UI

## Monitoring

- **Tilt UI**: http://localhost:10350
- **Labels**: `infra.data`, `migration`, `{service-name}`
- **Dependencies**: Each migrator depends on `postgres` service
- **Logs**: Available in Tilt UI for troubleshooting

## Troubleshooting

### Migration Fails
1. Check PostgreSQL is running and healthy
2. Verify database connection string
3. Check Prisma schema syntax
4. Review migration files for conflicts

### Service Won't Start
1. Ensure migrator completed successfully
2. Check service dependencies in Tiltfile
3. Verify database tables were created
4. Check application database connection

### Manual Database Reset
```bash
# Connect to PostgreSQL
docker exec -it beauty_crm_postgres psql -U beauty_crm

# Drop and recreate database
DROP DATABASE beauty_crm_appointment;
CREATE DATABASE beauty_crm_appointment;

# Re-run migrator
tilt trigger appointment-db-migrator
```

## Configuration

### Environment Variables
- `DATABASE_URL`: PostgreSQL connection string
- `NODE_ENV`: Environment (production/development)

### Dependencies
- PostgreSQL container must be healthy
- Prisma schema and migrations must exist
- Bun runtime for executing migrations

## Future Enhancements

1. **Health Checks**: Add health endpoints to migrators
2. **Rollback Support**: Implement migration rollback capabilities
3. **Seed Management**: Enhanced seeding with environment-specific data
4. **Migration Validation**: Pre-migration schema validation
5. **Backup Integration**: Automatic backups before migrations
