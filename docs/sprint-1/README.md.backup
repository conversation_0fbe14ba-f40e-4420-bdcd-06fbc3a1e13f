# Sprint 1: End-to-End Appointment Creation

**Sprint Goal:** Implement the complete, end-to-end flow for creating a new appointment, ensuring it is robust, transactional, and fully integrated with the eventing system across all relevant services.

This sprint focuses on building a solid foundation for the appointment domain by implementing the core creation logic according to the principles of Clean Architecture and Event-Driven Design.

## Architecture Overview

The implementation will adhere to the Ports and Adapters (Hexagonal) architecture, using a consistent set of interfaces for each service. The core flow involves:
1.  **API Layer (`presentation`):** Receives external requests (e.g., HTTP).
2.  **Application Layer (`application`):** A use case service orchestrates the business logic.
3.  **Domain Layer (`domain`):** Contains the core business logic, aggregates, and ports.

---

## What Needs to Be Done: Integration Tasks

### Integrate `platform-appointment-eventing` into Backend Services

Both the following services must be updated to use the new event publisher abstraction:
- `services/appointment/appointment-planner-backend`
- `services/appointment/appointment-management-backend`

#### Migration Steps:
1. **Add Dependency:**
   - Ensure both services have `platform-appointment-eventing` as a dependency in their package.json.

2. **Refactor Event Publishing:**
   - Replace any direct usage of NATS or legacy event publisher code with the new `AppointmentEventPublisherImpl` from `platform-appointment-eventing`.
   - Use the provided factory or class to publish all appointment-related events (upsert, cancel, etc.).
   - Remove direct NATS connection logic and related configuration from these services.

3. **Update Event Payloads:**
   - Ensure all published events conform to the new `DomainEvent` schema and use the correct data structures (e.g., `UnifiedAppointment`).
   - Update any event consumers to expect the new schema if necessary.

4. **Testing:**
   - Write or update integration tests to verify that events are published to the outbox and NATS via the new abstraction.
   - Test error handling, transactionality, and event delivery in both backend services.

5. **Documentation & Code Cleanup:**
   - Update internal documentation to reference the new eventing approach.
   - Remove obsolete code and configuration related to the old eventing logic.

---

**Goal:**
All appointment event publishing in both services must be routed through the new eventing platform for consistency, reliability, and maintainability.
4.  **Infrastructure Layer (`infrastructure`):** Implements the ports for databases, event publishers, etc.

## User Stories

*   **As a Principal Engineer,** I want the appointment creation process to be fully transactional, so that an appointment and its corresponding creation event are saved together atomically, preventing data inconsistency.
*   **As a Backend Engineer,** I want to implement the `CreateAppointmentUseCase` based on a clear interface, so that the business logic is decoupled from the web and infrastructure layers.
*   **As a QA Lead,** I want the entire flow to be covered by integration tests for each service, so that we can deploy with confidence and prevent regressions.

## Sprint Task Checklist

| Task                               | `appointment-planner-backend` | `appointment-management-backend` |
| ---------------------------------- | :---------------------------: | :------------------------------: |
| **1. Define Domain Ports**         |                               |                                  |
| `CreateAppointmentUseCase.ts`      |              ✅               |                ✅                |
| `AppointmentRepository.ts`         |              ✅               |                ✅                |
| `AppointmentEventing.ts`           |              ✅               |                ✅                |
| **2. Implement Infra Adapters**    |                               |                                  |
| `PrismaAppointmentRepository.ts`   |              ✅               |                ☐                 |
| `PrismaTransactionalOutbox.ts`     |              ✅               |                ☐                 |
| `NatsEventPublisher.ts`            |              ✅               |                ☐                 |
| **3. Implement Application Logic** |                               |                                  |
| `CreateAppointmentService.ts`      |              ✅               |                ☐                 |
| **4. Wire Presentation Layer**     |                               |                                  |
| `AppointmentController.ts`         |              ✅               |                ☐                 |
| `appointmentRoutes.ts`             |              ✅               |                ☐                 |
| **5. Write Integration Tests**     |                               |                                  |
| `CreateAppointment.test.ts`        |              ✅               |                ☐                 |

## Definition of Done

*   All interfaces are implemented with concrete classes for each service.
*   The `CreateAppointmentUseCase` correctly orchestrates the creation flow in each service.
*   The entire operation (appointment creation + event storage) is guaranteed to be atomic.
*   The API endpoint for each service is fully functional.
*   Integration tests pass for each service, verifying transactional integrity.
*   Code is reviewed and merged.


 ███            █████████  ██████████ ██████   ██████ █████ ██████   █████ █████
░░░███         ███░░░░░███░░███░░░░░█░░██████ ██████ ░░███ ░░██████ ░░███ ░░███
  ░░░███      ███     ░░░  ░███  █ ░  ░███░█████░███  ░███  ░███░███ ░███  ░███
    ░░░███   ░███          ░██████    ░███░░███ ░███  ░███  ░███░░███░███  ░███
     ███░    ░███    █████ ░███░░█    ░███ ░░░  ░███  ░███  ░███ ░░██████  ░███
   ███░      ░░███  ░░███  ░███ ░   █ ░███      ░███  ░███  ░███  ░░█████  ░███
 ███░         ░░█████████  ██████████ █████     █████ █████ █████  ░░█████ █████
░░░            ░░░░░░░░░  ░░░░░░░░░░ ░░░░░     ░░░░░ ░░░░░ ░░░░░    ░░░░░ ░░░░░


Tips for getting started:
1. Ask questions, edit files, or run commands.
2. Be specific for the best results.
3. /help for more information.


╭─────────────────────────────────────────────────────────────────────────────────────────╮
│  > bun run lint:check --fix --max-diagnostics=999999999                                 │
│    $ ./node_modules/@biomejs/cli-darwin-arm64/biome check . --fix                       │
│    "--max-diagnostics=999999999"                                                        │
│    shared-platform-engineering/platform-eventing/src/PrismaOutboxStorage.ts:50:70       │
│    lint/suspicious/noExplicitAny ━━━━━━━━━━                                             │
│                                                                                         │
│      ⚠ Unexpected any. Specify a different type.                                        │
│                                                                                         │
│        48 │  */                                                                         │
│        49 │                                                                             │
│      > 50 │ export class PrismaOutboxStorage<TClient extends GenericPrismaClient<any>>  │
│           │                                                                      ^^^    │
│        51 │   implements OutboxStorage                                                  │
│        52 │ {                                                                           │
│                                                                                         │
│      ℹ any disables many type checking rules. Its use should be avoided.                │
│                                                                                         │
│                                                                                         │
│    Checked 1617 files in 1747ms. No fixes applied.                                      │
│    Found 1 warning.                                                                     │
╰─────────────────────────────────────────────────────────────────────────────────────────╯

✦ Okay, I see the linting warning about noExplicitAny in
  shared-platform-engineering/platform-eventing/src/PrismaOutboxStorage.ts. I will read the
  file to understand the context and determine the correct type to use instead of any.
 ╭────────────────────────────────────────────────────────╮
 │ ✔  ReadFile ...engineering/src/PrismaOutboxStorage.ts  │
 ╰────────────────────────────────────────────────────────╯
✕ [API Error: got status: UNAVAILABLE. {"error":{"code":503,"message":"The model is 
  overloaded. Please try again later.","status":"UNAVAILABLE"}}]

 ╭─────────────────────────────────────────────────────────────────────────────────────────────╮
 │ ✔  SearchText 'interface GenericPrismaClient|type GenericPrismaClient|class GenericPrisma…  │
 │                                                                                             │
 │    Found 1 match                                                                            │
 ╰─────────────────────────────────────────────────────────────────────────────────────────────╯
 ╭────────────────────────────────────────────────────────╮
 │ ✔  ReadFile ...engineering/src/PrismaOutboxStorage.ts  │
 ╰────────────────────────────────────────────────────────╯
 ╭─────────────────────────────────────────────────────────────────────────────────────────────╮
 │ ✔  Edit ...engineering/src/PrismaOutboxStorage.ts: export class PrismaOutboxStora... => e…  │
 │                                                                                             │
 │    47      * Implements OutboxStorage using Prisma ORM for database operations              │
 │    48      */                                                                               │
 │    49                                                                                       │
 │    50   - export class PrismaOutboxStorage<TClient extends                                  │
 │           GenericPrismaClient<any>>                                                         │
 │    50   + export class PrismaOutboxStorage<TClient extends GenericPrismaClient<{            │
 │           transactionalOutbox: OutboxModelMethods }>>                                       │
 │    51       implements OutboxStorage                                                        │
 │    52     {                                                                                 │
 │    53       private readonly outboxModel: OutboxModelMethods;                               │
 ╰─────────────────────────────────────────────────────────────────────────────────────────────╯
 ╭─────────────────────────────────────────────────────────────────────────────────────────────╮
 │ ✔  Edit ...engineering/src/PrismaOutboxStorage.ts: export function createPrismaOu... => e…  │
 │                                                                                             │
 │    280     * 🏭 Create Prisma Outbox Storage instance                                       │
 │    281     */                                                                               │
 │    282    export function createPrismaOutboxStorage<                                        │
 │    283  -   // biome-ignore lint/suspicious/noExplicitAny: Generic client type              │
 │           requires any for now                                                              │
 │    284  -   TClient extends GenericPrismaClient<any>,                                       │
 │    283  +   TClient extends GenericPrismaClient<{ transactionalOutbox:                      │
 │           OutboxModelMethods }>,                                                            │
 │    284    >(prisma: TClient, source: string): PrismaOutboxStorage<TClient> {                │
 │    285      return new PrismaOutboxStorage(prisma, source);                                 │
 │    286    }                                                                                 │
 ╰─────────────────────────────────────────────────────────────────────────────────────────────╯
 ╭─────────────────────────────────────────────────────────────────────────────────────────────╮
 │ ✔  Shell bun run lint:check --fix --max-diagnostics=999999999 (Running lint check to veri…  │
 │                                                                                             │
 │    $ ./node_modules/@biomejs/cli-darwin-arm64/biome check . --fix                           │
 │    "--max-diagnostics=999999999"                                                            │
 │    Checked 1617 files in 1079ms. Fixed 1 file.                                              │
 │                                                                                             │
 ╰─────────────────────────────────────────────────────────────────────────────────────────────╯
 ╭───────────────────────────────────────────────────────────────────────────────────╮
 │ ✔  Shell git status (Checking git status to see the changes.)                     │
 │                                                                                   │
 │    On branch main                                                                 │
 │    Your branch is up to date with 'origin/main'.                                  │
 │                                                                                   │
 │    Changes to be committed:                                                       │
 │      (use "git reset HEAD <file>..." to unstage)                                  │
 │                                                                                   │
 │      modified:                                                                     │
 │    shared-platform-engineering/platform-eventing/src/PrismaOutboxStorage.ts       │
 │                                                                                   │
 │    Changes not staged for commit:                                                 │
 │      (use "git add <file>..." to update what will be committed)                   │
 │      (use "git checkout -- <file>..." to discard changes in working directory)    │
 │                                                                                   │
 │      modified:                                                                     │
 │    shared-platform-engineering/platform-eventing/src/PrismaOutboxStorage.ts       │
 │                                                                                   │
 │                                                                                   │
 ╰───────────────────────────────────────────────────────────────────────────────────╯
✦ I have fixed the noExplicitAny linting warning in
  shared-platform-engineering/platform-eventing/src/PrismaOutboxStorage.ts.


  The following file has been modified:
   - shared-platform-engineering/platform-eventing/src/PrismaOutboxStorage.ts


  Would you like me to commit these changes with the message "Fix: Resolve noExplicitAny in
  PrismaOutboxStorage"?



╭───────────╮
│  > /quit  │
╰───────────╯

╭────────────────────────────────────────────────────────────────────────────────────────────────────╮
│                                                                                                    │
│  Agent powering down. Goodbye!                                                                     │
│                                                                                                    │
│  Interaction Summary                                                                               │
│  Tool Calls:                 7 ( ✔ 7 ✖ 0 )                                                         │
│  Success Rate:               100.0%                                                                │
│  User Agreement:             100.0% (3 reviewed)                                                   │
│                                                                                                    │
│  Performance                                                                                       │
│  Wall Time:                  3m 27s                                                                │
│  Agent Active:               1m 47s                                                                │
│    » API Time:               31.4s (29.2%)                                                         │
│    » Tool Time:              1m 16s (70.8%)                                                        │
│                                                                                                    │
│                                                                                                    │
│  Model Usage                  Reqs   Input Tokens  Output Tokens                                   │
│  ───────────────────────────────────────────────────────────────                                   │
│  gemini-2.5-flash-preview-       9        135,385            686                                   │
│  04-17                                                                                             │
│                                                                                                    │
│  Savings Highlight: 115,141 (85.0%) of input tokens were served from the cache, reducing costs.    │
│                                                                                                    │
│  » Tip: For a full token breakdown, run `/stats model`.                                            │
│                                                                                                    │
╰────────────────────────────────────────────────────────────────────────────────────────────────────╯

  /private/v/w/2025/o/beauty-crm   main *114 +1 !1 ❯                     ✘ INT  2.6.2  13:37:11
❯ 
❯ 
❯ gemini


 ███            █████████  ██████████ ██████   ██████ █████ ██████   █████ █████
░░░███         ███░░░░░███░░███░░░░░█░░██████ ██████ ░░███ ░░██████ ░░███ ░░███
  ░░░███      ███     ░░░  ░███  █ ░  ░███░█████░███  ░███  ░███░███ ░███  ░███
    ░░░███   ░███          ░██████    ░███░░███ ░███  ░███  ░███░░███░███  ░███
     ███░    ░███    █████ ░███░░█    ░███ ░░░  ░███  ░███  ░███ ░░██████  ░███
   ███░      ░░███  ░░███  ░███ ░   █ ░███      ░███  ░███  ░███  ░░█████  ░███
 ███░         ░░█████████  ██████████ █████     █████ █████ █████  ░░█████ █████
░░░            ░░░░░░░░░  ░░░░░░░░░░ ░░░░░     ░░░░░ ░░░░░ ░░░░░    ░░░░░ ░░░░░


Tips for getting started:
1. Ask questions, edit files, or run commands.
2. Be specific for the best results.
3. /help for more information.


Using 2 GEMINI.md files and 6 MCP servers (ctrl+t to view)
╭──────────────────────────────────────────────────────────────────────────────────────────╮
│ > enting. all services in future will have one outbox table per serice . ask me          │
│   extra info, lets build things together .                                               │
│   /private/var/www/2025/ollamar1/beauty-crm/shared-platform-engineering/platform-ap      │
│   pointment-eventing should be used by                                                   │
│   /private/var/www/2025/ollamar1/beauty-crm/services/appointment/appointment-planne      │
│   r-backend                                                                              │
│   /private/var/www/2025/ollamar1/beauty-crm/services/appointment/appointment-manage      │
│   ment-backend. also make sure to use                                                    │
│   /private/var/www/2025/ollamar1/beauty-crm/shared-product-engineering/product-appo      │
│   intment-types in appoienmtnt services. lets discuss.                                   │
╰──────────────────────────────────────────────────────────────────────────────────────────╯

/private/var/www/2025/ollamar1/b no sandbox     gemini-2.5-pro (100%  | ✖ 2 errors (ctrl+o 
eauty-crm (main*)                (see /docs)   context left)            for details)

