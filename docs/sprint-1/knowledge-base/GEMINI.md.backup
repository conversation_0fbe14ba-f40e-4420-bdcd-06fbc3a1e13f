# Gemini Guidelines for the Beauty CRM Project

use mcp servers for db use prisma and pg mcp. for frontend mcp use playwrgight to see ui how rendered.             

use tilt trigger and tilt logs. read tiltfile.

This document provides guidelines for interacting with the Beauty CRM codebase. Adhering to these principles will ensure consistency, reliability, and maintainability.

## 1. Core Architecture: Event-Driven with NATS

The system is built on an **event-driven architecture (EDA)**.

*   **Event Bus:** We use **NATS JetStream** as the primary event bus for real-time, reliable messaging. Do not use Redis for eventing.
*   **Transactional Integrity:** The **Outbox pattern** is mandatory for publishing events from services that have a database. This ensures that an event is published if and only if the corresponding business transaction succeeds.
    1.  When a business entity is created/updated, save the corresponding event to an `outbox` table within the same database transaction.
    2.  A separate "relayer" process is responsible for reading from the outbox and publishing to NATS.
*   **Event Streams:** Events are organized into streams. The primary stream for appointments is `APPOINTMENT_EVENTS`.

## 2. Key Conventions & Libraries

*   **Unified Data Models:** All appointment-related data structures are defined in the `@beauty-crm/product-appointment-types` library.
    *   Use the `UnifiedAppointment` interface for all appointment data.
    *   Use the Zod schemas from this library for validation.
*   **Eventing Library:** Use the `@beauty-crm/platform-eventing` library for all NATS interactions.
    *   `EventPublisher` to send events.
    *   `EventSubscriber` to receive events.
*   **Standardized Events:** All events must conform to the `AppointmentEvent` interface, which extends the base `DomainEvent`. Key fields include `eventId`, `eventType`, `aggregateId`, `data`, `timestamp`, etc.


## 3. Service Roles

*   **Planner Backend:**
    *   Receives appointment booking requests from the Planner Frontend.
    *   Publishes `appointment.created`, `appointment.updated`, and `appointment.cancelled` events to NATS.
    *   Acts as an event source; it does not directly persist appointment data in its own database.
*   **Management Backend:**
    *   Subscribes to `APPOINTMENT_EVENTS` from NATS.
    *   Processes these events (e.g., `appointment.created`, `appointment.updated`, `appointment.cancelled`).
    *   Persists and updates appointment data in its own database.
    *   Responsible for conflict detection and maintaining the authoritative state of appointments.
*   **Frontend Applications (Planner & Management):**
    *   **Planner Frontend:** Provides the user interface for booking appointments and publishes `appointment.created` events directly to NATS.
    *   **Management Frontend:** Subscribes to appointment events from NATS to display real-time updates and management capabilities.

## 4. Development & Testing

*   **NASA-Grade Quality:** We adhere to extremely high-quality standards.
*   **Test Pyramid:**
    *   **Unit Tests:** For individual components.
    *   **Integration Tests:** To validate interactions between services via NATS.
    *   **E2E Tests:** To simulate full user journeys.
*   **Frontend Testing:**
    *   Use `data-testid` attributes on all interactive elements to facilitate reliable automated testing with tools like Playwright.
    *   Ensure mobile responsiveness and accessibility.
*   **Edge Cases:** Always consider and test for edge cases like race conditions, network failures, and data corruption.

## 5. Key Personas

When developing features, consider the needs of our key personas:

*   **End Users:**
    *   **Lisa Wong (Client):** Wants a seamless and reliable booking experience.
    *   **Sarah Johnson (Salon Owner):** Needs efficient management tools.
    *   **Markus Ronde (Stylist):** Needs a simple, clear, and reliable mobile interface.
*   **Technical Team:**
    *   **Rajiv Patel (Principal Engineer):** Cares about data consistency and backend reliability.
    *   **Ana Garcia (Principal Frontend Engineer):** Cares about localization, test coverage, and UI quality.
    *   **Dr. Sarah Mitchell (QA Lead):** Enforces zero-defect deployments.

By following these guidelines, you will help us build a robust, scalable, and high-quality Beauty CRM system.

## Pattern Templates & Runbooks

- **Pattern Templates:**
  - See `shared-platform-engineering/pattern-templates/` for reusable infrastructure patterns (Traefik YAML label templates, code-level resilience patterns, etc.).
  - Use these templates to standardize Compose and service configuration across the monorepo.
  - Patterns are continuously improved as new best practices and requirements emerge.

- **Runbooks:**
  - See `RUNBOOKS.md` in the project root for operational procedures, troubleshooting, and deployment guides.
  - This file is a living document and should be updated with lessons learned and new operational knowledge.

- **Continuous Improvement:**
  - Both pattern templates and runbooks are actively maintained. Always contribute new patterns, fixes, and operational insights to keep them current and useful.
