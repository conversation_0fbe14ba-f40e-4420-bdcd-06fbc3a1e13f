---
description: 
globs: Docker<PERSON>le,*.yml,*.yaml,Docker*,docker-compose.dev.yml,docker-compose.dev.yml,docker-compose.yml
alwaysApply: false
---
# Roo Code IDE Rules: Bun + Docker Best Practices

## Overview
These rules guide the development of Bun applications with Docker containerization, with special focus on monorepo structures and dependency management.

## Version Guidelines
- Use major version tags for production (e.g., `oven/bun:1`)
- Use exact versions for development to ensure consistency (e.g., `oven/bun:1.0.21`)
- Always align Bun versions across all services in a monorepo

## Build-time vs Runtime Guidelines

### Build-time Operations
```dockerfile
# Base stage for shared dependencies
FROM oven/bun:1 AS base
WORKDIR /usr/src/monorepo
COPY package.json bun.lock ./
RUN bun install --frozen-lockfile

# Build stage for service-specific dependencies and build
FROM base AS builder
WORKDIR /usr/src/monorepo/services/myservice
COPY services/myservice/package.json ./
RUN bun install --frozen-lockfile
COPY services/myservice ./
RUN bun run build

# Production stage
FROM oven/bun:1-slim AS production
WORKDIR /usr/src/app
COPY --from=builder /usr/src/monorepo/services/myservice/dist ./dist
CMD ["bun", "run", "start"]
```

### Development Dockerfile
```dockerfile
FROM oven/bun:1 AS dev
WORKDIR /usr/src/monorepo

# Install root dependencies at build time
COPY package.json bun.lock ./
RUN bun install --frozen-lockfile

# Install service dependencies at build time
COPY services/myservice/package.json ./services/myservice/
WORKDIR /usr/src/monorepo/services/myservice
RUN bun install --frozen-lockfile

# Source mounted at runtime
VOLUME ["/usr/src/monorepo"]
CMD ["bun", "run", "dev"]
```

### Docker Compose Service Template
```yaml
services:
  myservice:
    build:
      context: ../..
      dockerfile: services/myservice/Dockerfile.dev
      args:
        - NODE_ENV=development
    volumes:
      - ../..:/usr/src/monorepo:ro
      - node_modules:/usr/src/monorepo/node_modules
      - service_modules:/usr/src/monorepo/services/myservice/node_modules
    environment:
      - NODE_ENV=development
    command: bun run dev
```

### Volume Management
```yaml
volumes:
  node_modules:
  service_modules:
```

## Dependency Installation Rules

1. **Build-time Installation**
   - ALL dependencies MUST be installed during image build
   - NEVER run `bun install` in container start command
   - Use multi-stage builds to separate build and runtime
   - Cache dependencies in named volumes

2. **Development Mode**
   - Mount source code as read-only
   - Use named volumes for node_modules
   - Pre-install dependencies in Dockerfile
   - Only mount necessary paths

3. **Production Mode**
   - Copy only built artifacts
   - Use slim base images
   - No development dependencies
   - No source code mounting

## Script Standards
```json
{
  "scripts": {
    "dev": "Development script",
    "start": "Production script",
    "build": "Build script",
    "test": "Test script"
  }
}
```

## Database Integration

### PostgreSQL Service
```yaml
services:
  db:
    image: postgres:15-alpine
    environment:
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_DB: ${DB_NAME}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready"]
      interval: 10s
```

## Error Recovery

### Common Issues
1. **Build-time Errors**
   - Clear build cache: `docker builder prune`
   - Rebuild without cache: `docker build --no-cache`
   - Check for missing build args

2. **Runtime Errors**
   - Check volume permissions
   - Verify environment variables
   - Inspect container logs

### Recovery Steps
1. Remove containers and volumes
2. Rebuild images
3. Start services
4. Check logs

## Security Best Practices

### Container Security
1. **Use non-root user**
```dockerfile
USER bun
```

2. **Read-only Filesystem**
```yaml
security_opt:
  - no-new-privileges:true
read_only: true
```

3. **Environment Variables**
   - Use .env.example
   - Use secrets in production
   - Never commit sensitive data

## Development Workflow

### Local Development
```bash
# Build images
docker compose build

# Start services
docker compose up -d

# View logs
docker compose logs -f

# Rebuild single service
docker compose build myservice
docker compose up -d myservice
```

### Debugging
```bash
# Access container
docker compose exec myservice sh

# View logs
docker compose logs myservice

# Check health
docker compose ps
```

## Monorepo Structure
```
project/
├── package.json
├── bun.lock
├── services/
│   └── myservice/
│       ├── Dockerfile
│       ├── Dockerfile.dev
│       └── package.json
└── docker-compose.yml
```

## Health Monitoring
```yaml
healthcheck:
  test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
  interval: 10s
```

## Monorepo Docker Configuration Rules

### 1. **Bun Version Consistency**
- **Always use consistent Bun versions across services**
  - Use `oven/bun:1.0.21` for precise version matching
  - Specify exact versions in Dockerfiles
  - Maintain version alignment in `bun.lock`

### 2. **Dockerfile Structure**
- **Implement Multi-Stage Builds**
  ```dockerfile
  # Builder Stage
  FROM oven/bun:1.0.21 AS builder
  WORKDIR /usr/src/app
  
  # Copy entire monorepo
  COPY . .
  
  # Install dependencies
  RUN bun install --frozen-lockfile
  
  # Build application
  RUN bun run build
  
  # Production Stage
  FROM oven/bun:1.0.21-slim
  WORKDIR /usr/src/app
  
  # Copy only necessary artifacts
  COPY --from=builder /usr/src/app/dist ./dist
  COPY --from=builder /usr/src/app/node_modules ./node_modules
  ```

### 3. **Dependency Management**
- **Centralized Dependency Installation**
  - Use `Dockerfile.deps` for root-level dependency management
  - Leverage `bun install --frozen-lockfile`
  - Cache dependencies using named volumes
  ```yaml
  volumes:
    node_modules_cache:
      driver: local
  ```

### 4. **Docker Compose Configuration**
- **Service Dependency Management**
  ```yaml
  services:
    deps:
      build:
        context: ../..
        dockerfile: Dockerfile.deps
      volumes:
        - node_modules_cache:/usr/src/monorepo/node_modules
    
    service_name:
      build:
        context: ../..
        dockerfile: services/service_name/Dockerfile
      volumes:
        - node_modules_cache:/usr/src/app/node_modules
      depends_on:
        - deps
  ```

### 5. **Development vs Production Separation**
- **Distinct Configurations**
  - Separate Dockerfiles for development and production
  - Use environment-specific build arguments
  - Mount source code as read-only in development

### 6. **Performance Optimization**
- **Caching Strategies**
  - Copy `package.json` and `bun.lock` first
  - Install dependencies before copying source code
  - Use `.dockerignore` to exclude unnecessary files

### 7. **Security Practices**
- **Container Hardening**
  ```dockerfile
  # Run as non-root user
  USER bun
  
  # Read-only filesystem
  FROM oven/bun:1.0.21-slim
  RUN chmod a-w /
  ```

### 8. **Health Monitoring**
- **Implement Health Checks**
  ```yaml
  healthcheck:
    test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
    interval: 10s
    timeout: 5s
    retries: 3
    start_period: 20s
  ```

### 9. **Error Handling**
- **Graceful Startup and Shutdown**
  - Use proper signal handling
  - Implement restart policies
  ```yaml
  restart: unless-stopped
  ```

### 10. **Logging and Monitoring**
- **Centralized Logging**
  ```yaml
  logging:
    driver: "json-file"
    options:
      max-size: "10m"
      max-file: "3"
  ```

## Common Pitfalls to Avoid
- ❌ Running `bun install` in container start command
- ❌ Copying entire monorepo without `.dockerignore`
- ❌ Inconsistent Bun versions across services
- ❌ Mounting entire source code in production

## Recommended Workflow
```bash
# Build dependencies
docker compose build deps

# Build and start services
docker compose up -d

# Rebuild specific service
docker compose build service_name
docker compose up -d service_name
```

These rules ensure proper separation of build-time and runtime operations, optimizing both development and production workflows while maintaining security and performance best practices.

# Docker Configuration Best Practices for Bun Monorepo

## 1. **Base Image Selection**
- **Always use official Bun images**
  ```dockerfile
  # ✅ DO: Use specific Bun version
  FROM oven/bun:1.2.15 AS base
  
  # ❌ DON'T: Use generic or unspecified versions
  FROM oven/bun:latest
  ```

## 2. **Multi-Stage Build Patterns**
```dockerfile
# Recommended Multi-Stage Dockerfile Template
FROM oven/bun:1.2.15 AS deps
WORKDIR /usr/src/app

# Install root dependencies
COPY package.json bun.lock ./
RUN bun install --frozen-lockfile

FROM oven/bun:1.2.15 AS builder
WORKDIR /usr/src/app

# Copy dependencies and source
COPY --from=deps /usr/src/app/node_modules ./node_modules
COPY . .

# Build application
RUN bun run build

FROM oven/bun:1.2.15-slim AS production
WORKDIR /usr/src/app

# Copy only necessary artifacts
COPY --from=builder /usr/src/app/dist ./dist
COPY --from=builder /usr/src/app/node_modules ./node_modules

CMD ["bun", "run", "start"]
```

## 3. **Dependency Management Rules**
- **Consistent Dependency Installation**
  ```bash
  # ✅ DO: Use frozen lockfile
  bun install --frozen-lockfile

  # ❌ DON'T: Loose dependency installation
  bun install
  ```

## 4. **Security Hardening**
- **Non-Root User Configuration**
  ```dockerfile
  # Create non-root user
  RUN addgroup --system --gid 1001 bun
  RUN adduser --system --uid 1001 bun

  # Switch to non-root user
  USER bun
  ```

## 5. **Docker Compose Best Practices**
```yaml


volumes:
  # Named volumes for consistent caching
  node_modules_cache:
    driver: local
  bun_cache:
    driver: local

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      # Read-only source mounting
      - .:/usr/src/app:ro
      # Persistent dependency caching
      - node_modules_cache:/usr/src/app/node_modules
      - bun_cache:/bun-cache
    environment:
      - NODE_ENV=development
    ports:
      - "3000:3000"
    depends_on:
      - deps
```

## 6. **Performance Optimization**
- **Caching Strategies**
  ```dockerfile
  # Copy package files first to leverage build cache
  COPY package.json bun.lock ./
  RUN bun install --frozen-lockfile

  # Then copy source code
  COPY . .
  ```

## 7. **Health Checking**
```yaml
services:
  app:
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
```

## 8. **Environment-Specific Configurations**
```dockerfile
# Use build arguments for environment-specific builds
ARG NODE_ENV=production
ENV NODE_ENV=${NODE_ENV}

# Conditional dependency installation
RUN if [ "$NODE_ENV" = "development" ]; then \
      bun install; \
    else \
      bun install --production; \
    fi
```

## 9. **Common Pitfalls to Avoid**
- ❌ Running `bun install` in container start command
- ❌ Not using multi-stage builds
- ❌ Running containers as root user
- ❌ Ignoring build caching strategies

## 10. **Recommended Workflow**
```bash
# Build dependencies
docker compose build deps

# Start services
docker compose up -d

# Rebuild specific service
docker compose build service_name
docker compose up -d service_name
```

## Continuous Improvement Triggers
- New Bun runtime versions
- Security patches
- Performance optimization opportunities
- Emerging containerization best practices

## Docker Configuration Standards

### File Organization
- **Separate Configuration Scripts**
  ```bash
  # ✅ DO: Use separate scripts for common Docker operations
  COPY scripts/docker-copy-configs.sh .
  RUN ./docker-copy-configs.sh
  
  # ❌ DON'T: Repeat copy commands in Dockerfile
  COPY package.json bun.lock ./
  COPY tsconfig.base.json ./
  ```

### User Permissions
- **Standardized User Setup**
  ```bash
  # ✅ DO: Use the docker-user-permissions.sh script
  COPY scripts/docker-user-permissions.sh .
  RUN ./docker-user-permissions.sh
  
  # ❌ DON'T: Repeat user creation commands
  RUN groupadd --system --gid 1001 bun
  ```

### Health Checks
- **Consistent Health Check Configuration**
  ```dockerfile
  # ✅ DO: Use the docker-healthcheck.sh script
  COPY scripts/docker-healthcheck.sh /usr/local/bin/
  HEALTHCHECK --interval=10s --timeout=5s --start-period=20s \
    CMD ["/usr/local/bin/docker-healthcheck.sh"]
  
  # ❌ DON'T: Hardcode health check commands
  HEALTHCHECK CMD curl -f http://localhost:5018/health
  ```

### Build Stages
- **Standard Stage Names**
  - deps: For dependency installation
  - development: For development environment
  - builder: For building the application
  - production: For production environment

### Image Versions
- **Standardized Base Images**
  - Backend: `oven/bun:1.2.15-slim`
  - Frontend: `nginx:1.25-alpine`

### Dependencies
- **Installation Best Practices**
  ```dockerfile
  # ✅ DO: Install dependencies at build time
  RUN bun install --frozen-lockfile
  
  # ❌ DON'T: Install at runtime
  CMD ["bun", "install"]
  ```

### Security
- **Non-root User**
  - Always run containers as non-root user
  - Use standardized UID/GID (1001)


## Tasks

### Extracted Tasks

- [ ] Use major version tags for production (e.g., `oven/bun:1`) - M1
- [ ] Use exact versions for development to ensure consistency (e.g., `oven/bun:1.0.21`) - M2
- [ ] Always align Bun versions across all services in a monorepo - M3
- [ ] NODE_ENV=development - M4
- [ ] ../..:/usr/src/monorepo:ro - M5
- [ ] node_modules:/usr/src/monorepo/node_modules - M6
- [ ] service_modules:/usr/src/monorepo/services/myservice/node_modules - M7
- [ ] NODE_ENV=development - M8
- [ ] ALL dependencies MUST be installed during image build - M9
- [ ] NEVER run `bun install` in container start command - M10
- [ ] Use multi-stage builds to separate build and runtime - M11
- [ ] Cache dependencies in named volumes - M12
- [ ] Mount source code as read-only - M13
- [ ] Use named volumes for node_modules - M14
- [ ] Pre-install dependencies in Dockerfile - M15
- [ ] Only mount necessary paths - M16
- [ ] Copy only built artifacts - M17
- [ ] Use slim base images - M18
- [ ] No development dependencies - M19
- [ ] No source code mounting - M20
- [ ] postgres_data:/var/lib/postgresql/data - M21
- [ ] Clear build cache: `docker builder prune` - M22
- [ ] Rebuild without cache: `docker build --no-cache` - M23
- [ ] Check for missing build args - M24
- [ ] Check volume permissions - M25
- [ ] Verify environment variables - M26
- [ ] Inspect container logs - M27
- [ ] no-new-privileges:true - M28
- [ ] Use .env.example - M29
- [ ] Use secrets in production - M30
- [ ] Never commit sensitive data - M31
- [ ] **Always use consistent Bun versions across services** - M32
- [ ] Use `oven/bun:1.0.21` for precise version matching - M33
- [ ] Specify exact versions in Dockerfiles - M34
- [ ] Maintain version alignment in `bun.lock` - M35
- [ ] **Implement Multi-Stage Builds** - M36
- [ ] **Centralized Dependency Installation** - M37
- [ ] Use `Dockerfile.deps` for root-level dependency management - M38
- [ ] Leverage `bun install --frozen-lockfile` - M39
- [ ] Cache dependencies using named volumes - M40
- [ ] **Service Dependency Management** - M41
- [ ] node_modules_cache:/usr/src/monorepo/node_modules - M42
- [ ] node_modules_cache:/usr/src/app/node_modules - M43
- [ ] **Distinct Configurations** - M44
- [ ] Separate Dockerfiles for development and production - M45
- [ ] Use environment-specific build arguments - M46
- [ ] Mount source code as read-only in development - M47
- [ ] **Caching Strategies** - M48
- [ ] Copy `package.json` and `bun.lock` first - M49
- [ ] Install dependencies before copying source code - M50
- [ ] Use `.dockerignore` to exclude unnecessary files - M51
- [ ] **Container Hardening** - M52
- [ ] **Implement Health Checks** - M53
- [ ] **Graceful Startup and Shutdown** - M54
- [ ] Use proper signal handling - M55
- [ ] Implement restart policies - M56
- [ ] **Centralized Logging** - M57
- [ ] ❌ Running `bun install` in container start command - M58
- [ ] ❌ Copying entire monorepo without `.dockerignore` - M59
- [ ] ❌ Inconsistent Bun versions across services - M60
- [ ] ❌ Mounting entire source code in production - M61
- [ ] **Always use official Bun images** - M62
- [ ] **Consistent Dependency Installation** - M63
- [ ] **Non-Root User Configuration** - M64
- [ ] .:/usr/src/app:ro - M65
- [ ] node_modules_cache:/usr/src/app/node_modules - M66
- [ ] bun_cache:/bun-cache - M67
- [ ] NODE_ENV=development - M68
- [ ] "3000:3000" - M69
- [ ] **Caching Strategies** - M70
- [ ] ❌ Running `bun install` in container start command - M71
- [ ] ❌ Not using multi-stage builds - M72
- [ ] ❌ Running containers as root user - M73
- [ ] ❌ Ignoring build caching strategies - M74
- [ ] New Bun runtime versions - M75
- [ ] Security patches - M76
- [ ] Performance optimization opportunities - M77
- [ ] Emerging containerization best practices - M78
- [ ] **Separate Configuration Scripts** - M79
- [ ] **Standardized User Setup** - M80
- [ ] **Consistent Health Check Configuration** - M81
- [ ] **Standard Stage Names** - M82
- [ ] deps: For dependency installation - M83
- [ ] development: For development environment - M84
- [ ] builder: For building the application - M85
- [ ] production: For production environment - M86
- [ ] **Standardized Base Images** - M87
- [ ] Backend: `oven/bun:1.2.15-slim` - M88
- [ ] Frontend: `nginx:1.25-alpine` - M89
- [ ] **Installation Best Practices** - M90
- [ ] **Non-root User** - M91
- [ ] Always run containers as non-root user - M92
- [ ] Use standardized UID/GID (1001) - M93

### Documentation Tasks

- [ ] Update content accuracy - M1
- [ ] Add code examples - M2
- [ ] Add diagrams/screenshots - M3
- [ ] Review and proofread - M4
- [ ] Add cross-references - M5

