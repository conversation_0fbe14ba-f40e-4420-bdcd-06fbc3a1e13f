# Beauty CRM Docker Architecture Documentation

## Overview

This document explains the complex Docker networking architecture of the Beauty CRM system, including Traefik routing, Docker Compose orchestration, network segmentation, and service discovery.

## 🏗️ High-Level Architecture

```mermaid
graph TB
    subgraph "Host Machine (macOS)"
        subgraph "Local Services"
            TILT[Tilt Development UI<br/>127.0.0.1:10350]
            HOSTS[/etc/hosts<br/>Domain Mappings]
        end
        
        subgraph "Docker Desktop"
            subgraph "Traefik Networks"
                PUB[beauty_crm_traefik-public<br/>Web Services]
                PRIV[beauty_crm_traefik-private<br/>Database Services]
            end
            
            subgraph "Application Networks"
                BACKEND[backend<br/>Internal Communication]
                PROXY[proxy<br/>Service Proxies]
            end
        end
    end
    
    BROWSER[Web Browser] --> HOSTS
    HOSTS --> PUB
    PUB --> TRAEFIK[Traefik Reverse Proxy<br/>:80, :8080]
    TRAEFIK --> SERVICES[Application Services]
    TILT --> TILT_PROXY[Tilt Proxy Container]
    TILT_PROXY --> TRAEFIK
```

## 🌐 Network Architecture

```mermaid
graph LR
    subgraph "Network Segmentation"
        subgraph "Public Network (beauty_crm_traefik-public)"
            TRAEFIK[Traefik<br/>:80, :8080, :8082]
            DASHY[Dashy Dashboard<br/>:8080]
            TILT_PROXY[Tilt Proxy<br/>:80]
            SALON_FE[Salon Frontend Proxy<br/>:80]
            SALON_BE[Salon Backend Proxy<br/>:80]
        end
        
        subgraph "Private Network (beauty_crm_traefik-private)"
            POSTGRES[PostgreSQL<br/>:5432]
            REDIS[Redis<br/>:6379]
            NATS[NATS<br/>:4222, :8222]
        end
        
        subgraph "Backend Network"
            SALON_APP[Salon Management<br/>Backend :3001]
            SALON_WEB[Salon Management<br/>Frontend :3000]
        end
    end
    
    TRAEFIK --> SALON_FE
    TRAEFIK --> SALON_BE
    SALON_FE --> SALON_WEB
    SALON_BE --> SALON_APP
    SALON_APP --> POSTGRES
    SALON_APP --> REDIS
```

## 🔀 Traefik Routing Configuration

```mermaid
graph TD
    subgraph "Traefik Router Rules"
        subgraph "Domain Routing"
            BEAUTY[beauty-crm.localhost] --> MAIN_ROUTER[Main Router]
            TILT_DOM[tilt.localhost] --> TILT_ROUTER[Tilt Router]
            TRAEFIK_DOM[traefik.localhost] --> TRAEFIK_ROUTER[Traefik Dashboard Router]
            DASHBOARD[dashboard.localhost] --> DASHY_ROUTER[Dashy Router]
        end
        
        subgraph "Service Routing"
            MAIN_ROUTER --> |"/salons/"| SALON_SERVICE[Salon Frontend Proxy]
            MAIN_ROUTER --> |"/treatments/"| TREATMENT_SERVICE[Treatment Frontend Proxy]
            MAIN_ROUTER --> |"/staff/"| STAFF_SERVICE[Staff Frontend Proxy]
            MAIN_ROUTER --> |"/api/salons/"| SALON_API[Salon Backend Proxy]
        end
        
        subgraph "External Service Routing"
            TILT_ROUTER --> TILT_PROXY_SERVICE[Tilt Proxy Container]
            TILT_PROXY_SERVICE --> |"host.docker.internal:10350"| TILT_LOCAL[Local Tilt Instance]
        end
    end
```

## 🔌 Port Mapping and CIDR Blocks

```mermaid
graph TB
    subgraph "Host Ports (0.0.0.0)"
        HOST_80[":80 → Traefik HTTP"]
        HOST_8080[":8080 → Traefik Dashboard"]
        HOST_8082[":8082 → Prometheus Metrics"]
        HOST_5433[":5433 → PostgreSQL Proxy"]
        HOST_6380[":6380 → Redis Proxy"]
        HOST_10350[":10350 → Tilt (Local)"]
    end

    subgraph "Container Internal Ports"
        subgraph "Infrastructure Services"
            TRAEFIK_INTERNAL[Traefik: 80, 8080, 8082]
            POSTGRES_INTERNAL[PostgreSQL: 5432]
            REDIS_INTERNAL[Redis: 6379]
            NATS_INTERNAL[NATS: 4222, 8222]
        end

        subgraph "Application Services"
            SALON_BE_INTERNAL[Salon Backend: 3001]
            SALON_FE_INTERNAL[Salon Frontend: 3000]
            DASHY_INTERNAL[Dashy: 8080]
        end

        subgraph "Proxy Services"
            NGINX_PROXIES[Nginx Proxies: 80]
        end
    end

    HOST_80 --> TRAEFIK_INTERNAL
    HOST_8080 --> TRAEFIK_INTERNAL
    HOST_5433 --> POSTGRES_INTERNAL
    HOST_6380 --> REDIS_INTERNAL
```

## 📁 Docker Compose File Structure

```mermaid
graph TD
    subgraph "Docker Compose Organization"
        subgraph "Infrastructure Layer"
            DB_COMPOSE[docker-compose.databases.yml<br/>PostgreSQL, Redis, NATS]
            PROXY_COMPOSE[docker-compose.proxy.yml<br/>Traefik, Dashy, Tilt-Proxy]
            APM_COMPOSE[docker-compose.apm.yml<br/>SigNoz Monitoring]
        end

        subgraph "Application Layer"
            SALON_COMPOSE[services/salon/docker-compose.app.yml<br/>Salon Management + Proxies]
            TREATMENT_COMPOSE[services/treatment/docker-compose.app.yml<br/>Treatment Management + Proxies]
            STAFF_COMPOSE[services/staff/docker-compose.app.yml<br/>Staff Management + Proxies]
        end

        subgraph "Orchestration"
            TILTFILE[Tiltfile<br/>Service Dependencies & Build Rules]
            TILT_LOCAL[Local Tilt Process<br/>Orchestrates All Services]
        end
    end

    TILT_LOCAL --> DB_COMPOSE
    TILT_LOCAL --> PROXY_COMPOSE
    TILT_LOCAL --> SALON_COMPOSE
    DB_COMPOSE --> PROXY_COMPOSE
    PROXY_COMPOSE --> SALON_COMPOSE
```

## 🔗 Service Dependencies and Startup Order

```mermaid
graph TD
    subgraph "Startup Sequence"
        subgraph "Phase 1: Infrastructure"
            NETWORKS[Create Docker Networks]
            POSTGRES[PostgreSQL Database]
            REDIS[Redis Cache]
            NATS[NATS Messaging]
        end

        subgraph "Phase 2: Reverse Proxy"
            TRAEFIK[Traefik Reverse Proxy]
            DASHY[Dashy Dashboard]
        end

        subgraph "Phase 3: Application Services"
            SALON_BACKEND[Salon Management Backend]
            SALON_FRONTEND[Salon Management Frontend]
        end

        subgraph "Phase 4: API Gateway Proxies"
            SALON_FE_PROXY[Salon Frontend Proxy]
            SALON_BE_PROXY[Salon Backend Proxy]
            TILT_PROXY[Tilt Proxy]
        end
    end

    NETWORKS --> POSTGRES
    NETWORKS --> REDIS
    NETWORKS --> NATS
    POSTGRES --> TRAEFIK
    REDIS --> TRAEFIK
    NATS --> TRAEFIK
    TRAEFIK --> DASHY
    TRAEFIK --> SALON_BACKEND
    SALON_BACKEND --> SALON_FRONTEND
    SALON_FRONTEND --> SALON_FE_PROXY
    SALON_BACKEND --> SALON_BE_PROXY
    TRAEFIK --> TILT_PROXY
```

## � /etc/hosts Configuration

```mermaid
graph LR
    subgraph "/etc/hosts Domain Mappings"
        subgraph "Main Application Domains"
            BEAUTY[beauty-crm.localhost → 127.0.0.1]
            TRAEFIK_HOST[traefik.localhost → 127.0.0.1]
            DASHBOARD_HOST[dashboard.localhost → 127.0.0.1]
            TILT_HOST[tilt.localhost → 127.0.0.1]
        end

        subgraph "Database Aliases"
            DB_HOST[db.localhost → 127.0.0.1]
            REDIS_HOST[redis.localhost → 127.0.0.1]
            NATS_HOST[nats.localhost → 127.0.0.1]
        end

        subgraph "Service-Specific Domains"
            SALON_HOST[salon-frontend.localhost → 127.0.0.1]
            TREATMENT_HOST[treatment-frontend.localhost → 127.0.0.1]
            STAFF_HOST[staff-frontend.localhost → 127.0.0.1]
        end
    end

    subgraph "Browser Resolution"
        BROWSER[Web Browser] --> BEAUTY
        BROWSER --> TRAEFIK_HOST
        BROWSER --> DASHBOARD_HOST
        BROWSER --> TILT_HOST
    end

    BEAUTY --> TRAEFIK_CONTAINER[Traefik Container :80]
    TRAEFIK_HOST --> TRAEFIK_CONTAINER
    DASHBOARD_HOST --> TRAEFIK_CONTAINER
    TILT_HOST --> TRAEFIK_CONTAINER
```

## 🔧 Detailed Service Configuration

### Traefik Reverse Proxy
```yaml
# Key Configuration
Ports: 80 (HTTP), 8080 (Dashboard), 8082 (Metrics)
Networks: traefik-public, traefik-private, backend, proxy
Labels:
  - traefik.http.routers.dashboard.rule=Host(`traefik.localhost`)
  - traefik.http.routers.dashboard.service=api@internal
```

### Tilt Proxy (External Service Bridge)
```yaml
# Bridges local Tilt to Docker network
Container: beauty_crm_tilt_proxy
Upstream: host.docker.internal:10350
Networks: traefik-public, backend, proxy
Labels:
  - traefik.http.routers.tilt.rule=Host(`tilt.localhost`)
  - traefik.http.services.tilt.loadbalancer.server.port=80
```

### Salon Management Services
```yaml
# Backend Service
salon-management-backend:
  Port: 3001
  Networks: backend
  Dependencies: postgres, redis

# Frontend Service
salon-management-frontend:
  Port: 3000
  Networks: backend
  Dependencies: salon-management-backend

# Frontend Proxy
salon-frontend-proxy:
  Port: 80
  Networks: traefik-public, backend, proxy
  Labels:
    - traefik.http.routers.salon-frontend.rule=Host(`beauty-crm.localhost`) && PathPrefix(`/salons/`)
```

## �🏷️ Docker Labels and Service Discovery

```mermaid
graph TB
    subgraph "Traefik Label System"
        subgraph "Router Labels"
            ROUTER_RULE["traefik.http.routers.{name}.rule"]
            ROUTER_SERVICE["traefik.http.routers.{name}.service"]
            ROUTER_ENTRY["traefik.http.routers.{name}.entrypoints"]
        end
        
        subgraph "Service Labels"
            SERVICE_PORT["traefik.http.services.{name}.loadbalancer.server.port"]
            SERVICE_URL["traefik.http.services.{name}.loadbalancer.server.url"]
        end
        
        subgraph "Network Labels"
            DOCKER_NET["traefik.docker.network"]
            ENABLE["traefik.enable=true"]
        end
    end
    
    subgraph "Example: Salon Frontend Proxy"
        SALON_LABELS[
            "traefik.enable=true"<br/>
            "traefik.http.routers.salon-frontend.rule=Host(`beauty-crm.localhost`) && PathPrefix(`/salons/`)"<br/>
            "traefik.http.services.salon-frontend.loadbalancer.server.port=80"<br/>
            "traefik.docker.network=beauty_crm_traefik-public"
        ]
    end
```
