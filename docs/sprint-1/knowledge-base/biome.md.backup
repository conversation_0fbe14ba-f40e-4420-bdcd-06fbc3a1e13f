---
description: 
globs: *.ts,*.tsx,*.js,*.jsx,*.json
alwaysApply: false
---
# Use Biome for Linting, Formatting, and Code Quality - Immediate Setup

## Rule Name: biome_immediate_setup

## Description: 

Enforce proper Biome configuration and usage in all projects for consistent code formatting, linting, and to prevent common issues. Configure Biome immediately when starting any project to avoid downstream problems.

## Rule Content

Always use Biome for TypeScript, JavaScript, JSON, and CSS formatting and linting.
Configure Biome immediately when starting any new project or working on existing projects.
All code must pass Biome checks before being considered complete.
Immediate Biome Setup

## Installation:

Install Biome as the first step in any project:
bash# Using npm
npm install --save-dev @biomejs/biome

# Using pnpm
pnpm add -D @biomejs/biome

# Using yarn
yarn add -D @biomejs/biome

# Using bun
bun add -d @biomejs/biome

Configuration File (biome.json):

Create biome.json in project root immediately:
json{
  "$schema": "https://biomejs.dev/schemas/1.9.4/schema.json",
  "vcs": {
    "enabled": true,
    "clientKind": "git",
    "useIgnoreFile": true
  },
  "files": {
    "ignoreUnknown": false,
    "ignore": ["dist/**", "build/**", "node_modules/**", ".next/**", "coverage/**"]
  },
  "formatter": {
    "enabled": true,
    "indentStyle": "space",
    "indentWidth": 2,
    "lineWidth": 100,
    "lineEnding": "lf"
  },
  "organizeImports": {
    "enabled": true
  },
  "linter": {
    "enabled": true,
    "rules": {
      "recommended": true,
      "correctness": {
        "noUnusedVariables": "error",
        "useValidForDirection": "error",
        "useYield": "error"
      },
      "suspicious": {
        "noExplicitAny": "warn",
        "noArrayIndexKey": "warn",
        "useAwait": "error"
      },
      "style": {
        "useConst": "error",
        "useTemplate": "error",
        "noNegationElse": "error"
      },
      "complexity": {
        "noExcessiveCognitiveComplexity": "warn",
        "noMultipleSpacesInRegularExpressionLiterals": "error"
      },
      "security": {
        "noDangerouslySetInnerHtml": "error"
      },
      "nursery": {
        "useSortedClasses": "error"
      }
    }
  },
  "javascript": {
    "formatter": {
      "quoteStyle": "single",
      "jsxQuoteStyle": "double",
      "semicolons": "always",
      "trailingCommas": "es5",
      "bracketSpacing": true,
      "bracketSameLine": false,
      "arrowParentheses": "asNeeded"
    }
  },
  "json": {
    "formatter": {
      "enabled": true
    },
    "parser": {
      "allowComments": true
    }
  }
}

## Package.json Scripts:

Add these scripts immediately:
json{
  "scripts": {
    "lint": "biome lint .",
    "lint:fix": "biome lint --write .",
    "format": "biome format .",
    "format:fix": "biome format --write --fix .",
    "check": "biome check .",
    "check:fix": "biome check --write .",
    "ci": "biome ci ."
  }
}

## VSCode Integration (Roo Code)

Install Biome Extension:
Install the official Biome VSCode extension immediately.
VSCode Settings (.vscode/settings.json):
Create this file in project root:
json{
  "editor.defaultFormatter": "biomejs.biome",
  "editor.formatOnSave": true,
  "editor.formatOnPaste": true,
  "editor.formatOnType": true,
  "editor.codeActionsOnSave": {
    "quickfix.biome": "explicit",
    "source.organizeImports.biome": "explicit"
  },
  "[javascript]": {
    "editor.defaultFormatter": "biomejs.biome"
  },
  "[javascriptreact]": {
    "editor.defaultFormatter": "biomejs.biome"
  },
  "[typescript]": {
    "editor.defaultFormatter": "biomejs.biome"
  },
  "[typescriptreact]": {
    "editor.defaultFormatter": "biomejs.biome"
  },
  "[json]": {
    "editor.defaultFormatter": "biomejs.biome"
  },
  "[jsonc]": {
    "editor.defaultFormatter": "biomejs.biome"
  }
}

## Pre-commit Setup (Optional but Recommended)

Using Husky + lint-staged:
bashnpm install --save-dev husky lint-staged
Add to package.json:
json{
  "lint-staged": {
    "*.{js,jsx,ts,tsx,json}": [
      "biome check --write --no-errors-on-unmatched"
    ]
  }
}
Immediate Actions for Every Project
Install Biome as first development dependency
Create biome.json with above configuration
Add npm scripts for linting and formatting
Setup VSCode settings for automatic formatting
Run initial check: npm run check:fix to format all existing code
Verify setup: Ensure all files are properly formatted and linted

## What Biome Prevents

Inconsistent code formatting across team members
Runtime errors from unused variables and imports
Performance issues from inefficient code patterns
Security vulnerabilities from dangerous patterns
Import organization problems
Style inconsistencies
Common JavaScript/TypeScript pitfalls

## Migration from Other Tools

From ESLint + Prettier:

Remove ESLint and Prettier dependencies
Delete .eslintrc.* and .prettierrc.* files
Install and configure Biome as above
Run biome check --write . to format entire codebase
Update CI/CD pipelines to use biome ci

From other formatters:
Replace all formatter configurations with Biome setup above.
Best Practices

Run biome check . before every commit
Configure CI to fail on Biome errors
Use biome check --write . for bulk fixes
Keep biome.json configuration consistent across projects
Update Biome regularly for latest features and fixes
Use useSortedClasses rule for Tailwind CSS class sorting

## Troubleshooting

If Biome conflicts with existing tools, remove the conflicting tools first
For large codebases, run biome check --write . in smaller chunks
Use biome rage command to get system information for debugging
Check Biome documentation for latest configuration options

## DO NOT

Skip Biome setup on new projects
Mix Biome with ESLint/Prettier in the same project
Ignore Biome errors - fix them immediately
Use different formatting configurations across team projects
Disable important rules without team discussion
