---
description: This rule enforces best practices and coding standards for Playwright tests, e2e tests
globs: **/*.spec.ts,**/*.test.ts,*.test.ts,*.spec.ts
alwaysApply: false
---
---
description: This rule enforces best practices and coding standards for Playwright tests, including stable selectors, test isolation, user-centric testing, and performance considerations.
globs: **/*.spec.ts,**/*.test.ts
---
- **General Principles**
  - **Test User-Visible Behavior:** Focus tests on how users interact with your application, not on internal implementation details.
  - **Isolate Tests:** Ensure tests are independent of each other to prevent cascading failures and ensure predictable results.
  - **Avoid Testing Third-Party Dependencies:** Mock or stub external services and APIs to isolate your application's behavior.
  - ** Monorepo, first identify playwright configs inside sub packages in monorepo, do not create general playwrite configs in root
   
- **Code Organization and Structure**
  - **Directory Structure:**
    - `**/tests/`: Contains all test files.
    - `**/tests/e2e/`: End-to-end tests.
    - `**/tests/unit/`: Unit tests (if applicable, though <PERSON><PERSON> is primarily for E2E).
    - `**/tests/utils/`: Helper functions and page object models.
  - **File Naming Conventions:**
    - Use `.spec.ts` or `.spec.js` for test files (e.g., `login.spec.ts`).
    - Group related tests in the same file.
  - **Module Organization:**
    - Employ Page Object Model (POM) to encapsulate UI elements and interactions.
  - **Component Architecture:**
    - Structure tests around components or features of your application.
  - **Code Splitting Strategies:**
    - Not directly applicable to tests, but keep test files concise and focused.

- **Common Patterns and Anti-patterns**
  - **Design Patterns:**
    - **Page Object Model (POM):** A common pattern where each page is represented as a class, with methods for interacting with the page's elements.  This improves reusability and maintainability. Example:
      typescript
      class LoginPage {
        constructor(private readonly page: Page) {}

        async goto() {
          await this.page.goto('/login');
        }

        async login(username: string, password: string) {
          await this.page.fill('#username', username);
          await this.page.fill('#password', password);
          await this.page.click('#login-button');
        }

        async getErrorMessage() {
          return await this.page.textContent('#error-message');
        }
      }
      
    - **Fixture pattern:** Use Playwright's built-in fixtures to manage test setup and teardown. This ensures each test starts in a clean state.
  - **Recommended Approaches:**
    - Use `baseURL` in `playwright.config.ts` to avoid hardcoding URLs in tests.
    - Utilize `expect` matchers for assertions (e.g., `expect(page.locator('#success')).toBeVisible()`).
    - Use auto-waiting features for improved stability.
  - **Anti-patterns:**
    - Hardcoding URLs.
    - Using brittle selectors (e.g., XPath based on DOM structure).
    - Writing tests that depend on each other.
  - **State Management:**
    - Keep tests stateless. Reset the application state before each test.
    - Use database transactions or API calls to seed data for tests.
  - **Error Handling:**
    - Use `try...catch` blocks to handle expected errors.
    - Log errors and failures with descriptive messages.
    - Use `expect.soft()` for non-critical assertions that shouldn't fail the test immediately.

- **Performance Considerations**
  - **Optimization Techniques:**
    - Run tests in parallel to reduce overall test execution time.
    - Use `reuseExistingServer: true` in `playwright.config.ts` during development to speed up debugging.
    - Use `codegen` to generate selectors automatically.
  - **Memory Management:**
    - Close pages and browsers after each test or group of tests to release resources.
  - **Rendering Optimization:**
    - Not directly applicable but optimize your application's rendering for faster testing.
  - **Bundle Size Optimization:**
    - Not directly applicable, but optimize your application's bundle size for faster loading.
  - **Lazy Loading Strategies:**
    - Not directly applicable to tests.

- **Security Best Practices**
  - **Common Vulnerabilities:**
    - Avoid exposing sensitive data (e.g., passwords, API keys) in test code or logs.
  - **Input Validation:**
    - Test input validation to ensure your application handles invalid data correctly.
  - **Authentication and Authorization:**
    - Test different user roles and permissions.
  - **Data Protection:**
    - Ensure sensitive data is encrypted in the database.
  - **Secure API Communication:**
    - Test that API calls are made over HTTPS.

- **Testing Approaches**
  - **Unit Testing:**
    - While Playwright primarily focuses on E2E testing, unit tests can be written for utility functions or components.
  - **Integration Testing:**
    - Test the interaction between different parts of your application.
  - **End-to-End Testing:**
    - Simulate user flows to test the entire application.
  - **Test Organization:**
    - Group tests by feature or functionality.
    - Use `describe` blocks to organize tests.
  - **Mocking and Stubbing:**
    - Use Playwright's `route` API to mock API responses.
    - Use `locator.evaluate` to stub JavaScript functions.

- **Common Pitfalls and Gotchas**
  - **Frequent Mistakes:**
    - Using XPath instead of CSS selectors.
    - Not using auto-waiting features.
    - Writing flaky tests.
  - **Edge Cases:**
    - Handling different screen sizes and devices.
    - Testing error conditions and edge cases.
  - **Version-Specific Issues:**
    - Stay up-to-date with Playwright's release notes and upgrade guides.
  - **Compatibility Concerns:**
    - Test on different browsers and operating systems.
  - **Debugging Strategies:**
    - Use Playwright Inspector to debug tests visually.
    - Use `console.log` statements to log information during test execution.
    - Use `pause()` to halt test execution and inspect the page.

- **Tooling and Environment**
  - **Recommended Development Tools:**
    - VS Code with the Playwright extension.
  - **Build Configuration:**
    - Use TypeScript for type safety and autocompletion.
  - **Linting and Formatting:**
    - Use ESLint and Prettier to enforce code style.
  - **Deployment Best Practices:**
    - Run tests in CI/CD pipeline before deploying to production.
  - **CI/CD Integration:**
    - Integrate Playwright with CI/CD tools like GitHub Actions, Jenkins, or GitLab CI.

- **Specific Best Practices & Details**
    - **Stable Selectors:** Prefer CSS selectors based on attributes like `data-testid` or `data-test-id` over XPath or fragile CSS classnames.
    - **Leverage Auto-waiting:** Playwright automatically waits for elements to be actionable before performing actions.  Avoid explicit waits where possible. However, use explicit waits (e.g. `waitForSelector`) when necessary.
    - **Web-First Assertions:** Use `expect` assertions, which retry and wait for conditions to be met. They help to avoid flakiness.
    - **Configure Debugging Highlights:**  Configure `playwright.config.ts` to highlight actions performed by playwright in the browser during debugging to see what's happening step by step. Example:
        typescript
        use: {
            /* Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer */
            trace: 'on-first-retry',
            video: 'on',
            screenshot: 'only-on-failure',
        }
        

- **Additional Notes**
    - Regularly review and update your test suite to reflect changes in your application.
    - Document your tests to make them easier to understand and maintain.
    - Use a consistent naming convention for your tests.

## Tasks

### Extracted Tasks

- [ ] **General Principles** - M1
- [ ] **Test User-Visible Behavior:** Focus tests on how users interact with your application, not on internal implementation details. - M2
- [ ] **Isolate Tests:** Ensure tests are independent of each other to prevent cascading failures and ensure predictable results. - M3
- [ ] **Avoid Testing Third-Party Dependencies:** Mock or stub external services and APIs to isolate your application's behavior. - M4
- [ ] ** Monorepo, first identify playwright configs inside sub packages in monorepo, do not create general playwrite configs in root - M5
- [ ] **Code Organization and Structure** - M6
- [ ] **Directory Structure:** - M7
- [ ] `**/tests/`: Contains all test files. - M8
- [ ] `**/tests/e2e/`: End-to-end tests. - M9
- [ ] `**/tests/unit/`: Unit tests (if applicable, though Playwright is primarily for E2E). - M10
- [ ] `**/tests/utils/`: Helper functions and page object models. - M11
- [ ] **File Naming Conventions:** - M12
- [ ] Use `.spec.ts` or `.spec.js` for test files (e.g., `login.spec.ts`). - M13
- [ ] Group related tests in the same file. - M14
- [ ] **Module Organization:** - M15
- [ ] Employ Page Object Model (POM) to encapsulate UI elements and interactions. - M16
- [ ] **Component Architecture:** - M17
- [ ] Structure tests around components or features of your application. - M18
- [ ] **Code Splitting Strategies:** - M19
- [ ] Not directly applicable to tests, but keep test files concise and focused. - M20
- [ ] **Common Patterns and Anti-patterns** - M21
- [ ] **Design Patterns:** - M22
- [ ] **Page Object Model (POM):** A common pattern where each page is represented as a class, with methods for interacting with the page's elements.  This improves reusability and maintainability. Example: - M23
- [ ] **Fixture pattern:** Use Playwright's built-in fixtures to manage test setup and teardown. This ensures each test starts in a clean state. - M24
- [ ] **Recommended Approaches:** - M25
- [ ] Use `baseURL` in `playwright.config.ts` to avoid hardcoding URLs in tests. - M26
- [ ] Utilize `expect` matchers for assertions (e.g., `expect(page.locator('#success')).toBeVisible()`). - M27
- [ ] Use auto-waiting features for improved stability. - M28
- [ ] **Anti-patterns:** - M29
- [ ] Hardcoding URLs. - M30
- [ ] Using brittle selectors (e.g., XPath based on DOM structure). - M31
- [ ] Writing tests that depend on each other. - M32
- [ ] **State Management:** - M33
- [ ] Keep tests stateless. Reset the application state before each test. - M34
- [ ] Use database transactions or API calls to seed data for tests. - M35
- [ ] **Error Handling:** - M36
- [ ] Use `try...catch` blocks to handle expected errors. - M37
- [ ] Log errors and failures with descriptive messages. - M38
- [ ] Use `expect.soft()` for non-critical assertions that shouldn't fail the test immediately. - M39
- [ ] **Performance Considerations** - M40
- [ ] **Optimization Techniques:** - M41
- [ ] Run tests in parallel to reduce overall test execution time. - M42
- [ ] Use `reuseExistingServer: true` in `playwright.config.ts` during development to speed up debugging. - M43
- [ ] Use `codegen` to generate selectors automatically. - M44
- [ ] **Memory Management:** - M45
- [ ] Close pages and browsers after each test or group of tests to release resources. - M46
- [ ] **Rendering Optimization:** - M47
- [ ] Not directly applicable but optimize your application's rendering for faster testing. - M48
- [ ] **Bundle Size Optimization:** - M49
- [ ] Not directly applicable, but optimize your application's bundle size for faster loading. - M50
- [ ] **Lazy Loading Strategies:** - M51
- [ ] Not directly applicable to tests. - M52
- [ ] **Security Best Practices** - M53
- [ ] **Common Vulnerabilities:** - M54
- [ ] Avoid exposing sensitive data (e.g., passwords, API keys) in test code or logs. - M55
- [ ] **Input Validation:** - M56
- [ ] Test input validation to ensure your application handles invalid data correctly. - M57
- [ ] **Authentication and Authorization:** - M58
- [ ] Test different user roles and permissions. - M59
- [ ] **Data Protection:** - M60
- [ ] Ensure sensitive data is encrypted in the database. - M61
- [ ] **Secure API Communication:** - M62
- [ ] Test that API calls are made over HTTPS. - M63
- [ ] **Testing Approaches** - M64
- [ ] **Unit Testing:** - M65
- [ ] While Playwright primarily focuses on E2E testing, unit tests can be written for utility functions or components. - M66
- [ ] **Integration Testing:** - M67
- [ ] Test the interaction between different parts of your application. - M68
- [ ] **End-to-End Testing:** - M69
- [ ] Simulate user flows to test the entire application. - M70
- [ ] **Test Organization:** - M71
- [ ] Group tests by feature or functionality. - M72
- [ ] Use `describe` blocks to organize tests. - M73
- [ ] **Mocking and Stubbing:** - M74
- [ ] Use Playwright's `route` API to mock API responses. - M75
- [ ] Use `locator.evaluate` to stub JavaScript functions. - M76
- [ ] **Common Pitfalls and Gotchas** - M77
- [ ] **Frequent Mistakes:** - M78
- [ ] Using XPath instead of CSS selectors. - M79
- [ ] Not using auto-waiting features. - M80
- [ ] Writing flaky tests. - M81
- [ ] **Edge Cases:** - M82
- [ ] Handling different screen sizes and devices. - M83
- [ ] Testing error conditions and edge cases. - M84
- [ ] **Version-Specific Issues:** - M85
- [ ] Stay up-to-date with Playwright's release notes and upgrade guides. - M86
- [ ] **Compatibility Concerns:** - M87
- [ ] Test on different browsers and operating systems. - M88
- [ ] **Debugging Strategies:** - M89
- [ ] Use Playwright Inspector to debug tests visually. - M90
- [ ] Use `console.log` statements to log information during test execution. - M91
- [ ] Use `pause()` to halt test execution and inspect the page. - M92
- [ ] **Tooling and Environment** - M93
- [ ] **Recommended Development Tools:** - M94
- [ ] VS Code with the Playwright extension. - M95
- [ ] **Build Configuration:** - M96
- [ ] Use TypeScript for type safety and autocompletion. - M97
- [ ] **Linting and Formatting:** - M98
- [ ] Use ESLint and Prettier to enforce code style. - M99
- [ ] **Deployment Best Practices:** - M100
- [ ] Run tests in CI/CD pipeline before deploying to production. - M101
- [ ] **CI/CD Integration:** - M102
- [ ] Integrate Playwright with CI/CD tools like GitHub Actions, Jenkins, or GitLab CI. - M103
- [ ] **Specific Best Practices & Details** - M104
- [ ] **Stable Selectors:** Prefer CSS selectors based on attributes like `data-testid` or `data-test-id` over XPath or fragile CSS classnames. - M105
- [ ] **Leverage Auto-waiting:** Playwright automatically waits for elements to be actionable before performing actions.  Avoid explicit waits where possible. However, use explicit waits (e.g. `waitForSelector`) when necessary. - M106
- [ ] **Web-First Assertions:** Use `expect` assertions, which retry and wait for conditions to be met. They help to avoid flakiness. - M107
- [ ] **Configure Debugging Highlights:**  Configure `playwright.config.ts` to highlight actions performed by playwright in the browser during debugging to see what's happening step by step. Example: - M108
- [ ] **Additional Notes** - M109
- [ ] Regularly review and update your test suite to reflect changes in your application. - M110
- [ ] Document your tests to make them easier to understand and maintain. - M111
- [ ] Use a consistent naming convention for your tests. - M112

### Documentation Tasks

- [ ] Update content accuracy - M1
- [ ] Add code examples - M2
- [ ] Add diagrams/screenshots - M3
- [ ] Review and proofread - M4
- [ ] Add cross-references - M5

