# Beauty CRM Platform

## Overview

This document provides a visual overview of the Beauty CRM platform architecture, codebase statistics, and system design. The platform is built using modern technologies with a focus on Domain-Driven Design (DDD), microservices architecture, and scalability.

## Codebase Statistics

<div align="center">

### Total Lines of Code
- **Total Lines**: 1,663,165
- **Source Code**: 819,784
- **Tests**: 9,506
- **Configuration**: 434,128
- **Documentation**: 399,747

### Technology Distribution
- **TypeScript/JavaScript Files**: 165,893
- **Test Files**: 252
- **Documentation Files**: 8,722

### Codebase Composition

![Codebase Composition](./assets/codebase-composition.svg)

</div>

## Architecture Overview

<div align="center">

### System Architecture

```mermaid
graph TD
    Client[Client Applications] --> ApiGateway[API Gateway]
    
    subgraph "Frontend Layer"
        ApiGateway --> SalonUI[Salon Management UI]
        ApiGateway --> AppointmentUI[Appointment Planner UI]
        ApiGateway --> AppointmentMgmtUI[Appointment Management UI]
        ApiGateway --> IdentityUI[Identity Management UI]
    end
    
    subgraph "Backend Services"
        SalonUI --> SalonService[Salon Management Service]      
        AppointmentUI --> AppointmentService[Appointment Planner Service]
        AppointmentMgmtUI --> AppointmentMgmtService[Appointment Management Service]
        IdentityUI --> IdentityService[Identity Service]
        
        SalonService --> SalonDomain[Salon Domain]  
        AppointmentService --> AppointmentDomain[Appointment Domain]
        AppointmentMgmtService --> AppointmentMgmtDomain[Appointment Management Domain]
        IdentityService --> IdentityDomain[Identity Domain]
    end
    
    subgraph "Shared Platform Engineering"
        PlatformLogger[Platform Logger]
        PlatformDB[Platform DB Client]
        PlatformUtilities[Platform Utilities]
        PlatformIdentity[Platform Identity Client]
        PlatformRuntime[Platform Computing Runtime]
        PlatformLifecycle[Platform Computing Lifecycle]
        PlatformUI[Platform Introvertic UI]
        NeuralMCP[Neural MCP]
    end
    
    subgraph "Shared Product Engineering"
        ProductKernel[Product Kernel]
        ProductTypes[Product Domain Types]
        ProductIdentityTypes[Product Identity Types]
        ProductResponses[Product Responses]
        ProductTranslations[Product Translations]
    end
    
    subgraph "DDD Layers"
        DomainLayer[Domain Layer]
        ApplicationLayer[Application Layer]
        InfrastructureLayer[Infrastructure Layer]
        PresentationLayer[Presentation Layer]
    end
    
    subgraph "Data Layer"
        SalonDomain --> DB[(Neon PostgreSQL)]
        AppointmentDomain --> DB
        AppointmentMgmtDomain --> DB
        IdentityDomain --> DB
        
        AppointmentService --> Redis[(Redis Cache)]
        AppointmentMgmtService --> Redis
    end
    
    style Client fill:#f9f9f9,stroke:#999,stroke-width:1px
    style ApiGateway fill:#ff9800,stroke:#e65100,stroke-width:2px,color:white
    style SalonUI fill:#2196f3,stroke:#0d47a1,stroke-width:1px,color:white
    style AppointmentUI fill:#2196f3,stroke:#0d47a1,stroke-width:1px,color:white
    style AppointmentMgmtUI fill:#2196f3,stroke:#0d47a1,stroke-width:1px,color:white
    style IdentityUI fill:#2196f3,stroke:#0d47a1,stroke-width:1px,color:white
    style SalonService fill:#4caf50,stroke:#1b5e20,stroke-width:1px,color:white
    style AppointmentService fill:#4caf50,stroke:#1b5e20,stroke-width:1px,color:white
    style AppointmentMgmtService fill:#4caf50,stroke:#1b5e20,stroke-width:1px,color:white
    style IdentityService fill:#4caf50,stroke:#1b5e20,stroke-width:1px,color:white
    style DB fill:#673ab7,stroke:#311b92,stroke-width:2px,color:white
    style Redis fill:#dc382d,stroke:#b71c1c,stroke-width:2px,color:white
    style SalonDomain fill:#8bc34a,stroke:#558b2f,stroke-width:1px
    style AppointmentDomain fill:#8bc34a,stroke:#558b2f,stroke-width:1px
    style AppointmentMgmtDomain fill:#8bc34a,stroke:#558b2f,stroke-width:1px
    style IdentityDomain fill:#8bc34a,stroke:#558b2f,stroke-width:1px
```

</div>

## Domain Model

<div align="center">

### Core Domain Model

```mermaid
classDiagram
    class Salon {
        +String id
        +String name
        +SalonSettings settings
        +Date createdAt
        +Array~User~ users
        +createUser()
        +updateSettings()
        +manageBilling()
    }
    
    class User {
        +String id
        +String email
        +String hashedPassword
        +UserProfile profile
        +Boolean isActive
        +Array~Session~ sessions
        +authenticate()
        +resetPassword()
        +updateProfile()
    }
    
    class Customer {
        +String id
        +String name
        +ContactInfo contactInfo
        +PreferenceSettings preferences
        +Array~Appointment~ appointments
        +CustomerHistory history
        +updateProfile()
        +makeAppointment()
        +viewHistory()
    }
    
    class Appointment {
        +String id
        +DateTime startTime
        +DateTime endTime
        +AppointmentStatus status
        +Array~Service~ services
        +Customer customer
        +Staff staff
        +AppointmentNotes notes
        +reschedule()
        +cancel()
        +complete()
        +addNotes()
    }
    
    class Service {
        +String id
        +String name
        +String description
        +Money price
        +Duration duration
        +Array~Category~ categories
        +ServiceRequirements requirements
        +updatePricing()
        +setDuration()
    }
    
    class Staff {
        +String id
        +String name
        +Array~ServiceCapability~ capabilities
        +WorkSchedule schedule
        +Array~Appointment~ appointments
        +StaffProfile profile
        +updateSchedule()
        +addCapability()
    }
    
    class AppointmentPlanner {
        +String id
        +PlannerSettings settings
        +Array~TimeSlot~ availableSlots
        +BookingRules rules
        +findAvailableSlots()
        +validateBooking()
        +optimizeSchedule()
    }
    
    Salon "1" -- "many" User : contains
    Salon "1" -- "many" Staff : employs
    User "1" -- "many" Session : has
    Customer "1" -- "many" Appointment : books
    Appointment "many" -- "many" Service : includes
    Appointment "many" -- "1" Staff : assigned to
    Service "many" -- "many" Category : belongs to
    AppointmentPlanner "1" -- "many" Appointment : manages
    
    class Category {
        +String id
        +String name
        +String description
        +CategorySettings settings
    }
    
    class Session {
        +String id
        +DateTime createdAt
        +DateTime expiresAt
        +String deviceInfo
        +SessionMetadata metadata
        +revoke()
        +refresh()
    }
```

</div>

## System Workflow

<div align="center">

### Customer Appointment Flow

```mermaid
sequenceDiagram
    participant C as Customer
    participant UI as Appointment Planner UI
    participant API as API Gateway
    participant APS as Appointment Planner Service
    participant AMS as Appointment Management Service
    participant NS as Notification Service
    participant DB as Database
    participant Redis as Redis Cache
    
    C->>UI: Browse available services
    UI->>API: GET /services
    API->>APS: Request available services
    APS->>Redis: Check cache
    Redis-->>APS: Cache miss
    APS->>DB: Query services
    DB-->>APS: Return services data
    APS->>Redis: Cache services
    APS-->>API: Services response
    API-->>UI: Display services
    
    C->>UI: Select service & time slot
    UI->>API: POST /appointments
    API->>APS: Create appointment request
    APS->>DB: Check staff availability
    DB-->>APS: Availability confirmed
    APS->>AMS: Coordinate appointment creation
    AMS->>DB: Save appointment
    DB-->>AMS: Appointment created
    AMS->>NS: Trigger notifications
    NS->>DB: Get notification templates
    DB-->>NS: Return templates
    NS-->>AMS: Notification queued
    AMS-->>APS: Appointment confirmed
    APS-->>API: Appointment response
    API-->>UI: Confirmation screen
    
    Note over NS,DB: Async notification process
    NS->>C: Send email confirmation
    NS->>C: Send SMS reminder (24h before)
    
    rect rgb(240, 248, 255)
    Note over C,DB: Day of Appointment
    C->>UI: Check-in
    UI->>API: POST /appointments/check-in
    API->>AMS: Update appointment status
    AMS->>DB: Update status
    DB-->>AMS: Status updated
    AMS-->>API: Success response
    API-->>UI: Display confirmation
    end
```

</div>

## Technology Stack

<div align="center">

### Technology Stack Overview

```mermaid
graph LR
    subgraph "Frontend Technologies"
        TypeScript["TypeScript"]
        React["React"]
        Vite["Vite"]
        CSS["CSS/SCSS"]
        NX["NX Monorepo"]
    end

    subgraph "Backend Technologies"
        Hono["Hono Framework"]
        Node["Node.js/Bun"]
        TS2["TypeScript"]
        Vitest["Vitest"]
        DDD["Domain-Driven Design"]
    end

    subgraph "Database & Cache"
        Neon["Neon PostgreSQL"]
        Prisma["Prisma ORM"]
        Redis["Redis Cache"]
    end

    subgraph "Development & DevOps"
        Biome["Biome Linter/Formatter"]
        CDKTF["CDKTF"]
        FlyIO["Fly.io"]
        Docker["Docker Compose"]
        MegaLinter["MegaLinter"]
    end

    subgraph "Platform Engineering"
        PlatformLogger["Platform Logger"]
        PlatformDB["Platform DB Client"]
        PlatformUtilities["Platform Utilities"]
        NeuralMCP["Neural MCP"]
    end

    Frontend --> Backend
    Backend --> Database
    Frontend -.-> Development
    Backend -.-> Development
    Backend -.-> Platform
    
    classDef frontend fill:#42a5f5,stroke:#1976d2,color:white
    classDef backend fill:#66bb6a,stroke:#43a047,color:white
    classDef database fill:#ab47bc,stroke:#8e24aa,color:white
    classDef devops fill:#ff7043,stroke:#e64a19,color:white
    classDef platform fill:#26a69a,stroke:#00695c,color:white
    
    class TypeScript,React,Vite,CSS,NX frontend
    class Hono,Node,TS2,Vitest,DDD backend
    class Neon,Prisma,Redis database
    class Biome,CDKTF,FlyIO,Docker,MegaLinter devops
    class PlatformLogger,PlatformDB,PlatformUtilities,NeuralMCP platform
```

</div>

## Performance Metrics

<div align="center">

### System Performance Visualization

![Performance Metrics](./assets/performance-metrics.svg)

</div>

## Deployment Architecture

<div align="center">

### Deployment Topology

```mermaid
flowchart TB
    subgraph "Client Layer"
        Browser["Web Browsers"]
        Mobile["Mobile Apps"]
    end
    
    subgraph "Edge Network"
        CDN["CDN"]
    end
    
    subgraph "Load Balancers"
        LB["Load Balancers"]
    end
    
    subgraph "Service Layer"
        direction LR
        subgraph "Region 1"
            API1["API Gateway"]
            Salon1["Salon Service"]
            Appointment1["Appointment Service"]
            Identity1["Identity Service"]
        end
        
        subgraph "Region 2"
            API2["API Gateway"]
            Salon2["Salon Service"]
            Appointment2["Appointment Service"]
            Identity2["Identity Service"]
        end
    end
    
    subgraph "Data Layer"
        PG["Neon PostgreSQL"]
        PGRO["PostgreSQL Read Replicas"]
        Redis["Redis Cache Cluster"]
    end
    
    Browser --> CDN
    Mobile --> CDN
    CDN --> LB
    LB --> API1
    LB --> API2
    API1 --> Salon1
    API1 --> Appointment1
    API1 --> Identity1
    API2 --> Salon2
    API2 --> Appointment2
    API2 --> Identity2
    Salon1 --> PG
    Appointment1 --> PG
    Identity1 --> PG
    Salon2 --> PG
    Appointment2 --> PG
    Identity2 --> PG
    Appointment1 -.-> Redis
    Appointment2 -.-> Redis
    Identity1 -.-> Redis
    Identity2 -.-> Redis
    PG --> PGRO
    Appointment1 -.-> PGRO
    Appointment2 -.-> PGRO
    
    classDef client fill:#bbdefb,stroke:#2196f3,color:#333
    classDef edge fill:#ffcc80,stroke:#ff9800,color:#333
    classDef lb fill:#c5e1a5,stroke:#8bc34a,color:#333
    classDef service fill:#e1bee7,stroke:#9c27b0,color:#333
    classDef data fill:#ffecb3,stroke:#ffc107,color:#333
    classDef service1 fill:#d1c4e9,stroke:#673ab7,color:#333
    classDef service2 fill:#c5cae9,stroke:#3f51b5,color:#333
    
    class Browser,Mobile client
    class CDN edge
    class LB lb
    class API1,Salon1,Appointment1,Identity1 service1
    class API2,Salon2,Appointment2,Identity2 service2
    class PG,PGRO,Redis data
```

</div>

## Project Structure

<div align="center">

### Monorepo Organization

```mermaid
graph TD
    Root[Beauty CRM Root] --> Services[Services]
    Root --> SharedPlatform[Shared Platform Engineering]
    Root --> SharedProduct[Shared Product Engineering]
    Root --> SharedDDD[Shared DDD Layers]
    
    Services --> Salon[Salon Management]
    Services --> Appointment[Appointment Services]
    Services --> Identity[Identity Services]
    Services --> Inventory[Inventory Services]
    Services --> Orchestration[Orchestration Services]
    
    Salon --> SalonBackend[Salon Backend]
    Salon --> SalonFrontend[Salon Frontend]
    
    Appointment --> AppointmentPlanner[Appointment Planner]
    Appointment --> AppointmentManagement[Appointment Management]
    Appointment --> AppointmentSDK[Appointment SDK]
    
    SharedPlatform --> PlatformLogger[Platform Logger]
    SharedPlatform --> PlatformDB[Platform DB Client]
    SharedPlatform --> PlatformUtilities[Platform Utilities]
    SharedPlatform --> PlatformIdentity[Platform Identity Client]
    SharedPlatform --> NeuralMCP[Neural MCP]
    
    SharedProduct --> ProductKernel[Product Kernel]
    SharedProduct --> ProductTypes[Product Types]
    SharedProduct --> ProductResponses[Product Responses]
    
    SharedDDD --> DomainLayer[Domain Layer]
    SharedDDD --> ApplicationLayer[Application Layer]
    SharedDDD --> InfrastructureLayer[Infrastructure Layer]
    SharedDDD --> PresentationLayer[Presentation Layer]
    
    classDef services fill:#4caf50,stroke:#2e7d32,color:white
    classDef platform fill:#ff9800,stroke:#f57c00,color:white
    classDef product fill:#2196f3,stroke:#1976d2,color:white
    classDef ddd fill:#9c27b0,stroke:#7b1fa2,color:white
    
    class Services,Salon,Appointment,Identity,Inventory,Orchestration,SalonBackend,SalonFrontend,AppointmentPlanner,AppointmentManagement,AppointmentSDK services
    class SharedPlatform,PlatformLogger,PlatformDB,PlatformUtilities,PlatformIdentity,NeuralMCP platform
    class SharedProduct,ProductKernel,ProductTypes,ProductResponses product
    class SharedDDD,DomainLayer,ApplicationLayer,InfrastructureLayer,PresentationLayer ddd
```

</div>

## Project Roadmap

<div align="center">

### Development Roadmap

![Development Roadmap](./assets/development-roadmap.svg)

</div>

## Conclusion

The Beauty CRM platform represents a comprehensive solution for beauty industry businesses, built with modern enterprise-grade architecture. The platform leverages:

### Key Technical Features
* **Monorepo Architecture**: NX-powered workspace with 165,893+ TypeScript/JavaScript files
* **Domain-Driven Design**: Structured with dedicated DDD layers (Domain, Application, Infrastructure, Presentation)
* **Microservices**: Independent services for Salon Management, Appointment Planning, Identity Management
* **Modern Stack**: Hono framework, Bun runtime, Neon PostgreSQL, Redis caching
* **Platform Engineering**: Shared utilities, logging, database clients, and computing runtime
* **Quality Assurance**: 252 test files, Biome linting, MegaLinter integration

### Business Capabilities
* Multi-tenant architecture supporting various business sizes
* Comprehensive appointment planning and management system
* Advanced identity and access management
* Integrated customer relationship features
* Scalable infrastructure using Fly.io and Neon PostgreSQL

### Development Practices
* TypeScript-first development with strict typing
* Comprehensive testing with Vitest
* Automated code quality with Biome and MegaLinter
* Container-based development with Docker Compose
* Infrastructure as Code with CDKTF

The platform's architecture emphasizes scalability, maintainability, and developer experience while providing robust business functionality for the beauty industry.
