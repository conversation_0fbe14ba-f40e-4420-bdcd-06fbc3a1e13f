---
description: Guidelines for creating and maintaining Trae rules to ensure consistency and effectiveness.
globs: .trae/rules/*.md
alwaysApply: true
---

- **Required Rule Structure:**
  ```markdown
  ---
  description: Clear, one-line description of what the rule enforces
  globs: path/to/files/*.ext, other/path/**/*
  alwaysApply: boolean
  ---

  - **Main Points in Bold**
    - Sub-points with details
    - Examples and explanations
  ```

- **File References:**
  - Use `[filename](mdc:path/to/file)` ([filename](mdc:filename)) to reference files
  - Example: [prisma.md](.trae/rules/prisma.md) for rule references
  - Example: [schema.prisma](mdc:prisma/schema.prisma) for code references

- **Code Examples:**
  - Use language-specific code blocks
  ```typescript
  // ✅ DO: Show good examples
  const goodExample = true;
  
  // ❌ DON'T: Show anti-patterns
  const badExample = false;
  ```

- **Rule Content Guidelines:**
  - Start with high-level overview
  - Include specific, actionable requirements
  - Show examples of correct implementation
  - Reference existing code when possible
  - Keep rules DRY by referencing other rules

- **Rule Maintenance:**
  - Update rules when new patterns emerge
  - Add examples from actual codebase
  - Remove outdated patterns
  - Cross-reference related rules

- **Best Practices:**
  - Use bullet points for clarity
  - Keep descriptions concise
  - Include both DO and DON'T examples
  - Reference actual code over theoretical examples
  - Use consistent formatting across rules 

## Tasks

### Extracted Tasks

- [ ] **Required Rule Structure:** - M1
- [ ] **Main Points in Bold** - M2
- [ ] Sub-points with details - M3
- [ ] Examples and explanations - M4
- [ ] **File References:** - M5
- [ ] Use `[filename](mdc:path/to/file)` ([filename](mdc:filename)) to reference files - M6
- [ ] Example: [prisma.md](.trae/rules/prisma.md) for rule references - M7
- [ ] Example: [schema.prisma](mdc:prisma/schema.prisma) for code references - M8
- [ ] **Code Examples:** - M9
- [ ] Use language-specific code blocks - M10
- [ ] **Rule Content Guidelines:** - M11
- [ ] Start with high-level overview - M12
- [ ] Include specific, actionable requirements - M13
- [ ] Show examples of correct implementation - M14
- [ ] Reference existing code when possible - M15
- [ ] Keep rules DRY by referencing other rules - M16
- [ ] **Rule Maintenance:** - M17
- [ ] Update rules when new patterns emerge - M18
- [ ] Add examples from actual codebase - M19
- [ ] Remove outdated patterns - M20
- [ ] Cross-reference related rules - M21
- [ ] **Best Practices:** - M22
- [ ] Use bullet points for clarity - M23
- [ ] Keep descriptions concise - M24
- [ ] Include both DO and DON'T examples - M25
- [ ] Reference actual code over theoretical examples - M26
- [ ] Use consistent formatting across rules - M27

### Documentation Tasks

- [ ] Update content accuracy - M1
- [ ] Add code examples - M2
- [ ] Add diagrams/screenshots - M3
- [ ] Review and proofread - M4
- [ ] Add cross-references - M5

