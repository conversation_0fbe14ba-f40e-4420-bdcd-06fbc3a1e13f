---
description: Guidelines for continuously improving Trae rules based on emerging code patterns and best practices.
globs: **/*
alwaysApply: true
---

- **Rule Improvement Triggers:**
  - New code patterns not covered by existing rules
  - Repeated similar implementations across files
  - Common error patterns that could be prevented
  - New libraries or tools being used consistently
  - Emerging best practices in the codebase

- **Analysis Process:**
  - Compare new code with existing rules
  - Identify patterns that should be standardized
  - Look for references to external documentation
  - Check for consistent error handling patterns
  - Monitor test patterns and coverage

- **Rule Updates:**
  - **Add New Rules When:**
    - A new technology/pattern is used in 3+ files
    - Common bugs could be prevented by a rule
    - Code reviews repeatedly mention the same feedback
    - New security or performance patterns emerge

  - **Modify Existing Rules When:**
    - Better examples exist in the codebase
    - Additional edge cases are discovered
    - Related rules have been updated
    - Implementation details have changed

- **Example Pattern Recognition:**
  ```typescript
  // If you see repeated patterns like:
  const data = await prisma.user.findMany({
    select: { id: true, email: true },
    where: { status: 'ACTIVE' }
  });
  
  // Consider adding to [prisma.md](.trae/rules/prisma.md):
  // - Standard select fields
  // - Common where conditions
  // - Performance optimization patterns
  ```

- **Rule Quality Checks:**
  - Rules should be actionable and specific
  - Examples should come from actual code
  - References should be up to date
  - Patterns should be consistently enforced

- **Continuous Improvement:**
  - Monitor code review comments
  - Track common development questions
  - Update rules after major refactors
  - Add links to relevant documentation
  - Cross-reference related rules

- **Rule Deprecation:**
  - Mark outdated patterns as deprecated
  - Remove rules that no longer apply
  - Update references to deprecated rules
  - Document migration paths for old patterns

- **Documentation Updates:**
  - Keep examples synchronized with code
  - Update references to external docs
  - Maintain links between related rules
  - Document breaking changes
Follow [trae_rules.md](.trae/rules/trae_rules.md) for proper rule formatting and structure.


## Tasks

### Extracted Tasks

- [ ] **Rule Improvement Triggers:** - M1
- [ ] New code patterns not covered by existing rules - M2
- [ ] Repeated similar implementations across files - M3
- [ ] Common error patterns that could be prevented - M4
- [ ] New libraries or tools being used consistently - M5
- [ ] Emerging best practices in the codebase - M6
- [ ] **Analysis Process:** - M7
- [ ] Compare new code with existing rules - M8
- [ ] Identify patterns that should be standardized - M9
- [ ] Look for references to external documentation - M10
- [ ] Check for consistent error handling patterns - M11
- [ ] Monitor test patterns and coverage - M12
- [ ] **Rule Updates:** - M13
- [ ] **Add New Rules When:** - M14
- [ ] A new technology/pattern is used in 3+ files - M15
- [ ] Common bugs could be prevented by a rule - M16
- [ ] Code reviews repeatedly mention the same feedback - M17
- [ ] New security or performance patterns emerge - M18
- [ ] **Modify Existing Rules When:** - M19
- [ ] Better examples exist in the codebase - M20
- [ ] Additional edge cases are discovered - M21
- [ ] Related rules have been updated - M22
- [ ] Implementation details have changed - M23
- [ ] **Example Pattern Recognition:** - M24
- [ ] **Rule Quality Checks:** - M25
- [ ] Rules should be actionable and specific - M26
- [ ] Examples should come from actual code - M27
- [ ] References should be up to date - M28
- [ ] Patterns should be consistently enforced - M29
- [ ] **Continuous Improvement:** - M30
- [ ] Monitor code review comments - M31
- [ ] Track common development questions - M32
- [ ] Update rules after major refactors - M33
- [ ] Add links to relevant documentation - M34
- [ ] Cross-reference related rules - M35
- [ ] **Rule Deprecation:** - M36
- [ ] Mark outdated patterns as deprecated - M37
- [ ] Remove rules that no longer apply - M38
- [ ] Update references to deprecated rules - M39
- [ ] Document migration paths for old patterns - M40
- [ ] **Documentation Updates:** - M41
- [ ] Keep examples synchronized with code - M42
- [ ] Update references to external docs - M43
- [ ] Maintain links between related rules - M44
- [ ] Document breaking changes - M45

### Documentation Tasks

- [ ] Update content accuracy - M1
- [ ] Add code examples - M2
- [ ] Add diagrams/screenshots - M3
- [ ] Review and proofread - M4
- [ ] Add cross-references - M5

