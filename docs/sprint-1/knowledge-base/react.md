---
description: Comprehensive guide to React best practices, covering code organization, performance, security, testing, and common pitfalls. Adhering to these guidelines helps developers build maintainable, scalable, and high-performing React applications.
globs: *.ts,*.tsx
alwaysApply: false
---
# React Best Practices: A Comprehensive Guide
Read exising files and fix them first.
Use introvertic-ui /private/var/www/2025/ollamar1/beauty-crm/shared-platform-engineering/platform-introvertic-ui for components instead of using html tags.

This document outlines the best practices for developing React applications, covering various aspects from code organization to security and testing. Following these guidelines leads to more maintainable, scalable, and performant applications.

## 1. Code Organization and Structure

### 1.1 Directory Structure

A well-defined directory structure is crucial for maintainability. Here's a recommended structure:

    - **Feature-based:** Group files related to a specific feature within a dedicated directory.
    
    src/
    ├── feature1/
    |   ├── contexts/
    │   │   ├── AuthContext.jsx
    │   │   └── ThemeContext.jsx
    │   ├── components/ 
    │   │   ├── ComponentA.tsx
    │   │   └── ComponentB.tsx
    │   ├── types.ts
    │   └── feature1.service.ts
    ├── feature2/ 
    │   └── ...
    └── shared/
        ├── components/
        │   └── ReusableComponent.tsx
        ├── services/
        │   └── shared-connector.service.ts
        └── types/
            └── global.d.ts


-   **`components/`**: Reusable UI components.
    -   Each component has its own directory containing the component file, associated styles (using CSS modules), and tests.
-   **`contexts/`**: React context providers.
-   **`hooks/`**: Custom React hooks.
-   **`pages/`**: Top-level components representing different routes or views.
-   **`services/`**: API interaction logic.
-   **`utils/`**: Utility functions.

### 1.2 File Naming Conventions

-   **Components**: Use PascalCase (e.g., `MyComponent.jsx`).
-   **Hooks**: Use camelCase prefixed with `use` (e.g., `useMyHook.js`).
-   **Contexts**: Use PascalCase suffixed with `Context` (e.g., `MyContext.jsx`).
-   **Services/Utils**: Use camelCase (e.g., `apiService.js`, `stringUtils.js`).
-   **CSS Modules**: Use `.module.css` or `.module.scss` (e.g., `Button.module.css`).

### 1.3 Module Organization

-   **Co-location**: Keep related files (component, styles, tests) together in the same directory.
-   **Single Responsibility**: Each module should have a clear and specific purpose.
-   **Avoid Circular Dependencies**: Ensure modules don't depend on each other in a circular manner.

### 1.4 Component Architecture

-   **Atomic Design**: Consider using Atomic Design principles (Atoms, Molecules, Organisms, Templates, Pages) to structure components.
-   **Composition over Inheritance**: Favor component composition to reuse code and functionality.
-   **Presentational and Container Components**: Separate UI rendering (presentational) from state management and logic (container).

### 1.5 Code Splitting Strategies

-   **Route-Based Splitting**: Use `React.lazy` and `Suspense` to load components only when a specific route is accessed.  This is very common and improves initial load time.
-   **Component-Based Splitting**: Split large components into smaller chunks that can be loaded on demand.
-   **Bundle Analyzer**: Use a tool like `webpack-bundle-analyzer` to identify large dependencies and optimize bundle size.

## 2. Common Patterns and Anti-patterns

### 2.1 Design Patterns

-   **Higher-Order Components (HOCs)**: Reusable logic that wraps components (use with caution; prefer hooks).
-   **Render Props**: Sharing code using a prop whose value is a function.
-   **Compound Components**: Components that work together implicitly (e.g., `Tabs`, `Tab`).
-   **Hooks**: Reusable stateful logic that can be shared across functional components.

### 2.2 Recommended Approaches

-   **Form Handling**: Use controlled components with local state or a form library like Formik or React Hook Form.
-   **API Calls**: Use `useEffect` hook to make API calls and manage loading states.
-   **Conditional Rendering**: Use short-circuit evaluation (`&&`) or ternary operators for simple conditions; use separate components for complex scenarios.
-   **List Rendering**: Always provide a unique and stable `key` prop when rendering lists.

### 2.3 Anti-patterns and Code Smells

-   **Direct DOM Manipulation**: Avoid directly manipulating the DOM; let React handle updates.
-   **Mutating State Directly**: Always use `setState` or the state updater function to modify state.
-   **Inline Styles**: Use CSS modules or styled-components for maintainable styles.
-   **Over-Engineering**: Avoid using complex solutions for simple problems.
-   **Prop Drilling**: Passing props through multiple levels of components without them being used.

### 2.4 State Management Best Practices

-   **Local State**: Use `useState` for component-specific state.
-   **Context API**: Use `useContext` for global state accessible to many components, but avoid for very frequently updated data.
-   **Redux/Mobx**: Use these libraries for complex state management in large applications.
-   **Recoil/Zustand**: Lightweight alternatives to Redux, often easier to set up and use.
-   **Immutable Data**: Treat state as immutable to prevent unexpected side effects.

### 2.5 Error Handling Patterns

-   **Error Boundaries**: Wrap components with error boundaries to catch errors during rendering and prevent crashes.
-   **Try-Catch Blocks**: Use try-catch blocks for handling errors in asynchronous operations and event handlers.
-   **Centralized Error Logging**: Implement a centralized error logging service to track errors and improve application stability.

## 3. Performance Considerations

### 3.1 Optimization Techniques

-   **Memoization**: Use `React.memo`, `useMemo`, and `useCallback` to prevent unnecessary re-renders and recalculations.
-   **Virtualization**: Use libraries like `react-window` or `react-virtualized` to efficiently render large lists or tables.
-   **Debouncing/Throttling**: Limit the rate at which functions are executed (e.g., in input fields).
-   **Code Splitting**: Load code on demand using `React.lazy` and `Suspense`.

### 3.2 Memory Management

-   **Avoid Memory Leaks**: Clean up event listeners, timers, and subscriptions in `useEffect`'s cleanup function.
-   **Release Unused Objects**: Avoid holding onto large objects in memory when they are no longer needed.
-   **Garbage Collection**: Understand how JavaScript's garbage collection works and avoid creating unnecessary objects.

### 3.3 Rendering Optimization

-   **Minimize State Updates**: Avoid unnecessary state updates that trigger re-renders.
-   **Batch Updates**: Batch multiple state updates into a single update using `ReactDOM.unstable_batchedUpdates`.
-   **Keys**: Ensure that keys are unique and consistent across renders.

### 3.4 Bundle Size Optimization

-   **Tree Shaking**: Remove unused code during the build process.
-   **Minification**: Reduce the size of JavaScript and CSS files.
-   **Image Optimization**: Compress and optimize images to reduce file size.
-   **Dependency Analysis**: Use tools like `webpack-bundle-analyzer` to identify large dependencies.

### 3.5 Lazy Loading Strategies

-   **Route-Based Lazy Loading**: Load components when a user navigates to a specific route.
-   **Component-Based Lazy Loading**: Load components when they are about to be rendered.
-   **Intersection Observer**: Load components when they become visible in the viewport.

## 4. Security Best Practices

### 4.1 Common Vulnerabilities and Prevention

-   **Cross-Site Scripting (XSS)**: Sanitize user input to prevent malicious code injection.
-   **Cross-Site Request Forgery (CSRF)**: Use anti-CSRF tokens to protect against unauthorized requests.
-   **Denial of Service (DoS)**: Implement rate limiting and request validation to prevent abuse.
-   **Injection Attacks**: Avoid directly embedding user input into database queries or system commands.

### 4.2 Input Validation

-   **Client-Side Validation**: Validate user input in the browser to provide immediate feedback.
-   **Server-Side Validation**: Always validate user input on the server to prevent malicious data.
-   **Sanitize Input**: Sanitize user input to remove potentially harmful characters or code.

### 4.3 Authentication and Authorization

-   **Secure Authentication**: Use secure authentication mechanisms like OAuth 2.0 or JWT.
-   **Role-Based Access Control (RBAC)**: Implement RBAC to control access to resources based on user roles.
-   **Multi-Factor Authentication (MFA)**: Enable MFA to add an extra layer of security.

### 4.4 Data Protection Strategies

-   **Encryption**: Encrypt sensitive data at rest and in transit.
-   **Data Masking**: Mask sensitive data in logs and UI displays.
-   **Regular Backups**: Create regular backups of application data.

### 4.5 Secure API Communication

-   **HTTPS**: Use HTTPS to encrypt communication between the client and the server.
-   **API Keys**: Protect API keys and secrets.
-   **CORS**: Configure Cross-Origin Resource Sharing (CORS) to prevent unauthorized access to APIs.

## 5. Testing Approaches

### 5.1 Unit Testing

-   **Test Components**: Test individual components in isolation.
-   **Testing Library**: Use React Testing Library for UI testing, focusing on user behavior.
-   **Jest**: Use Jest as the test runner.

### 5.2 Integration Testing

-   **Test Component Interactions**: Test how components interact with each other.
-   **Mock API Calls**: Mock API calls to test component behavior in different scenarios.
-   **React Testing Library**: Effective for testing integration points in components.

### 5.3 End-to-End (E2E) Testing

-   **Test Full Application Flows**: Test complete user flows, such as login, registration, and checkout.
-   **Cypress/Playwright**: Use tools like Cypress or Playwright for E2E testing.
-   **Automated Browser Tests**: Automate browser tests to ensure application stability.

### 5.4 Test Organization

-   **Co-locate Tests**: Keep test files close to the components they test (e.g., `Button.test.jsx` in the `Button` directory).
-   **Descriptive Names**: Use descriptive names for test files and test cases.
-   **Test Suites**: Organize tests into logical suites.

### 5.5 Mocking and Stubbing

-   **Mock Modules**: Mock external modules or API calls to isolate components during testing.
-   **Stub Functions**: Stub function implementations to control component behavior.
-   **Jest Mocks**: Utilize Jest's mocking capabilities for effective unit testing.

## 6. Common Pitfalls and Gotchas

### 6.1 Frequent Mistakes

-   **Ignoring Keys in Lists**: Forgetting to provide unique and stable `key` props when rendering lists.
-   **Incorrect State Updates**: Mutating state directly instead of using `setState` or the state updater function.
-   **Missing Dependencies in `useEffect`**: Not including all dependencies in the dependency array of the `useEffect` hook.
-   **Over-Using State**: Storing derived data in state instead of calculating it on demand.

### 6.2 Edge Cases

-   **Asynchronous State Updates**: Handling state updates in asynchronous operations.
-   **Race Conditions**: Preventing race conditions when making multiple API calls.
-   **Handling Errors in Event Handlers**: Properly handling errors in event handlers to prevent crashes.

### 6.3 Version-Specific Issues

-   **React 16 vs. React 17/18**: Understanding differences in lifecycle methods, error handling, and concurrent mode.
-   **Deprecated Features**: Being aware of deprecated features and using recommended alternatives.

### 6.4 Compatibility Concerns

-   **Browser Compatibility**: Ensuring compatibility with different browsers and devices.
-   **Library Compatibility**: Ensuring compatibility between React and other libraries.

### 6.5 Debugging Strategies

-   **React DevTools**: Use React DevTools to inspect component hierarchies, props, and state.
-   **Console Logging**: Use console logging to debug code and track variables.
-   **Breakpoints**: Set breakpoints in the code to step through execution and inspect variables.

## 7. Tooling and Environment

### 7.1 Recommended Development Tools

-   **VS Code**: A popular code editor with excellent React support.
-   **Create React App**: A tool for quickly setting up a new React project.
-   **React DevTools**: A browser extension for inspecting React components.
-   **ESLint**: A linter for enforcing code style and preventing errors.
-   **Prettier**: A code formatter for automatically formatting code.

### 7.2 Build Configuration

-   **Webpack/Vite**: Configure Webpack or Vite to bundle and optimize code.
-   **Babel**: Configure Babel to transpile JavaScript code to older versions.
-   **Environment Variables**: Use environment variables to configure different environments.

### 7.3 Linting and Formatting

-   **ESLint**: Configure ESLint with recommended React rules.
-   **Prettier**: Configure Prettier to automatically format code.
-   **Husky/lint-staged**: Use Husky and lint-staged to run linters and formatters before committing code.

### 7.4 Deployment Best Practices

-   **Static Hosting**: Host static assets on a CDN.
-   **Server-Side Rendering (SSR)**: Use SSR to improve SEO and initial load time.
-   **Continuous Deployment**: Automate the deployment process using CI/CD.

### 7.5 CI/CD Integration

-   **GitHub Actions/GitLab CI**: Use GitHub Actions or GitLab CI to automate testing, linting, and deployment.
-   **Automated Testing**: Run automated tests on every commit or pull request.
-   **Automated Deployment**: Automatically deploy code to production after successful tests.

By following these best practices, React developers can build high-quality, maintainable, and scalable applications that meet the demands of modern web development. Continual education and adaptation to emerging trends in the React ecosystem are crucial for sustained success.

## Tasks

### Extracted Tasks

- [ ] **Feature-based:** Group files related to a specific feature within a dedicated directory. - M1
- [ ] **`components/`**: Reusable UI components. - M2
- [ ] Each component has its own directory containing the component file, associated styles (using CSS modules), and tests. - M3
- [ ] **`contexts/`**: React context providers. - M4
- [ ] **`hooks/`**: Custom React hooks. - M5
- [ ] **`pages/`**: Top-level components representing different routes or views. - M6
- [ ] **`services/`**: API interaction logic. - M7
- [ ] **`utils/`**: Utility functions. - M8
- [ ] **Components**: Use PascalCase (e.g., `MyComponent.jsx`). - M9
- [ ] **Hooks**: Use camelCase prefixed with `use` (e.g., `useMyHook.js`). - M10
- [ ] **Contexts**: Use PascalCase suffixed with `Context` (e.g., `MyContext.jsx`). - M11
- [ ] **Services/Utils**: Use camelCase (e.g., `apiService.js`, `stringUtils.js`). - M12
- [ ] **CSS Modules**: Use `.module.css` or `.module.scss` (e.g., `Button.module.css`). - M13
- [ ] **Co-location**: Keep related files (component, styles, tests) together in the same directory. - M14
- [ ] **Single Responsibility**: Each module should have a clear and specific purpose. - M15
- [ ] **Avoid Circular Dependencies**: Ensure modules don't depend on each other in a circular manner. - M16
- [ ] **Atomic Design**: Consider using Atomic Design principles (Atoms, Molecules, Organisms, Templates, Pages) to structure components. - M17
- [ ] **Composition over Inheritance**: Favor component composition to reuse code and functionality. - M18
- [ ] **Presentational and Container Components**: Separate UI rendering (presentational) from state management and logic (container). - M19
- [ ] **Route-Based Splitting**: Use `React.lazy` and `Suspense` to load components only when a specific route is accessed.  This is very common and improves initial load time. - M20
- [ ] **Component-Based Splitting**: Split large components into smaller chunks that can be loaded on demand. - M21
- [ ] **Bundle Analyzer**: Use a tool like `webpack-bundle-analyzer` to identify large dependencies and optimize bundle size. - M22
- [ ] **Higher-Order Components (HOCs)**: Reusable logic that wraps components (use with caution; prefer hooks). - M23
- [ ] **Render Props**: Sharing code using a prop whose value is a function. - M24
- [ ] **Compound Components**: Components that work together implicitly (e.g., `Tabs`, `Tab`). - M25
- [ ] **Hooks**: Reusable stateful logic that can be shared across functional components. - M26
- [ ] **Form Handling**: Use controlled components with local state or a form library like Formik or React Hook Form. - M27
- [ ] **API Calls**: Use `useEffect` hook to make API calls and manage loading states. - M28
- [ ] **Conditional Rendering**: Use short-circuit evaluation (`&&`) or ternary operators for simple conditions; use separate components for complex scenarios. - M29
- [ ] **List Rendering**: Always provide a unique and stable `key` prop when rendering lists. - M30
- [ ] **Direct DOM Manipulation**: Avoid directly manipulating the DOM; let React handle updates. - M31
- [ ] **Mutating State Directly**: Always use `setState` or the state updater function to modify state. - M32
- [ ] **Inline Styles**: Use CSS modules or styled-components for maintainable styles. - M33
- [ ] **Over-Engineering**: Avoid using complex solutions for simple problems. - M34
- [ ] **Prop Drilling**: Passing props through multiple levels of components without them being used. - M35
- [ ] **Local State**: Use `useState` for component-specific state. - M36
- [ ] **Context API**: Use `useContext` for global state accessible to many components, but avoid for very frequently updated data. - M37
- [ ] **Redux/Mobx**: Use these libraries for complex state management in large applications. - M38
- [ ] **Recoil/Zustand**: Lightweight alternatives to Redux, often easier to set up and use. - M39
- [ ] **Immutable Data**: Treat state as immutable to prevent unexpected side effects. - M40
- [ ] **Error Boundaries**: Wrap components with error boundaries to catch errors during rendering and prevent crashes. - M41
- [ ] **Try-Catch Blocks**: Use try-catch blocks for handling errors in asynchronous operations and event handlers. - M42
- [ ] **Centralized Error Logging**: Implement a centralized error logging service to track errors and improve application stability. - M43
- [ ] **Memoization**: Use `React.memo`, `useMemo`, and `useCallback` to prevent unnecessary re-renders and recalculations. - M44
- [ ] **Virtualization**: Use libraries like `react-window` or `react-virtualized` to efficiently render large lists or tables. - M45
- [ ] **Debouncing/Throttling**: Limit the rate at which functions are executed (e.g., in input fields). - M46
- [ ] **Code Splitting**: Load code on demand using `React.lazy` and `Suspense`. - M47
- [ ] **Avoid Memory Leaks**: Clean up event listeners, timers, and subscriptions in `useEffect`'s cleanup function. - M48
- [ ] **Release Unused Objects**: Avoid holding onto large objects in memory when they are no longer needed. - M49
- [ ] **Garbage Collection**: Understand how JavaScript's garbage collection works and avoid creating unnecessary objects. - M50
- [ ] **Minimize State Updates**: Avoid unnecessary state updates that trigger re-renders. - M51
- [ ] **Batch Updates**: Batch multiple state updates into a single update using `ReactDOM.unstable_batchedUpdates`. - M52
- [ ] **Keys**: Ensure that keys are unique and consistent across renders. - M53
- [ ] **Tree Shaking**: Remove unused code during the build process. - M54
- [ ] **Minification**: Reduce the size of JavaScript and CSS files. - M55
- [ ] **Image Optimization**: Compress and optimize images to reduce file size. - M56
- [ ] **Dependency Analysis**: Use tools like `webpack-bundle-analyzer` to identify large dependencies. - M57
- [ ] **Route-Based Lazy Loading**: Load components when a user navigates to a specific route. - M58
- [ ] **Component-Based Lazy Loading**: Load components when they are about to be rendered. - M59
- [ ] **Intersection Observer**: Load components when they become visible in the viewport. - M60
- [ ] **Cross-Site Scripting (XSS)**: Sanitize user input to prevent malicious code injection. - M61
- [ ] **Cross-Site Request Forgery (CSRF)**: Use anti-CSRF tokens to protect against unauthorized requests. - M62
- [ ] **Denial of Service (DoS)**: Implement rate limiting and request validation to prevent abuse. - M63
- [ ] **Injection Attacks**: Avoid directly embedding user input into database queries or system commands. - M64
- [ ] **Client-Side Validation**: Validate user input in the browser to provide immediate feedback. - M65
- [ ] **Server-Side Validation**: Always validate user input on the server to prevent malicious data. - M66
- [ ] **Sanitize Input**: Sanitize user input to remove potentially harmful characters or code. - M67
- [ ] **Secure Authentication**: Use secure authentication mechanisms like OAuth 2.0 or JWT. - M68
- [ ] **Role-Based Access Control (RBAC)**: Implement RBAC to control access to resources based on user roles. - M69
- [ ] **Multi-Factor Authentication (MFA)**: Enable MFA to add an extra layer of security. - M70
- [ ] **Encryption**: Encrypt sensitive data at rest and in transit. - M71
- [ ] **Data Masking**: Mask sensitive data in logs and UI displays. - M72
- [ ] **Regular Backups**: Create regular backups of application data. - M73
- [ ] **HTTPS**: Use HTTPS to encrypt communication between the client and the server. - M74
- [ ] **API Keys**: Protect API keys and secrets. - M75
- [ ] **CORS**: Configure Cross-Origin Resource Sharing (CORS) to prevent unauthorized access to APIs. - M76
- [ ] **Test Components**: Test individual components in isolation. - M77
- [ ] **Testing Library**: Use React Testing Library for UI testing, focusing on user behavior. - M78
- [ ] **Jest**: Use Jest as the test runner. - M79
- [ ] **Test Component Interactions**: Test how components interact with each other. - M80
- [ ] **Mock API Calls**: Mock API calls to test component behavior in different scenarios. - M81
- [ ] **React Testing Library**: Effective for testing integration points in components. - M82
- [ ] **Test Full Application Flows**: Test complete user flows, such as login, registration, and checkout. - M83
- [ ] **Cypress/Playwright**: Use tools like Cypress or Playwright for E2E testing. - M84
- [ ] **Automated Browser Tests**: Automate browser tests to ensure application stability. - M85
- [ ] **Co-locate Tests**: Keep test files close to the components they test (e.g., `Button.test.jsx` in the `Button` directory). - M86
- [ ] **Descriptive Names**: Use descriptive names for test files and test cases. - M87
- [ ] **Test Suites**: Organize tests into logical suites. - M88
- [ ] **Mock Modules**: Mock external modules or API calls to isolate components during testing. - M89
- [ ] **Stub Functions**: Stub function implementations to control component behavior. - M90
- [ ] **Jest Mocks**: Utilize Jest's mocking capabilities for effective unit testing. - M91
- [ ] **Ignoring Keys in Lists**: Forgetting to provide unique and stable `key` props when rendering lists. - M92
- [ ] **Incorrect State Updates**: Mutating state directly instead of using `setState` or the state updater function. - M93
- [ ] **Missing Dependencies in `useEffect`**: Not including all dependencies in the dependency array of the `useEffect` hook. - M94
- [ ] **Over-Using State**: Storing derived data in state instead of calculating it on demand. - M95
- [ ] **Asynchronous State Updates**: Handling state updates in asynchronous operations. - M96
- [ ] **Race Conditions**: Preventing race conditions when making multiple API calls. - M97
- [ ] **Handling Errors in Event Handlers**: Properly handling errors in event handlers to prevent crashes. - M98
- [ ] **React 16 vs. React 17/18**: Understanding differences in lifecycle methods, error handling, and concurrent mode. - M99
- [ ] **Deprecated Features**: Being aware of deprecated features and using recommended alternatives. - M100
- [ ] **Browser Compatibility**: Ensuring compatibility with different browsers and devices. - M101
- [ ] **Library Compatibility**: Ensuring compatibility between React and other libraries. - M102
- [ ] **React DevTools**: Use React DevTools to inspect component hierarchies, props, and state. - M103
- [ ] **Console Logging**: Use console logging to debug code and track variables. - M104
- [ ] **Breakpoints**: Set breakpoints in the code to step through execution and inspect variables. - M105
- [ ] **VS Code**: A popular code editor with excellent React support. - M106
- [ ] **Create React App**: A tool for quickly setting up a new React project. - M107
- [ ] **React DevTools**: A browser extension for inspecting React components. - M108
- [ ] **ESLint**: A linter for enforcing code style and preventing errors. - M109
- [ ] **Prettier**: A code formatter for automatically formatting code. - M110
- [ ] **Webpack/Vite**: Configure Webpack or Vite to bundle and optimize code. - M111
- [ ] **Babel**: Configure Babel to transpile JavaScript code to older versions. - M112
- [ ] **Environment Variables**: Use environment variables to configure different environments. - M113
- [ ] **ESLint**: Configure ESLint with recommended React rules. - M114
- [ ] **Prettier**: Configure Prettier to automatically format code. - M115
- [ ] **Husky/lint-staged**: Use Husky and lint-staged to run linters and formatters before committing code. - M116
- [ ] **Static Hosting**: Host static assets on a CDN. - M117
- [ ] **Server-Side Rendering (SSR)**: Use SSR to improve SEO and initial load time. - M118
- [ ] **Continuous Deployment**: Automate the deployment process using CI/CD. - M119
- [ ] **GitHub Actions/GitLab CI**: Use GitHub Actions or GitLab CI to automate testing, linting, and deployment. - M120
- [ ] **Automated Testing**: Run automated tests on every commit or pull request. - M121
- [ ] **Automated Deployment**: Automatically deploy code to production after successful tests. - M122

### Documentation Tasks

- [ ] Update content accuracy - M1
- [ ] Add code examples - M2
- [ ] Add diagrams/screenshots - M3
- [ ] Review and proofread - M4
- [ ] Add cross-references - M5

