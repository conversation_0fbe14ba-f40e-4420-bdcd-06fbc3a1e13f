---
description: Guide for using <PERSON><PERSON> to manage task-driven development workflows
globs: **/*
alwaysApply: true
---

# Taskmaster Development Workflow

This guide outlines the standard process for using Taskmaster to manage software development projects. It is written as a set of instructions for you, the AI agent.

- **Your Default Stance**: For most projects, the user can work directly within the `master` task context. Your initial actions should operate on this default context unless a clear pattern for multi-context work emerges.
- **Your Goal**: Your role is to elevate the user's workflow by intelligently introducing advanced features like **Tagged Task Lists** when you detect the appropriate context. Do not force tags on the user; suggest them as a helpful solution to a specific need.

## The Basic Loop
The fundamental development cycle you will facilitate is:
1.  **`list`**: Show the user what needs to be done.
2.  **`next`**: Help the user decide what to work on.
3.  **`show <id>`**: Provide details for a specific task.
4.  **`expand <id>`**: Break down a complex task into smaller, manageable subtasks.
5.  **Implement**: The user writes the code and tests.
6.  **`update-subtask`**: Log progress and findings on behalf of the user.
7.  **`set-status`**: Mark tasks and subtasks as `done` as work is completed.
8.  **Repeat**.

All your standard command executions should operate on the user's current task context, which defaults to `master`.

---

## Standard Development Workflow Process

### Simple Workflow (Default Starting Point)

For new projects or when users are getting started, operate within the `master` tag context:

-   Start new projects by running `initialize_project` tool / `task-master init` or `parse_prd` / `task-master parse-prd --input='<prd-file.txt>'` (see @`taskmaster.md`) to generate initial tasks.json with tagged structure
-   Configure rule sets during initialization with `--rules` flag (e.g., `task-master init --rules trae,windsurf`) or manage them later with `task-master rules add/remove` commands  
-   Begin coding sessions with `get_tasks` / `task-master list` (see @`taskmaster.md`) to see current tasks, status, and IDs
-   Determine the next task to work on using `next_task` / `task-master next` (see @`taskmaster.md`)
-   Analyze task complexity with `analyze_project_complexity` / `task-master analyze-complexity --research` (see @`taskmaster.md`) before breaking down tasks
-   Review complexity report using `complexity_report` / `task-master complexity-report` (see @`taskmaster.md`)
-   Select tasks based on dependencies (all marked 'done'), priority level, and ID order
-   View specific task details using `get_task` / `task-master show <id>` (see @`taskmaster.md`) to understand implementation requirements
-   Break down complex tasks using `expand_task` / `task-master expand --id=<id> --force --research` (see @`taskmaster.md`) with appropriate flags like `--force` (to replace existing subtasks) and `--research`
-   Implement code following task details, dependencies, and project standards
-   Mark completed tasks with `set_task_status` / `task-master set-status --id=<id> --status=done` (see @`taskmaster.md`)
-   Update dependent tasks when implementation differs from original plan using `update` / `task-master update --from=<id> --prompt="..."` or `update_task` / `task-master update-task --id=<id> --prompt="..."` (see @`taskmaster.md`)

---

## Leveling Up: Agent-Led Multi-Context Workflows

While the basic workflow is powerful, your primary opportunity to add value is by identifying when to introduce **Tagged Task Lists**. These patterns are your tools for creating a more organized and efficient development environment for the user, especially if you detect agentic or parallel development happening across the same session.

**Critical Principle**: Most users should never see a difference in their experience. Only introduce advanced workflows when you detect clear indicators that the project has evolved beyond simple task management.

### When to Introduce Tags: Your Decision Patterns

Here are the patterns to look for. When you detect one, you should propose the corresponding workflow to the user.

#### Pattern 1: Simple Git Feature Branching
This is the most common and direct use case for tags.

- **Trigger**: The user creates a new git branch (e.g., `git checkout -b feature/user-auth`).
- **Your Action**: Propose creating a new tag that mirrors the branch name to isolate the feature's tasks from `master`.
- **Your Suggested Prompt**: *"I see you've created a new branch named 'feature/user-auth'. To keep all related tasks neatly organized and separate from your main list, I can create a corresponding task tag for you. This helps prevent merge conflicts in your `tasks.json` file later. Shall I create the 'feature-user-auth' tag?"*
- **Tool to Use**: `task-master add-tag --from-branch`

#### Pattern 2: Team Collaboration
- **Trigger**: The user mentions working with teammates (e.g., "My teammate Alice is handling the database schema," or "I need to review Bob's work on the API.").
- **Your Action**: Suggest creating a separate tag for the user's work to prevent conflicts with shared master context.
- **Your Suggested Prompt**: *"Since you're working with Alice, I can create a separate task context for your work to avoid conflicts. This way, Alice can continue working with the master list while you have your own isolated context. When you're ready to merge your work, we can coordinate the tasks back to master. Shall I create a tag for your current work?"*
- **Tool to Use**: `task-master add-tag my-work --copy-from-current --description="My tasks while collaborating with Alice"`

#### Pattern 3: Experiments or Risky Refactors
- **Trigger**: The user wants to try something that might not be kept (e.g., "I want to experiment with switching our state management library," or "Let's refactor the old API module, but I want to keep the current tasks as a reference.").
- **Your Action**: Propose creating a sandboxed tag for the experimental work.
- **Your Suggested Prompt**: *"This sounds like a great experiment. To keep these new tasks separate from our main plan, I can create a temporary 'experiment-zustand' tag for this work. If we decide not to proceed, we can simply delete the tag without affecting the main task list. Sound good?"*
- **Tool to Use**: `task-master add-tag experiment-zustand --description="Exploring Zustand migration"`

#### Pattern 4: Large Feature Initiatives (PRD-Driven)
This is a more structured approach for significant new features or epics.

- **Trigger**: The user describes a large, multi-step feature that would benefit from a formal plan.
- **Your Action**: Propose a comprehensive, PRD-driven workflow.
- **Your Suggested Prompt**: *"This sounds like a significant new feature. To manage this effectively, I suggest we create a dedicated task context for it. Here's the plan: I'll create a new tag called 'feature-xyz', then we can draft a Product Requirements Document (PRD) together to scope the work. Once the PRD is ready, I'll automatically generate all the necessary tasks within that new tag. How does that sound?"*
- **Your Implementation Flow**:
    1.  **Create an empty tag**: `task-master add-tag feature-xyz --description "Tasks for the new XYZ feature"`. You can also start by creating a git branch if applicable, and then create the tag from that branch.
    2.  **Collaborate & Create PRD**: Work with the user to create a detailed PRD file (e.g., `.taskmaster/docs/feature-xyz-prd.txt`).
    3.  **Parse PRD into the new tag**: `task-master parse-prd .taskmaster/docs/feature-xyz-prd.txt --tag feature-xyz`
    4.  **Prepare the new task list**: Follow up by suggesting `analyze-complexity` and `expand-all` for the newly created tasks within the `feature-xyz` tag.

#### Pattern 5: Version-Based Development
Tailor your approach based on the project maturity indicated by tag names.

- **Prototype/MVP Tags** (`prototype`, `mvp`, `poc`, `v0.x`):
  - **Your Approach**: Focus on speed and functionality over perfection
  - **Task Generation**: Create tasks that emphasize "get it working" over "get it perfect"
  - **Complexity Level**: Lower complexity, fewer subtasks, more direct implementation paths
  - **Research Prompts**: Include context like "This is a prototype - prioritize speed and basic functionality over optimization"
  - **Example Prompt Addition**: *"Since this is for the MVP, I'll focus on tasks that get core functionality working quickly rather than over-engineering."*

- **Production/Mature Tags** (`v1.0+`, `production`, `stable`):
  - **Your Approach**: Emphasize robustness, testing, and maintainability
  - **Task Generation**: Include comprehensive error handling, testing, documentation, and optimization
  - **Complexity Level**: Higher complexity, more detailed subtasks, thorough implementation paths
  - **Research Prompts**: Include context like "This is for production - prioritize reliability, performance, and maintainability"
  - **Example Prompt Addition**: *"Since this is for production, I'll ensure tasks include proper error handling, testing, and documentation."*

### Advanced Workflow (Tag-Based & PRD-Driven)

**When to Transition**: Recognize when the project has evolved (or has initiated a project which existing code) beyond simple task management. Look for these indicators:
- User mentions teammates or collaboration needs
- Project has grown to 15+ tasks with mixed priorities
- User creates feature branches or mentions major initiatives
- User initializes Taskmaster on an existing, complex codebase
- User describes large features that would benefit from dedicated planning

**Your Role in Transition**: Guide the user to a more sophisticated workflow that leverages tags for organization and PRDs for comprehensive planning.

#### Master List Strategy (High-Value Focus)
Once you transition to tag-based workflows, the `master` tag should ideally contain only:
- **High-level deliverables** that provide significant business value
- **Major milestones** and epic-level features
- **Critical infrastructure** work that affects the entire project
- **Release-blocking** items

**What NOT to put in master**:
- Detailed implementation subtasks (these go in feature-specific tags' parent tasks)
- Refactoring work (create dedicated tags like `refactor-auth`)
- Experimental features (use `experiment-*` tags)
- Team member-specific tasks (use person-specific tags)

#### PRD-Driven Feature Development

**For New Major Features**:
1. **Identify the Initiative**: When user describes a significant feature
2. **Create Dedicated Tag**: `add_tag feature-[name] --description="[Feature description]"`
3. **Collaborative PRD Creation**: Work with user to create comprehensive PRD in `.taskmaster/docs/feature-[name]-prd.txt`
4. **Parse & Prepare**: 
   - `parse_prd .taskmaster/docs/feature-[name]-prd.txt --tag=feature-[name]`
   - `analyze_project_complexity --tag=feature-[name] --research`
   - `expand_all --tag=feature-[name] --research`
5. **Add Master Reference**: Create a high-level task in `master` that references the feature tag

**For Existing Codebase Analysis**:
When users initialize Taskmaster on existing projects:
1. **Codebase Discovery**: Use your native tools for producing deep context about the code base. You may use `research` tool with `--tree` and `--files` to collect up to date information using the existing architecture as context.
2. **Collaborative Assessment**: Work with user to identify improvement areas, technical debt, or new features
3. **Strategic PRD Creation**: Co-author PRDs that include:
   - Current state analysis (based on your codebase research)
   - Proposed improvements or new features
   - Implementation strategy considering existing code
4. **Tag-Based Organization**: Parse PRDs into appropriate tags (`refactor-api`, `feature-dashboard`, `tech-debt`, etc.)
5. **Master List Curation**: Keep only the most valuable initiatives in master

The parse-prd's `--append` flag enables the user to parse multple PRDs within tags or across tags. PRDs should be focused and the number of tasks they are parsed into should be strategically chosen relative to the PRD's complexity and level of detail.

### Workflow Transition Examples

**Example 1: Simple → Team-Based**
```
User: "Alice is going to help with the API work"
Your Response: "Great! To avoid conflicts, I'll create a separate task context for your work. Alice can continue with the master list while you work in your own context. When you're ready to merge, we can coordinate the tasks back together."
Action: add_tag my-api-work --copy-from-current --description="My API tasks while collaborating with Alice"
```

**Example 2: Simple → PRD-Driven**
```
User: "I want to add a complete user dashboard with analytics, user management, and reporting"
Your Response: "This sounds like a major feature that would benefit from detailed planning. Let me create a dedicated context for this work and we can draft a PRD together to ensure we capture all requirements."
Actions: 
1. add_tag feature-dashboard --description="User dashboard with analytics and management"
2. Collaborate on PRD creation
3. parse_prd dashboard-prd.txt --tag=feature-dashboard
4. Add high-level "User Dashboard" task to master
```

**Example 3: Existing Project → Strategic Planning**
```
User: "I just initialized Taskmaster on my existing React app. It's getting messy and I want to improve it."
Your Response: "Let me research your codebase to understand the current architecture, then we can create a strategic plan for improvements."
Actions:
1. research "Current React app architecture and improvement opportunities" --tree --files=src/
2. Collaborate on improvement PRD based on findings
3. Create tags for different improvement areas (refactor-components, improve-state-management, etc.)
4. Keep only major improvement initiatives in master
```

---

## Primary Interaction: MCP Server vs. CLI

Taskmaster offers two primary ways to interact:

1.  **MCP Server (Recommended for Integrated Tools)**:
    - For AI agents and integrated development environments (like Trae), interacting via the **MCP server is the preferred method**.
    - The MCP server exposes Taskmaster functionality through a set of tools (e.g., `get_tasks`, `add_subtask`).
    - This method offers better performance, structured data exchange, and richer error handling compared to CLI parsing.
    - Refer to @`mcp.md` for details on the MCP architecture and available tools.
    - A comprehensive list and description of MCP tools and their corresponding CLI commands can be found in @`taskmaster.md`.
    - **Restart the MCP server** if core logic in `scripts/modules` or MCP tool/direct function definitions change.
    - **Note**: MCP tools fully support tagged task lists with complete tag management capabilities.

2.  **`task-master` CLI (For Users & Fallback)**:
    - The global `task-master` command provides a user-friendly interface for direct terminal interaction.
    - It can also serve as a fallback if the MCP server is inaccessible or a specific function isn't exposed via MCP.
    - Install globally with `npm install -g task-master-ai` or use locally via `npx task-master-ai ...`.
    - The CLI commands often mirror the MCP tools (e.g., `task-master list` corresponds to `get_tasks`).
    - Refer to @`taskmaster.md` for a detailed command reference.
    - **Tagged Task Lists**: CLI fully supports the new tagged system with seamless migration.

## How the Tag System Works (For Your Reference)

- **Data Structure**: Tasks are organized into separate contexts (tags) like "master", "feature-branch", or "v2.0".
- **Silent Migration**: Existing projects automatically migrate to use a "master" tag with zero disruption.
- **Context Isolation**: Tasks in different tags are completely separate. Changes in one tag do not affect any other tag.
- **Manual Control**: The user is always in control. There is no automatic switching. You facilitate switching by using `use-tag <name>`.
- **Full CLI & MCP Support**: All tag management commands are available through both the CLI and MCP tools for you to use. Refer to @`taskmaster.md` for a full command list.

---

## Task Complexity Analysis

-   Run `analyze_project_complexity` / `task-master analyze-complexity --research` (see @`taskmaster.md`) for comprehensive analysis
-   Review complexity report via `complexity_report` / `task-master complexity-report` (see @`taskmaster.md`) for a formatted, readable version.
-   Focus on tasks with highest complexity scores (8-10) for detailed breakdown
-   Use analysis results to determine appropriate subtask allocation
-   Note that reports are automatically used by the `expand_task` tool/command

## Task Breakdown Process

-   Use `expand_task` / `task-master expand --id=<id>`. It automatically uses the complexity report if found, otherwise generates default number of subtasks.
-   Use `--num=<number>` to specify an explicit number of subtasks, overriding defaults or complexity report recommendations.
-   Add `--research` flag to leverage Perplexity AI for research-backed expansion.
-   Add `--force` flag to clear existing subtasks before generating new ones (default is to append).
-   Use `--prompt="<context>"` to provide additional context when needed.
-   Review and adjust generated subtasks as necessary.
-   Use `expand_all` tool or `task-master expand --all` to expand multiple pending tasks at once, respecting flags like `--force` and `--research`.
-   If subtasks need complete replacement (regardless of the `--force` flag on `expand`), clear them first with `clear_subtasks` / `task-master clear-subtasks --id=<id>`.

## Implementation Drift Handling

-   When implementation differs significantly from planned approach
-   When future tasks need modification due to current implementation choices
-   When new dependencies or requirements emerge
-   Use `update` / `task-master update --from=<futureTaskId> --prompt='<explanation>\nUpdate context...' --research` to update multiple future tasks.
-   Use `update_task` / `task-master update-task --id=<taskId> --prompt='<explanation>\nUpdate context...' --research` to update a single specific task.

## Task Status Management

-   Use 'pending' for tasks ready to be worked on
-   Use 'done' for completed and verified tasks
-   Use 'deferred' for postponed tasks
-   Add custom status values as needed for project-specific workflows

## Task Structure Fields

- **id**: Unique identifier for the task (Example: `1`, `1.1`)
- **title**: Brief, descriptive title (Example: `"Initialize Repo"`)
- **description**: Concise summary of what the task involves (Example: `"Create a new repository, set up initial structure."`)
- **status**: Current state of the task (Example: `"pending"`, `"done"`, `"deferred"`)
- **dependencies**: IDs of prerequisite tasks (Example: `[1, 2.1]`)
    - Dependencies are displayed with status indicators (✅ for completed, ⏱️ for pending)
    - This helps quickly identify which prerequisite tasks are blocking work
- **priority**: Importance level (Example: `"high"`, `"medium"`, `"low"`)
- **details**: In-depth implementation instructions (Example: `"Use GitHub client ID/secret, handle callback, set session token."`) 
- **testStrategy**: Verification approach (Example: `"Deploy and call endpoint to confirm 'Hello World' response."`) 
- **subtasks**: List of smaller, more specific tasks (Example: `[{"id": 1, "title": "Configure OAuth", ...}]`) 
- Refer to task structure details (previously linked to `tasks.md`).

## Configuration Management (Updated)

Taskmaster configuration is managed through two main mechanisms:

1.  **`.taskmaster/config.json` File (Primary):**
    *   Located in the project root directory.
    *   Stores most configuration settings: AI model selections (main, research, fallback), parameters (max tokens, temperature), logging level, default subtasks/priority, project name, etc.
    *   **Tagged System Settings**: Includes `global.defaultTag` (defaults to "master") and `tags` section for tag management configuration.
    *   **Managed via `task-master models --setup` command.** Do not edit manually unless you know what you are doing.
    *   **View/Set specific models via `task-master models` command or `models` MCP tool.**
    *   Created automatically when you run `task-master models --setup` for the first time or during tagged system migration.

2.  **Environment Variables (`.env` / `mcp.json`):**
    *   Used **only** for sensitive API keys and specific endpoint URLs.
    *   Place API keys (one per provider) in a `.env` file in the project root for CLI usage.
    *   For MCP/Trae integration, configure these keys in the `env` section of `.trae/mcp.json`.
    *   Available keys/variables: See `assets/env.example` or the Configuration section in the command reference (previously linked to `taskmaster.md`).

3.  **`.taskmaster/state.json` File (Tagged System State):**
    *   Tracks current tag context and migration status.
    *   Automatically created during tagged system migration.
    *   Contains: `currentTag`, `lastSwitched`, `migrationNoticeShown`.

**Important:** Non-API key settings (like model selections, `MAX_TOKENS`, `TASKMASTER_LOG_LEVEL`) are **no longer configured via environment variables**. Use the `task-master models` command (or `--setup` for interactive configuration) or the `models` MCP tool.
**If AI commands FAIL in MCP** verify that the API key for the selected provider is present in the `env` section of `.trae/mcp.json`.
**If AI commands FAIL in CLI** verify that the API key for the selected provider is present in the `.env` file in the root of the project.

## Rules Management

Taskmaster supports multiple AI coding assistant rule sets that can be configured during project initialization or managed afterward:

- **Available Profiles**: Claude Code, Cline, Codex, Trae, Roo Code, Trae, Windsurf (claude, cline, codex, trae, roo, trae, windsurf)
- **During Initialization**: Use `task-master init --rules trae,windsurf` to specify which rule sets to include
- **After Initialization**: Use `task-master rules add <profiles>` or `task-master rules remove <profiles>` to manage rule sets
- **Interactive Setup**: Use `task-master rules setup` to launch an interactive prompt for selecting rule profiles
- **Default Behavior**: If no `--rules` flag is specified during initialization, all available rule profiles are included
- **Rule Structure**: Each profile creates its own directory (e.g., `.trae/rules`, `.roo/rules`) with appropriate configuration files

## Determining the Next Task

- Run `next_task` / `task-master next` to show the next task to work on.
- The command identifies tasks with all dependencies satisfied
- Tasks are prioritized by priority level, dependency count, and ID
- The command shows comprehensive task information including:
    - Basic task details and description
    - Implementation details
    - Subtasks (if they exist)
    - Contextual suggested actions
- Recommended before starting any new development work
- Respects your project's dependency structure
- Ensures tasks are completed in the appropriate sequence
- Provides ready-to-use commands for common task actions

## Viewing Specific Task Details

- Run `get_task` / `task-master show <id>` to view a specific task.
- Use dot notation for subtasks: `task-master show 1.2` (shows subtask 2 of task 1)
- Displays comprehensive information similar to the next command, but for a specific task
- For parent tasks, shows all subtasks and their current status
- For subtasks, shows parent task information and relationship
- Provides contextual suggested actions appropriate for the specific task
- Useful for examining task details before implementation or checking status

## Managing Task Dependencies

- Use `add_dependency` / `task-master add-dependency --id=<id> --depends-on=<id>` to add a dependency.
- Use `remove_dependency` / `task-master remove-dependency --id=<id> --depends-on=<id>` to remove a dependency.
- The system prevents circular dependencies and duplicate dependency entries
- Dependencies are checked for existence before being added or removed
- Task files are automatically regenerated after dependency changes
- Dependencies are visualized with status indicators in task listings and files

## Task Reorganization

- Use `move_task` / `task-master move --from=<id> --to=<id>` to move tasks or subtasks within the hierarchy
- This command supports several use cases:
  - Moving a standalone task to become a subtask (e.g., `--from=5 --to=7`)
  - Moving a subtask to become a standalone task (e.g., `--from=5.2 --to=7`) 
  - Moving a subtask to a different parent (e.g., `--from=5.2 --to=7.3`)
  - Reordering subtasks within the same parent (e.g., `--from=5.2 --to=5.4`)
  - Moving a task to a new, non-existent ID position (e.g., `--from=5 --to=25`)
  - Moving multiple tasks at once using comma-separated IDs (e.g., `--from=10,11,12 --to=16,17,18`)
- The system includes validation to prevent data loss:
  - Allows moving to non-existent IDs by creating placeholder tasks
  - Prevents moving to existing task IDs that have content (to avoid overwriting)
  - Validates source tasks exist before attempting to move them
- The system maintains proper parent-child relationships and dependency integrity
- Task files are automatically regenerated after the move operation
- This provides greater flexibility in organizing and refining your task structure as project understanding evolves
- This is especially useful when dealing with potential merge conflicts arising from teams creating tasks on separate branches. Solve these conflicts very easily by moving your tasks and keeping theirs.

## Iterative Subtask Implementation

Once a task has been broken down into subtasks using `expand_task` or similar methods, follow this iterative process for implementation:

1.  **Understand the Goal (Preparation):**
    *   Use `get_task` / `task-master show <subtaskId>` (see @`taskmaster.md`) to thoroughly understand the specific goals and requirements of the subtask.

2.  **Initial Exploration & Planning (Iteration 1):**
    *   This is the first attempt at creating a concrete implementation plan.
    *   Explore the codebase to identify the precise files, functions, and even specific lines of code that will need modification.
    *   Determine the intended code changes (diffs) and their locations.
    *   Gather *all* relevant details from this exploration phase.

3.  **Log the Plan:**
    *   Run `update_subtask` / `task-master update-subtask --id=<subtaskId> --prompt='<detailed plan>'`.
    *   Provide the *complete and detailed* findings from the exploration phase in the prompt. Include file paths, line numbers, proposed diffs, reasoning, and any potential challenges identified. Do not omit details. The goal is to create a rich, timestamped log within the subtask's `details`.

4.  **Verify the Plan:**
    *   Run `get_task` / `task-master show <subtaskId>` again to confirm that the detailed implementation plan has been successfully appended to the subtask's details.

5.  **Begin Implementation:**
    *   Set the subtask status using `set_task_status` / `task-master set-status --id=<subtaskId> --status=in-progress`.
    *   Start coding based on the logged plan.

6.  **Refine and Log Progress (Iteration 2+):**
    *   As implementation progresses, you will encounter challenges, discover nuances, or confirm successful approaches.
    *   **Before appending new information**: Briefly review the *existing* details logged in the subtask (using `get_task` or recalling from context) to ensure the update adds fresh insights and avoids redundancy.
    *   **Regularly** use `update_subtask` / `task-master update-subtask --id=<subtaskId> --prompt='<update details>\n- What worked...\n- What didn't work...'` to append new findings.
    *   **Crucially, log:**
        *   What worked ("fundamental truths" discovered).
        *   What didn't work and why (to avoid repeating mistakes).
        *   Specific code snippets or configurations that were successful.
        *   Decisions made, especially if confirmed with user input.
        *   Any deviations from the initial plan and the reasoning.
    *   The objective is to continuously enrich the subtask's details, creating a log of the implementation journey that helps the AI (and human developers) learn, adapt, and avoid repeating errors.

7.  **Review & Update Rules (Post-Implementation):**
    *   Once the implementation for the subtask is functionally complete, review all code changes and the relevant chat history.
    *   Identify any new or modified code patterns, conventions, or best practices established during the implementation.
    *   Create new or update existing rules following internal guidelines (previously linked to `cursor_rules.md` and `self_improve.md`).

8.  **Mark Task Complete:**
    *   After verifying the implementation and updating any necessary rules, mark the subtask as completed: `set_task_status` / `task-master set-status --id=<subtaskId> --status=done`.

9.  **Commit Changes (If using Git):**
    *   Stage the relevant code changes and any updated/new rule files (`git add .`).
    *   Craft a comprehensive Git commit message summarizing the work done for the subtask, including both code implementation and any rule adjustments.
    *   Execute the commit command directly in the terminal (e.g., `git commit -m 'feat(module): Implement feature X for subtask <subtaskId>\n\n- Details about changes...\n- Updated rule Y for pattern Z'`).
    *   Consider if a Changeset is needed according to internal versioning guidelines (previously linked to `changeset.md`). If so, run `npm run changeset`, stage the generated file, and amend the commit or create a new one.

10. **Proceed to Next Subtask:**
    *   Identify the next subtask (e.g., using `next_task` / `task-master next`).

## Code Analysis & Refactoring Techniques

- **Top-Level Function Search**:
    - Useful for understanding module structure or planning refactors.
    - Use grep/ripgrep to find exported functions/constants:
      `rg "export (async function|function|const) \w+"` or similar patterns.
    - Can help compare functions between files during migrations or identify potential naming conflicts.

---
*This workflow provides a general guideline. Adapt it based on your specific project needs and team practices.*

## Tasks

### Extracted Tasks

- [ ] **Your Default Stance**: For most projects, the user can work directly within the `master` task context. Your initial actions should operate on this default context unless a clear pattern for multi-context work emerges. - M1
- [ ] **Your Goal**: Your role is to elevate the user's workflow by intelligently introducing advanced features like **Tagged Task Lists** when you detect the appropriate context. Do not force tags on the user; suggest them as a helpful solution to a specific need. - M2
- [ ] Start new projects by running `initialize_project` tool / `task-master init` or `parse_prd` / `task-master parse-prd --input='<prd-file.txt>'` (see @`taskmaster.md`) to generate initial tasks.json with tagged structure - M3
- [ ] Configure rule sets during initialization with `--rules` flag (e.g., `task-master init --rules trae,windsurf`) or manage them later with `task-master rules add/remove` commands - M4
- [ ] Begin coding sessions with `get_tasks` / `task-master list` (see @`taskmaster.md`) to see current tasks, status, and IDs - M5
- [ ] Determine the next task to work on using `next_task` / `task-master next` (see @`taskmaster.md`) - M6
- [ ] Analyze task complexity with `analyze_project_complexity` / `task-master analyze-complexity --research` (see @`taskmaster.md`) before breaking down tasks - M7
- [ ] Review complexity report using `complexity_report` / `task-master complexity-report` (see @`taskmaster.md`) - M8
- [ ] Select tasks based on dependencies (all marked 'done'), priority level, and ID order - M9
- [ ] View specific task details using `get_task` / `task-master show <id>` (see @`taskmaster.md`) to understand implementation requirements - M10
- [ ] Break down complex tasks using `expand_task` / `task-master expand --id=<id> --force --research` (see @`taskmaster.md`) with appropriate flags like `--force` (to replace existing subtasks) and `--research` - M11
- [ ] Implement code following task details, dependencies, and project standards - M12
- [ ] Mark completed tasks with `set_task_status` / `task-master set-status --id=<id> --status=done` (see @`taskmaster.md`) - M13
- [ ] Update dependent tasks when implementation differs from original plan using `update` / `task-master update --from=<id> --prompt="..."` or `update_task` / `task-master update-task --id=<id> --prompt="..."` (see @`taskmaster.md`) - M14
- [ ] **Trigger**: The user creates a new git branch (e.g., `git checkout -b feature/user-auth`). - M15
- [ ] **Your Action**: Propose creating a new tag that mirrors the branch name to isolate the feature's tasks from `master`. - M16
- [ ] **Your Suggested Prompt**: *"I see you've created a new branch named 'feature/user-auth'. To keep all related tasks neatly organized and separate from your main list, I can create a corresponding task tag for you. This helps prevent merge conflicts in your `tasks.json` file later. Shall I create the 'feature-user-auth' tag?"* - M17
- [ ] **Tool to Use**: `task-master add-tag --from-branch` - M18
- [ ] **Trigger**: The user mentions working with teammates (e.g., "My teammate Alice is handling the database schema," or "I need to review Bob's work on the API."). - M19
- [ ] **Your Action**: Suggest creating a separate tag for the user's work to prevent conflicts with shared master context. - M20
- [ ] **Your Suggested Prompt**: *"Since you're working with Alice, I can create a separate task context for your work to avoid conflicts. This way, Alice can continue working with the master list while you have your own isolated context. When you're ready to merge your work, we can coordinate the tasks back to master. Shall I create a tag for your current work?"* - M21
- [ ] **Tool to Use**: `task-master add-tag my-work --copy-from-current --description="My tasks while collaborating with Alice"` - M22
- [ ] **Trigger**: The user wants to try something that might not be kept (e.g., "I want to experiment with switching our state management library," or "Let's refactor the old API module, but I want to keep the current tasks as a reference."). - M23
- [ ] **Your Action**: Propose creating a sandboxed tag for the experimental work. - M24
- [ ] **Your Suggested Prompt**: *"This sounds like a great experiment. To keep these new tasks separate from our main plan, I can create a temporary 'experiment-zustand' tag for this work. If we decide not to proceed, we can simply delete the tag without affecting the main task list. Sound good?"* - M25
- [ ] **Tool to Use**: `task-master add-tag experiment-zustand --description="Exploring Zustand migration"` - M26
- [ ] **Trigger**: The user describes a large, multi-step feature that would benefit from a formal plan. - M27
- [ ] **Your Action**: Propose a comprehensive, PRD-driven workflow. - M28
- [ ] **Your Suggested Prompt**: *"This sounds like a significant new feature. To manage this effectively, I suggest we create a dedicated task context for it. Here's the plan: I'll create a new tag called 'feature-xyz', then we can draft a Product Requirements Document (PRD) together to scope the work. Once the PRD is ready, I'll automatically generate all the necessary tasks within that new tag. How does that sound?"* - M29
- [ ] **Your Implementation Flow**: - M30
- [ ] **Prototype/MVP Tags** (`prototype`, `mvp`, `poc`, `v0.x`): - M31
- [ ] **Your Approach**: Focus on speed and functionality over perfection - M32
- [ ] **Task Generation**: Create tasks that emphasize "get it working" over "get it perfect" - M33
- [ ] **Complexity Level**: Lower complexity, fewer subtasks, more direct implementation paths - M34
- [ ] **Research Prompts**: Include context like "This is a prototype - prioritize speed and basic functionality over optimization" - M35
- [ ] **Example Prompt Addition**: *"Since this is for the MVP, I'll focus on tasks that get core functionality working quickly rather than over-engineering."* - M36
- [ ] **Production/Mature Tags** (`v1.0+`, `production`, `stable`): - M37
- [ ] **Your Approach**: Emphasize robustness, testing, and maintainability - M38
- [ ] **Task Generation**: Include comprehensive error handling, testing, documentation, and optimization - M39
- [ ] **Complexity Level**: Higher complexity, more detailed subtasks, thorough implementation paths - M40
- [ ] **Research Prompts**: Include context like "This is for production - prioritize reliability, performance, and maintainability" - M41
- [ ] **Example Prompt Addition**: *"Since this is for production, I'll ensure tasks include proper error handling, testing, and documentation."* - M42
- [ ] User mentions teammates or collaboration needs - M43
- [ ] Project has grown to 15+ tasks with mixed priorities - M44
- [ ] User creates feature branches or mentions major initiatives - M45
- [ ] User initializes Taskmaster on an existing, complex codebase - M46
- [ ] User describes large features that would benefit from dedicated planning - M47
- [ ] **High-level deliverables** that provide significant business value - M48
- [ ] **Major milestones** and epic-level features - M49
- [ ] **Critical infrastructure** work that affects the entire project - M50
- [ ] **Release-blocking** items - M51
- [ ] Detailed implementation subtasks (these go in feature-specific tags' parent tasks) - M52
- [ ] Refactoring work (create dedicated tags like `refactor-auth`) - M53
- [ ] Experimental features (use `experiment-*` tags) - M54
- [ ] Team member-specific tasks (use person-specific tags) - M55
- [ ] `parse_prd .taskmaster/docs/feature-[name]-prd.txt --tag=feature-[name]` - M56
- [ ] `analyze_project_complexity --tag=feature-[name] --research` - M57
- [ ] `expand_all --tag=feature-[name] --research` - M58
- [ ] Current state analysis (based on your codebase research) - M59
- [ ] Proposed improvements or new features - M60
- [ ] Implementation strategy considering existing code - M61
- [ ] add_tag my-api-work --copy-from-current --description="My API tasks while collaborating with Alice" - M62
- [ ] For AI agents and integrated development environments (like Trae), interacting via the **MCP server is the preferred method**. - M63
- [ ] The MCP server exposes Taskmaster functionality through a set of tools (e.g., `get_tasks`, `add_subtask`). - M64
- [ ] This method offers better performance, structured data exchange, and richer error handling compared to CLI parsing. - M65
- [ ] Refer to @`mcp.md` for details on the MCP architecture and available tools. - M66
- [ ] A comprehensive list and description of MCP tools and their corresponding CLI commands can be found in @`taskmaster.md`. - M67
- [ ] **Restart the MCP server** if core logic in `scripts/modules` or MCP tool/direct function definitions change. - M68
- [ ] **Note**: MCP tools fully support tagged task lists with complete tag management capabilities. - M69
- [ ] The global `task-master` command provides a user-friendly interface for direct terminal interaction. - M70
- [ ] It can also serve as a fallback if the MCP server is inaccessible or a specific function isn't exposed via MCP. - M71
- [ ] Install globally with `npm install -g task-master-ai` or use locally via `npx task-master-ai ...`. - M72
- [ ] The CLI commands often mirror the MCP tools (e.g., `task-master list` corresponds to `get_tasks`). - M73
- [ ] Refer to @`taskmaster.md` for a detailed command reference. - M74
- [ ] **Tagged Task Lists**: CLI fully supports the new tagged system with seamless migration. - M75
- [ ] **Data Structure**: Tasks are organized into separate contexts (tags) like "master", "feature-branch", or "v2.0". - M76
- [ ] **Silent Migration**: Existing projects automatically migrate to use a "master" tag with zero disruption. - M77
- [ ] **Context Isolation**: Tasks in different tags are completely separate. Changes in one tag do not affect any other tag. - M78
- [ ] **Manual Control**: The user is always in control. There is no automatic switching. You facilitate switching by using `use-tag <name>`. - M79
- [ ] **Full CLI & MCP Support**: All tag management commands are available through both the CLI and MCP tools for you to use. Refer to @`taskmaster.md` for a full command list. - M80
- [ ] Run `analyze_project_complexity` / `task-master analyze-complexity --research` (see @`taskmaster.md`) for comprehensive analysis - M81
- [ ] Review complexity report via `complexity_report` / `task-master complexity-report` (see @`taskmaster.md`) for a formatted, readable version. - M82
- [ ] Focus on tasks with highest complexity scores (8-10) for detailed breakdown - M83
- [ ] Use analysis results to determine appropriate subtask allocation - M84
- [ ] Note that reports are automatically used by the `expand_task` tool/command - M85
- [ ] Use `expand_task` / `task-master expand --id=<id>`. It automatically uses the complexity report if found, otherwise generates default number of subtasks. - M86
- [ ] Use `--num=<number>` to specify an explicit number of subtasks, overriding defaults or complexity report recommendations. - M87
- [ ] Add `--research` flag to leverage Perplexity AI for research-backed expansion. - M88
- [ ] Add `--force` flag to clear existing subtasks before generating new ones (default is to append). - M89
- [ ] Use `--prompt="<context>"` to provide additional context when needed. - M90
- [ ] Review and adjust generated subtasks as necessary. - M91
- [ ] Use `expand_all` tool or `task-master expand --all` to expand multiple pending tasks at once, respecting flags like `--force` and `--research`. - M92
- [ ] If subtasks need complete replacement (regardless of the `--force` flag on `expand`), clear them first with `clear_subtasks` / `task-master clear-subtasks --id=<id>`. - M93
- [ ] When implementation differs significantly from planned approach - M94
- [ ] When future tasks need modification due to current implementation choices - M95
- [ ] When new dependencies or requirements emerge - M96
- [ ] Use `update` / `task-master update --from=<futureTaskId> --prompt='<explanation>\nUpdate context...' --research` to update multiple future tasks. - M97
- [ ] Use `update_task` / `task-master update-task --id=<taskId> --prompt='<explanation>\nUpdate context...' --research` to update a single specific task. - M98
- [ ] Use 'pending' for tasks ready to be worked on - M99
- [ ] Use 'done' for completed and verified tasks - M100
- [ ] Use 'deferred' for postponed tasks - M101
- [ ] Add custom status values as needed for project-specific workflows - M102
- [ ] **id**: Unique identifier for the task (Example: `1`, `1.1`) - M103
- [ ] **title**: Brief, descriptive title (Example: `"Initialize Repo"`) - M104
- [ ] **description**: Concise summary of what the task involves (Example: `"Create a new repository, set up initial structure."`) - M105
- [ ] **status**: Current state of the task (Example: `"pending"`, `"done"`, `"deferred"`) - M106
- [ ] **dependencies**: IDs of prerequisite tasks (Example: `[1, 2.1]`) - M107
- [ ] Dependencies are displayed with status indicators (✅ for completed, ⏱️ for pending) - M108
- [ ] This helps quickly identify which prerequisite tasks are blocking work - M109
- [ ] **priority**: Importance level (Example: `"high"`, `"medium"`, `"low"`) - M110
- [ ] **details**: In-depth implementation instructions (Example: `"Use GitHub client ID/secret, handle callback, set session token."`) - M111
- [ ] **testStrategy**: Verification approach (Example: `"Deploy and call endpoint to confirm 'Hello World' response."`) - M112
- [ ] **subtasks**: List of smaller, more specific tasks (Example: `[{"id": 1, "title": "Configure OAuth", ...}]`) - M113
- [ ] Refer to task structure details (previously linked to `tasks.md`). - M114
- [ ] Located in the project root directory. - M115
- [ ] Stores most configuration settings: AI model selections (main, research, fallback), parameters (max tokens, temperature), logging level, default subtasks/priority, project name, etc. - M116
- [ ] **Tagged System Settings**: Includes `global.defaultTag` (defaults to "master") and `tags` section for tag management configuration. - M117
- [ ] **Managed via `task-master models --setup` command.** Do not edit manually unless you know what you are doing. - M118
- [ ] **View/Set specific models via `task-master models` command or `models` MCP tool.** - M119
- [ ] Created automatically when you run `task-master models --setup` for the first time or during tagged system migration. - M120
- [ ] Used **only** for sensitive API keys and specific endpoint URLs. - M121
- [ ] Place API keys (one per provider) in a `.env` file in the project root for CLI usage. - M122
- [ ] For MCP/Trae integration, configure these keys in the `env` section of `.trae/mcp.json`. - M123
- [ ] Available keys/variables: See `assets/env.example` or the Configuration section in the command reference (previously linked to `taskmaster.md`). - M124
- [ ] Tracks current tag context and migration status. - M125
- [ ] Automatically created during tagged system migration. - M126
- [ ] Contains: `currentTag`, `lastSwitched`, `migrationNoticeShown`. - M127
- [ ] **Available Profiles**: Claude Code, Cline, Codex, Trae, Roo Code, Trae, Windsurf (claude, cline, codex, trae, roo, trae, windsurf) - M128
- [ ] **During Initialization**: Use `task-master init --rules trae,windsurf` to specify which rule sets to include - M129
- [ ] **After Initialization**: Use `task-master rules add <profiles>` or `task-master rules remove <profiles>` to manage rule sets - M130
- [ ] **Interactive Setup**: Use `task-master rules setup` to launch an interactive prompt for selecting rule profiles - M131
- [ ] **Default Behavior**: If no `--rules` flag is specified during initialization, all available rule profiles are included - M132
- [ ] **Rule Structure**: Each profile creates its own directory (e.g., `.trae/rules`, `.roo/rules`) with appropriate configuration files - M133
- [ ] Run `next_task` / `task-master next` to show the next task to work on. - M134
- [ ] The command identifies tasks with all dependencies satisfied - M135
- [ ] Tasks are prioritized by priority level, dependency count, and ID - M136
- [ ] The command shows comprehensive task information including: - M137
- [ ] Basic task details and description - M138
- [ ] Implementation details - M139
- [ ] Subtasks (if they exist) - M140
- [ ] Contextual suggested actions - M141
- [ ] Recommended before starting any new development work - M142
- [ ] Respects your project's dependency structure - M143
- [ ] Ensures tasks are completed in the appropriate sequence - M144
- [ ] Provides ready-to-use commands for common task actions - M145
- [ ] Run `get_task` / `task-master show <id>` to view a specific task. - M146
- [ ] Use dot notation for subtasks: `task-master show 1.2` (shows subtask 2 of task 1) - M147
- [ ] Displays comprehensive information similar to the next command, but for a specific task - M148
- [ ] For parent tasks, shows all subtasks and their current status - M149
- [ ] For subtasks, shows parent task information and relationship - M150
- [ ] Provides contextual suggested actions appropriate for the specific task - M151
- [ ] Useful for examining task details before implementation or checking status - M152
- [ ] Use `add_dependency` / `task-master add-dependency --id=<id> --depends-on=<id>` to add a dependency. - M153
- [ ] Use `remove_dependency` / `task-master remove-dependency --id=<id> --depends-on=<id>` to remove a dependency. - M154
- [ ] The system prevents circular dependencies and duplicate dependency entries - M155
- [ ] Dependencies are checked for existence before being added or removed - M156
- [ ] Task files are automatically regenerated after dependency changes - M157
- [ ] Dependencies are visualized with status indicators in task listings and files - M158
- [ ] Use `move_task` / `task-master move --from=<id> --to=<id>` to move tasks or subtasks within the hierarchy - M159
- [ ] This command supports several use cases: - M160
- [ ] Moving a standalone task to become a subtask (e.g., `--from=5 --to=7`) - M161
- [ ] Moving a subtask to become a standalone task (e.g., `--from=5.2 --to=7`) - M162
- [ ] Moving a subtask to a different parent (e.g., `--from=5.2 --to=7.3`) - M163
- [ ] Reordering subtasks within the same parent (e.g., `--from=5.2 --to=5.4`) - M164
- [ ] Moving a task to a new, non-existent ID position (e.g., `--from=5 --to=25`) - M165
- [ ] Moving multiple tasks at once using comma-separated IDs (e.g., `--from=10,11,12 --to=16,17,18`) - M166
- [ ] The system includes validation to prevent data loss: - M167
- [ ] Allows moving to non-existent IDs by creating placeholder tasks - M168
- [ ] Prevents moving to existing task IDs that have content (to avoid overwriting) - M169
- [ ] Validates source tasks exist before attempting to move them - M170
- [ ] The system maintains proper parent-child relationships and dependency integrity - M171
- [ ] Task files are automatically regenerated after the move operation - M172
- [ ] This provides greater flexibility in organizing and refining your task structure as project understanding evolves - M173
- [ ] This is especially useful when dealing with potential merge conflicts arising from teams creating tasks on separate branches. Solve these conflicts very easily by moving your tasks and keeping theirs. - M174
- [ ] Use `get_task` / `task-master show <subtaskId>` (see @`taskmaster.md`) to thoroughly understand the specific goals and requirements of the subtask. - M175
- [ ] This is the first attempt at creating a concrete implementation plan. - M176
- [ ] Explore the codebase to identify the precise files, functions, and even specific lines of code that will need modification. - M177
- [ ] Determine the intended code changes (diffs) and their locations. - M178
- [ ] Gather *all* relevant details from this exploration phase. - M179
- [ ] Run `update_subtask` / `task-master update-subtask --id=<subtaskId> --prompt='<detailed plan>'`. - M180
- [ ] Provide the *complete and detailed* findings from the exploration phase in the prompt. Include file paths, line numbers, proposed diffs, reasoning, and any potential challenges identified. Do not omit details. The goal is to create a rich, timestamped log within the subtask's `details`. - M181
- [ ] Run `get_task` / `task-master show <subtaskId>` again to confirm that the detailed implementation plan has been successfully appended to the subtask's details. - M182
- [ ] Set the subtask status using `set_task_status` / `task-master set-status --id=<subtaskId> --status=in-progress`. - M183
- [ ] Start coding based on the logged plan. - M184
- [ ] As implementation progresses, you will encounter challenges, discover nuances, or confirm successful approaches. - M185
- [ ] **Before appending new information**: Briefly review the *existing* details logged in the subtask (using `get_task` or recalling from context) to ensure the update adds fresh insights and avoids redundancy. - M186
- [ ] **Regularly** use `update_subtask` / `task-master update-subtask --id=<subtaskId> --prompt='<update details>\n- What worked...\n- What didn't work...'` to append new findings. - M187
- [ ] **Crucially, log:** - M188
- [ ] What worked ("fundamental truths" discovered). - M189
- [ ] What didn't work and why (to avoid repeating mistakes). - M190
- [ ] Specific code snippets or configurations that were successful. - M191
- [ ] Decisions made, especially if confirmed with user input. - M192
- [ ] Any deviations from the initial plan and the reasoning. - M193
- [ ] The objective is to continuously enrich the subtask's details, creating a log of the implementation journey that helps the AI (and human developers) learn, adapt, and avoid repeating errors. - M194
- [ ] Once the implementation for the subtask is functionally complete, review all code changes and the relevant chat history. - M195
- [ ] Identify any new or modified code patterns, conventions, or best practices established during the implementation. - M196
- [ ] Create new or update existing rules following internal guidelines (previously linked to `cursor_rules.md` and `self_improve.md`). - M197
- [ ] After verifying the implementation and updating any necessary rules, mark the subtask as completed: `set_task_status` / `task-master set-status --id=<subtaskId> --status=done`. - M198
- [ ] Stage the relevant code changes and any updated/new rule files (`git add .`). - M199
- [ ] Craft a comprehensive Git commit message summarizing the work done for the subtask, including both code implementation and any rule adjustments. - M200
- [ ] Execute the commit command directly in the terminal (e.g., `git commit -m 'feat(module): Implement feature X for subtask <subtaskId>\n\n- Details about changes...\n- Updated rule Y for pattern Z'`). - M201
- [ ] Consider if a Changeset is needed according to internal versioning guidelines (previously linked to `changeset.md`). If so, run `npm run changeset`, stage the generated file, and amend the commit or create a new one. - M202
- [ ] Identify the next subtask (e.g., using `next_task` / `task-master next`). - M203
- [ ] **Top-Level Function Search**: - M204
- [ ] Useful for understanding module structure or planning refactors. - M205
- [ ] Use grep/ripgrep to find exported functions/constants: - M206
- [ ] Can help compare functions between files during migrations or identify potential naming conflicts. - M207

### Documentation Tasks

- [ ] Update content accuracy - M1
- [ ] Add code examples - M2
- [ ] Add diagrams/screenshots - M3
- [ ] Review and proofread - M4
- [ ] Add cross-references - M5

