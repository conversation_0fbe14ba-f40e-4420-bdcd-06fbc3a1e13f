---
description: Comprehensive guide to Vite best practices
globs: *.ts,*.tsx,*.js,*.jsx
alwaysApply: false
---
---
description: This rule provides comprehensive best practices, coding standards, and guidelines for developing applications using Vite, covering aspects from code organization and performance to security and testing.
globs: **/*.{js,jsx,ts,tsx,vue,svelte}
---
- **Introduction:**
  - This document outlines best practices for developing applications using Vite, a fast and opinionated build tool that aims to provide a better development experience.

- **Prerequisites:**
  - Ensure Node.js and npm/yarn/pnpm are installed.
  - Familiarity with JavaScript/TypeScript, HTML, and CSS.

- **Code Organization and Structure:**
  - **Directory Structure:**
    - Adopt a modular structure based on features or components.
    
    src/
    ├── components/
    │   ├── Button/
    │   │   ├── Button.tsx
    │   │   ├── Button.module.css
    │   │   └── Button.test.tsx
    │   ├── Input/
    │   │   └── ...
    ├── pages/
    │   ├── Home.tsx
    │   ├── About.tsx
    │   └── ...
    ├── services/
    │   ├── api.ts
    │   └── ...
    ├── utils/
    │   ├── helpers.ts
    │   └── ...
    ├── App.tsx
    ├── main.tsx
    └── vite-env.d.ts
    
  - **File Naming Conventions:**
    - Use descriptive and consistent names.
    - Component files: `ComponentName.tsx` or `component-name.tsx`.
    - Style files: `ComponentName.module.css` or `component-name.module.css`.
    - Test files: `ComponentName.test.tsx` or `component-name.test.tsx`.
  - **Module Organization:**
    - Group related files into modules or folders.
    - Use `index.ts` (barrel files) to simplify imports.
    typescript
    // src/components/Button/index.ts
    export { default as Button } from './Button';
    
  - **Component Architecture:**
    - Favor small, reusable components.
    - Utilize functional components and hooks in React (or equivalent in Vue/Svelte).
    - Separate concerns: presentational vs. container components.
  - **Code Splitting Strategies:**
    - Use dynamic imports (`import()`) for lazy loading.
    - Split routes using `React.lazy` or Vue's dynamic component feature.
    - Configure Vite's `rollupOptions.output.manualChunks` for fine-grained control.

- **Common Patterns and Anti-patterns:**
  - **Design Patterns:**
    - **Higher-Order Components (HOCs):**  Carefully consider alternatives like render props or hooks for better composability.
    - **Render Props:**  Useful for sharing logic between components, but can lead to deeply nested structures.
    - **Hooks:**  Promote code reuse and simplify component logic.
  - **Recommended Approaches:**
    - Use environment variables for configuration.
    - Implement a consistent API client for data fetching.
    - Centralize state management using libraries like Redux, Zustand, or Vuex.
  - **Anti-patterns:**
    - Avoid deeply nested component trees without proper optimization.
    - Don't mutate state directly; use setState or Vue's reactivity system.
    - Overusing global styles; prefer CSS modules or styled components.
  - **State Management:**
    - Choose a state management solution based on application complexity.
    - Use Redux for complex state management with predictable state transitions and time travel debugging.
    - Consider Zustand or Jotai for simpler state management with a smaller bundle size.
    - For Vue, Vuex or Pinia are popular choices.
  - **Error Handling:**
    - Implement global error boundaries to catch unhandled exceptions.
    - Use try-catch blocks for local error handling.
    - Log errors to a central error tracking service (e.g., Sentry, Rollbar).

- **Performance Considerations:**
  - **Optimization Techniques:**
    - Use production-ready code minification and bundling.
    - Optimize images and other assets using tools like `imagemin` or Vite plugins.
  - **Memory Management:**
    - Avoid memory leaks by properly cleaning up event listeners and subscriptions.
    - Use `useEffect` with a cleanup function in React (or `onUnmounted` in Vue).
  - **Rendering Optimization:**
    - Use memoization techniques (`React.memo`, `useMemo`, `shouldComponentUpdate`) to prevent unnecessary re-renders.
    - Virtualize large lists using libraries like `react-window` or `react-virtualized`.
  - **Bundle Size Optimization:**
    - Analyze bundle size using `rollup-plugin-visualizer` or similar tools.
    - Remove unused code using tree shaking.
    - Use code splitting to load only necessary code.
  - **Lazy Loading:**
    - Lazy load components and images that are not immediately visible.
    - Use `IntersectionObserver` to trigger loading when elements enter the viewport.

- **Security Best Practices:**
  - **Common Vulnerabilities:**
    - Cross-Site Scripting (XSS): Sanitize user input to prevent XSS attacks.
    - Cross-Site Request Forgery (CSRF): Use CSRF tokens to protect against CSRF attacks.
    - Injection Attacks: Validate and sanitize input to prevent SQL injection and other injection attacks.
  - **Input Validation:**
    - Validate all user input on both the client and server side.
    - Use a library like `yup` or `joi` for schema validation.
  - **Authentication and Authorization:**
    - Use a secure authentication and authorization mechanism (e.g., OAuth 2.0, JWT).
    - Store passwords securely using bcrypt or Argon2.
  - **Data Protection:**
    - Encrypt sensitive data at rest and in transit.
    - Use HTTPS for all communication.
  - **Secure API Communication:**
    - Implement proper CORS configuration to prevent unauthorized access to your API.
    - Rate limit API requests to prevent abuse.

- **Testing Approaches:**
  - **Unit Testing:**
    - Write unit tests for individual components and functions.
    - Use testing libraries like Jest, Mocha, or Vitest.
    - Mock dependencies to isolate units under test.
  - **Integration Testing:**
    - Test the interaction between different parts of your application.
    - Use testing libraries like React Testing Library or Vue Test Utils.
  - **End-to-End Testing:**
    - Test the entire application from the user's perspective.
    - Use tools like Cypress or Playwright.
  - **Test Organization:**
    - Organize tests into folders based on features or components.
    - Use descriptive test names.
  - **Mocking and Stubbing:**
    - Use mocks and stubs to isolate units under test.
    - Avoid over-mocking; test the actual implementation whenever possible.

- **Common Pitfalls and Gotchas:**
  - **Frequent Mistakes:**
    - Improperly handling asynchronous operations.
    - Neglecting accessibility considerations.
    - Using outdated dependencies.
  - **Edge Cases:**
    - Handling different screen sizes and devices.
    - Supporting internationalization and localization.
    - Dealing with slow network connections.
  - **Version-Specific Issues:**
    - Be aware of breaking changes in Vite and its plugins.
    - Pin dependencies to specific versions to avoid unexpected issues.
  - **Compatibility Concerns:**
    - Test your application in different browsers and devices.
    - Use polyfills to support older browsers.
  - **Debugging Strategies:**
    - Use browser developer tools to inspect the DOM, network requests, and console output.
    - Use debugging tools like `debugger` or `console.log`.

- **Tooling and Environment:**
  - **Recommended Tools:**
    - VS Code with extensions like ESLint, Prettier, and TypeScript.
    - Chrome DevTools or Firefox Developer Tools.
    - npm/yarn/pnpm for package management.
  - **Build Configuration:**
    - Configure Vite using `vite.config.ts` or `vite.config.js`.
    - Customize build options like `outDir`, `assetsDir`, and `rollupOptions`.
  - **Linting and Formatting:**
    - Use ESLint with recommended rulesets (e.g., `eslint:recommended`, `plugin:react/recommended`).
    - Use Prettier for code formatting.
    - Configure ESLint and Prettier to work together.
  - **Deployment Best Practices:**
    - Deploy to a CDN for optimal performance.
    - Use environment variables for configuration.
    - Set up proper caching headers.
  - **CI/CD Integration:**
    - Integrate with a CI/CD pipeline for automated testing and deployment.
    - Use tools like GitHub Actions, GitLab CI, or CircleCI.

- **TypeScript Best Practices (when using TypeScript):**
  - **Strict Type-Checking:**
    - Enable strict type-checking options in `tsconfig.json` (e.g., `strict: true`, `noImplicitAny: true`, `strictNullChecks: true`).
  - **Typing Props and State:**
    - Use interfaces or types to define the shape of props and state.
    typescript
    interface ButtonProps {
      label: string;
      onClick: () => void;
    }

    const Button: React.FC<ButtonProps> = ({ label, onClick }) => {
      return <button onClick={onClick}>{label}</button>;
    };
    

- **ESLint Configuration (Example):**
  javascript
  module.exports = {
    env: {
      browser: true,
      es2021: true,
      node: true,
    },
    extends: [
      'eslint:recommended',
      'plugin:react/recommended',
      'plugin:@typescript-eslint/recommended',
      'prettier',
    ],
    parser: '@typescript-eslint/parser',
    parserOptions: {
      ecmaFeatures: {
        jsx: true,
      },
      ecmaVersion: 12,
      sourceType: 'module',
    },
    plugins: ['react', '@typescript-eslint', 'prettier'],
    rules: {
      'prettier/prettier': 'error',
      'react/react-in-jsx-scope': 'off',
      '@typescript-eslint/explicit-function-return-type': 'off',
      '@typescript-eslint/no-explicit-any': 'warn',
    },
  };
  

- **Conclusion:**
  - Following these best practices will help you build efficient, maintainable, and secure applications with Vite.  Continuously review and update your practices as the library and ecosystem evolve.

## Tasks

### Extracted Tasks

- [ ] **Introduction:** - M1
- [ ] This document outlines best practices for developing applications using Vite, a fast and opinionated build tool that aims to provide a better development experience. - M2
- [ ] **Prerequisites:** - M3
- [ ] Ensure Node.js and npm/yarn/pnpm are installed. - M4
- [ ] Familiarity with JavaScript/TypeScript, HTML, and CSS. - M5
- [ ] **Code Organization and Structure:** - M6
- [ ] **Directory Structure:** - M7
- [ ] Adopt a modular structure based on features or components. - M8
- [ ] **File Naming Conventions:** - M9
- [ ] Use descriptive and consistent names. - M10
- [ ] Component files: `ComponentName.tsx` or `component-name.tsx`. - M11
- [ ] Style files: `ComponentName.module.css` or `component-name.module.css`. - M12
- [ ] Test files: `ComponentName.test.tsx` or `component-name.test.tsx`. - M13
- [ ] **Module Organization:** - M14
- [ ] Group related files into modules or folders. - M15
- [ ] Use `index.ts` (barrel files) to simplify imports. - M16
- [ ] **Component Architecture:** - M17
- [ ] Favor small, reusable components. - M18
- [ ] Utilize functional components and hooks in React (or equivalent in Vue/Svelte). - M19
- [ ] Separate concerns: presentational vs. container components. - M20
- [ ] **Code Splitting Strategies:** - M21
- [ ] Use dynamic imports (`import()`) for lazy loading. - M22
- [ ] Split routes using `React.lazy` or Vue's dynamic component feature. - M23
- [ ] Configure Vite's `rollupOptions.output.manualChunks` for fine-grained control. - M24
- [ ] **Common Patterns and Anti-patterns:** - M25
- [ ] **Design Patterns:** - M26
- [ ] **Higher-Order Components (HOCs):**  Carefully consider alternatives like render props or hooks for better composability. - M27
- [ ] **Render Props:**  Useful for sharing logic between components, but can lead to deeply nested structures. - M28
- [ ] **Hooks:**  Promote code reuse and simplify component logic. - M29
- [ ] **Recommended Approaches:** - M30
- [ ] Use environment variables for configuration. - M31
- [ ] Implement a consistent API client for data fetching. - M32
- [ ] Centralize state management using libraries like Redux, Zustand, or Vuex. - M33
- [ ] **Anti-patterns:** - M34
- [ ] Avoid deeply nested component trees without proper optimization. - M35
- [ ] Don't mutate state directly; use setState or Vue's reactivity system. - M36
- [ ] Overusing global styles; prefer CSS modules or styled components. - M37
- [ ] **State Management:** - M38
- [ ] Choose a state management solution based on application complexity. - M39
- [ ] Use Redux for complex state management with predictable state transitions and time travel debugging. - M40
- [ ] Consider Zustand or Jotai for simpler state management with a smaller bundle size. - M41
- [ ] For Vue, Vuex or Pinia are popular choices. - M42
- [ ] **Error Handling:** - M43
- [ ] Implement global error boundaries to catch unhandled exceptions. - M44
- [ ] Use try-catch blocks for local error handling. - M45
- [ ] Log errors to a central error tracking service (e.g., Sentry, Rollbar). - M46
- [ ] **Performance Considerations:** - M47
- [ ] **Optimization Techniques:** - M48
- [ ] Use production-ready code minification and bundling. - M49
- [ ] Optimize images and other assets using tools like `imagemin` or Vite plugins. - M50
- [ ] **Memory Management:** - M51
- [ ] Avoid memory leaks by properly cleaning up event listeners and subscriptions. - M52
- [ ] Use `useEffect` with a cleanup function in React (or `onUnmounted` in Vue). - M53
- [ ] **Rendering Optimization:** - M54
- [ ] Use memoization techniques (`React.memo`, `useMemo`, `shouldComponentUpdate`) to prevent unnecessary re-renders. - M55
- [ ] Virtualize large lists using libraries like `react-window` or `react-virtualized`. - M56
- [ ] **Bundle Size Optimization:** - M57
- [ ] Analyze bundle size using `rollup-plugin-visualizer` or similar tools. - M58
- [ ] Remove unused code using tree shaking. - M59
- [ ] Use code splitting to load only necessary code. - M60
- [ ] **Lazy Loading:** - M61
- [ ] Lazy load components and images that are not immediately visible. - M62
- [ ] Use `IntersectionObserver` to trigger loading when elements enter the viewport. - M63
- [ ] **Security Best Practices:** - M64
- [ ] **Common Vulnerabilities:** - M65
- [ ] Cross-Site Scripting (XSS): Sanitize user input to prevent XSS attacks. - M66
- [ ] Cross-Site Request Forgery (CSRF): Use CSRF tokens to protect against CSRF attacks. - M67
- [ ] Injection Attacks: Validate and sanitize input to prevent SQL injection and other injection attacks. - M68
- [ ] **Input Validation:** - M69
- [ ] Validate all user input on both the client and server side. - M70
- [ ] Use a library like `yup` or `joi` for schema validation. - M71
- [ ] **Authentication and Authorization:** - M72
- [ ] Use a secure authentication and authorization mechanism (e.g., OAuth 2.0, JWT). - M73
- [ ] Store passwords securely using bcrypt or Argon2. - M74
- [ ] **Data Protection:** - M75
- [ ] Encrypt sensitive data at rest and in transit. - M76
- [ ] Use HTTPS for all communication. - M77
- [ ] **Secure API Communication:** - M78
- [ ] Implement proper CORS configuration to prevent unauthorized access to your API. - M79
- [ ] Rate limit API requests to prevent abuse. - M80
- [ ] **Testing Approaches:** - M81
- [ ] **Unit Testing:** - M82
- [ ] Write unit tests for individual components and functions. - M83
- [ ] Use testing libraries like Jest, Mocha, or Vitest. - M84
- [ ] Mock dependencies to isolate units under test. - M85
- [ ] **Integration Testing:** - M86
- [ ] Test the interaction between different parts of your application. - M87
- [ ] Use testing libraries like React Testing Library or Vue Test Utils. - M88
- [ ] **End-to-End Testing:** - M89
- [ ] Test the entire application from the user's perspective. - M90
- [ ] Use tools like Cypress or Playwright. - M91
- [ ] **Test Organization:** - M92
- [ ] Organize tests into folders based on features or components. - M93
- [ ] Use descriptive test names. - M94
- [ ] **Mocking and Stubbing:** - M95
- [ ] Use mocks and stubs to isolate units under test. - M96
- [ ] Avoid over-mocking; test the actual implementation whenever possible. - M97
- [ ] **Common Pitfalls and Gotchas:** - M98
- [ ] **Frequent Mistakes:** - M99
- [ ] Improperly handling asynchronous operations. - M100
- [ ] Neglecting accessibility considerations. - M101
- [ ] Using outdated dependencies. - M102
- [ ] **Edge Cases:** - M103
- [ ] Handling different screen sizes and devices. - M104
- [ ] Supporting internationalization and localization. - M105
- [ ] Dealing with slow network connections. - M106
- [ ] **Version-Specific Issues:** - M107
- [ ] Be aware of breaking changes in Vite and its plugins. - M108
- [ ] Pin dependencies to specific versions to avoid unexpected issues. - M109
- [ ] **Compatibility Concerns:** - M110
- [ ] Test your application in different browsers and devices. - M111
- [ ] Use polyfills to support older browsers. - M112
- [ ] **Debugging Strategies:** - M113
- [ ] Use browser developer tools to inspect the DOM, network requests, and console output. - M114
- [ ] Use debugging tools like `debugger` or `console.log`. - M115
- [ ] **Tooling and Environment:** - M116
- [ ] **Recommended Tools:** - M117
- [ ] VS Code with extensions like ESLint, Prettier, and TypeScript. - M118
- [ ] Chrome DevTools or Firefox Developer Tools. - M119
- [ ] npm/yarn/pnpm for package management. - M120
- [ ] **Build Configuration:** - M121
- [ ] Configure Vite using `vite.config.ts` or `vite.config.js`. - M122
- [ ] Customize build options like `outDir`, `assetsDir`, and `rollupOptions`. - M123
- [ ] **Linting and Formatting:** - M124
- [ ] Use ESLint with recommended rulesets (e.g., `eslint:recommended`, `plugin:react/recommended`). - M125
- [ ] Use Prettier for code formatting. - M126
- [ ] Configure ESLint and Prettier to work together. - M127
- [ ] **Deployment Best Practices:** - M128
- [ ] Deploy to a CDN for optimal performance. - M129
- [ ] Use environment variables for configuration. - M130
- [ ] Set up proper caching headers. - M131
- [ ] **CI/CD Integration:** - M132
- [ ] Integrate with a CI/CD pipeline for automated testing and deployment. - M133
- [ ] Use tools like GitHub Actions, GitLab CI, or CircleCI. - M134
- [ ] **TypeScript Best Practices (when using TypeScript):** - M135
- [ ] **Strict Type-Checking:** - M136
- [ ] Enable strict type-checking options in `tsconfig.json` (e.g., `strict: true`, `noImplicitAny: true`, `strictNullChecks: true`). - M137
- [ ] **Typing Props and State:** - M138
- [ ] Use interfaces or types to define the shape of props and state. - M139
- [ ] **ESLint Configuration (Example):** - M140
- [ ] **Conclusion:** - M141
- [ ] Following these best practices will help you build efficient, maintainable, and secure applications with Vite.  Continuously review and update your practices as the library and ecosystem evolve. - M142

### Documentation Tasks

- [ ] Update content accuracy - M1
- [ ] Add code examples - M2
- [ ] Add diagrams/screenshots - M3
- [ ] Review and proofread - M4
- [ ] Add cross-references - M5

