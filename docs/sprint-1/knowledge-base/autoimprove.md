---
description: 
globs: 
alwaysApply: true
---
# Auto-Improve All Existing Rules

## Overview
This meta-rule automatically monitors, analyzes, and enhances all existing `.md` rule files based on user interactions, testing outcomes, and evolving best practices. The AI agent continuously learns from development patterns and updates rules to maintain optimal development workflows.

## Auto-Monitoring System

### Continuous Rule Analysis
The agent monitors all existing rule files:
- `biome.md` - Code formatting and linting standards
- `bun.md` - Bun runtime and package management
- `crm-rule.md` - Customer relationship management patterns
- `docker.md` - Containerization best practices
- `playwright.md` - End-to-end testing automation
- `react.md` - React development patterns
- `react-query.md` - Server state management
- `tailwind.md` - Utility-first CSS framework
- `typescript.md` - Type-safe JavaScript development
- `use-bun-instead-of-node-vite-npm-pnpm.md` - Runtime preference guidelines
- `vite.md` - Build tool configuration
- `vitest.md` - Unit testing framework
- `zod.md` - Schema validation patterns

### Learning Triggers
Auto-improvement activates when:

1. **Error Patterns**
   - Missing dependencies or scripts
   - Environment configuration issues
   - Network connectivity problems
   - Database connection failures
   - Build process errors
   - Runtime crashes

2. **User Feedback Patterns**
   - Repeated corrections to generated code
   - Consistent modifications to suggested patterns
   - User explicitly mentions missing practices
   - Performance issues with current recommendations

3. **Testing Outcomes**
   - Failed tests revealing rule gaps
   - Build failures due to outdated configurations
   - Deployment issues from insufficient guidelines
   - Security vulnerabilities in generated code
   - Integration test failures
   - End-to-end test failures

4. **Code Quality Metrics**
   - Increased error rates in specific areas
   - Performance degradation patterns
   - Accessibility issues in generated components
   - Bundle size optimization opportunities
   - Code duplication
   - Technical debt indicators

5. **Technology Evolution**
   - New framework versions with breaking changes
   - Deprecated API usage in existing rules
   - Emerging best practices in the ecosystem
   - Security updates requiring rule modifications
   - New tooling capabilities
   - Platform updates

## Error Pattern Recognition

### 1. Common Error Categories
```typescript
interface ErrorPattern {
  type: 'dependency' | 'configuration' | 'runtime' | 'build' | 'network' | 'database';
  frequency: number;
  impact: 'low' | 'medium' | 'high' | 'critical';
  context: {
    service: string;
    environment: string;
    stack: string[];
    logs: string[];
  };
  solution: {
    immediate: string;
    longTerm: string;
    prevention: string[];
  };
}
```

### 2. Error Analysis Process
1. **Log Collection**
   - Gather service logs
   - Collect error messages
   - Track stack traces
   - Record environment state

2. **Pattern Matching**
   - Compare with known issues
   - Identify root causes
   - Detect common patterns
   - Analyze frequency

3. **Solution Generation**
   - Immediate fixes
   - Long-term improvements
   - Prevention strategies
   - Documentation updates

### 3. Error Prevention Rules
```typescript
interface PreventionRule {
  pattern: RegExp;
  check: () => boolean;
  fix: () => void;
  documentation: string;
}

const commonPreventionRules: PreventionRule[] = [
  {
    pattern: /script.*not.*found/i,
    check: () => validatePackageScripts(),
    fix: () => addMissingScripts(),
    documentation: 'Ensure all required scripts are defined in package.json'
  },
  {
    pattern: /failed.*to.*connect.*to.*database/i,
    check: () => checkDatabaseConnection(),
    fix: () => setupDatabaseConnection(),
    documentation: 'Verify database connection settings and availability'
  }
];
```

## Improvement Implementation

### 1. Rule Update Process
```typescript
interface RuleUpdate {
  file: string;
  section: string;
  changes: {
    type: 'add' | 'modify' | 'remove';
    content: string;
    reason: string;
  }[];
  validation: () => boolean;
}

class RuleUpdater {
  async updateRule(update: RuleUpdate): Promise<boolean> {
    if (!this.validateUpdate(update)) return false;
    
    await this.backupRule(update.file);
    await this.applyChanges(update);
    
    return this.validateResult(update);
  }
}
```

### 2. Validation Steps
1. **Pre-update Checks**
   - Rule file exists
   - Content is valid
   - No conflicts with other rules
   - Dependencies are available

2. **Update Application**
   - Create backup
   - Apply changes
   - Format content
   - Update references

3. **Post-update Validation**
   - Syntax check
   - Integration test
   - Reference validation
   - User notification

### 3. Rollback Mechanism
```typescript
interface RollbackInfo {
  timestamp: Date;
  file: string;
  backup: string;
  changes: RuleUpdate[];
}

class RollbackManager {
  async rollback(info: RollbackInfo): Promise<void> {
    await this.restoreBackup(info);
    await this.notifyStakeholders(info);
    await this.logRollback(info);
  }
}
```

## Continuous Improvement

### 1. Monitoring Metrics
```typescript
interface RuleMetrics {
  usage: {
    frequency: number;
    successRate: number;
    errorRate: number;
  };
  performance: {
    buildTime: number;
    testDuration: number;
    resourceUsage: number;
  };
  quality: {
    codeComplexity: number;
    testCoverage: number;
    maintainability: number;
  };
}
```

### 2. Feedback Collection
1. **Automated Sources**
   - CI/CD pipelines
   - Test results
   - Build logs
   - Performance metrics
   - Error tracking

2. **User Sources**
   - Code reviews
   - Issue reports
   - Feature requests
   - Documentation feedback
   - Usage patterns

### 3. Improvement Prioritization
```typescript
interface ImprovementPriority {
  impact: number;
  effort: number;
  urgency: number;
  dependencies: string[];
  stakeholders: string[];
}

function calculatePriority(improvement: Improvement): number {
  return (improvement.impact * 0.4) +
         (improvement.urgency * 0.3) +
         (1 / improvement.effort * 0.3);
}
```

## Documentation Updates

### 1. Rule Documentation
```typescript
interface RuleDoc {
  title: string;
  description: string;
  examples: {
    good: string[];
    bad: string[];
  };
  relatedRules: string[];
  changelog: {
    date: Date;
    changes: string[];
    reason: string;
  }[];
}
```

### 2. Update Process
1. **Content Updates**
   - Add new patterns
   - Update examples
   - Clarify instructions
   - Add error solutions

2. **Format Updates**
   - Consistent styling
   - Clear structure
   - Proper linking
   - Code highlighting

3. **Version Control**
   - Track changes
   - Maintain history
   - Document reasons
   - Link to issues

## Integration Testing

### 1. Test Scenarios
```typescript
interface TestScenario {
  name: string;
  setup: () => Promise<void>;
  execute: () => Promise<void>;
  verify: () => Promise<boolean>;
  cleanup: () => Promise<void>;
}
```

### 2. Test Coverage
1. **Rule Validation**
   - Syntax checking
   - Reference validation
   - Pattern matching
   - Error handling

2. **Integration Tests**
   - Cross-rule dependencies
   - Tool interactions
   - Environment compatibility
   - Error recovery

3. **Performance Tests**
   - Build time impact
   - Resource usage
   - Scalability checks
   - Load testing

## Deployment Strategy

### 1. Release Process
```typescript
interface ReleaseProcess {
  version: string;
  changes: RuleUpdate[];
  tests: TestScenario[];
  rollback: RollbackInfo;
}
```

### 2. Release Steps
1. **Preparation**
   - Version bump
   - Change documentation
   - Test execution
   - Backup creation

2. **Deployment**
   - Rule updates
   - Reference updates
   - Cache clearing
   - Service restart

3. **Verification**
   - Health checks
   - Integration tests
   - Performance tests
   - User notification

### 3. Monitoring
1. **Health Metrics**
   - Error rates
   - Performance impact
   - Resource usage
   - User feedback

2. **Alert Conditions**
   - Error spikes
   - Performance degradation
   - Resource exhaustion
   - Integration failures

This auto-improvement system ensures that all development rules remain current, effective, and aligned with evolving best practices while maintaining high code quality and developer productivity.

## Auto-Improvement System for Docker and Dependency Management

## Core Improvement Strategies

### 1. **Dependency Resolution Patterns**
- **Bun Dependency Management**
  ```typescript
  interface DependencyImprovementRule {
    pattern: RegExp;
    analysis: (context: CodeContext) => ImprovementSuggestion[];
    priority: 'low' | 'medium' | 'high';
  }

  const bunDependencyRules: DependencyImprovementRule[] = [
    {
      pattern: /bun install(?!.*--frozen-lockfile)/,
      analysis: () => [{
        type: 'dependency',
        message: 'Use --frozen-lockfile to ensure consistent installations',
        fix: 'Replace "bun install" with "bun install --frozen-lockfile"'
      }],
      priority: 'high'
    },
    {
      pattern: /FROM\s+oven\/bun:(?!1\.2\.15)/,
      analysis: () => [{
        type: 'version',
        message: 'Standardize Bun version to 1.2.15 for consistency',
        fix: 'Update Bun image to oven/bun:1.2.15'
      }],
      priority: 'medium'
    }
  ];
  ```

### 2. **Docker Configuration Optimization**
- **Dockerfile Best Practices**
  ```typescript
  interface DockerfileOptimizationRule {
    check: (dockerfile: string) => boolean;
    recommendation: string;
    severity: 'low' | 'medium' | 'high';
  }

  const dockerfileRules: DockerfileOptimizationRule[] = [
    {
      check: (dockerfile) => !dockerfile.includes('--no-cache'),
      recommendation: 'Optimize layer caching in multi-stage builds',
      severity: 'medium'
    },
    {
      check: (dockerfile) => !dockerfile.includes('USER bun'),
      recommendation: 'Run container as non-root user for enhanced security',
      severity: 'high'
    }
  ];
  ```

### 3. **Monorepo Dependency Consolidation**
```typescript
interface MonorepoDependencyConsolidationRule {
  type: 'version' | 'installation' | 'caching';
  analysis: (projectStructure: ProjectStructure) => ImprovementSuggestion[];
}

const monorepoDependencyRules: MonorepoDependencyConsolidationRule[] = [
  {
    type: 'version',
    analysis: (structure) => {
      const inconsistentVersions = detectInconsistentDependencyVersions(structure);
      return inconsistentVersions.map(version => ({
        message: `Standardize dependency version: ${version}`,
        type: 'consolidation'
      }));
    }
  }
];
```

### 4. **Continuous Improvement Workflow**
```typescript
class AutoImproveEngine {
  async analyzeAndImprove(context: ProjectContext): Promise<ImprovementReport> {
    const improvements: Improvement[] = [
      ...this.analyzeBunDependencies(context),
      ...this.analyzeDockerfiles(context),
      ...this.analyzeMonorepoDependencies(context)
    ];

    return this.generateImprovementReport(improvements);
  }

  private analyzeBunDependencies(context: ProjectContext): Improvement[] {
    // Analyze Bun dependency installation patterns
    // Check for frozen lockfile usage
    // Validate version consistency
  }

  private analyzeDockerfiles(context: ProjectContext): Improvement[] {
    // Scan Dockerfiles for security and performance improvements
    // Check multi-stage build optimization
    // Validate non-root user configuration
  }

  private analyzeMonorepoDependencies(context: ProjectContext): Improvement[] {
    // Analyze dependency sharing and optimization
    // Check for redundant dependency installations
    // Validate volume and caching strategies
  }
}
```

## Improvement Prioritization
```typescript
function calculateImprovementPriority(improvement: Improvement): number {
  return (
    improvement.securityImpact * 0.4 +
    improvement.performanceImpact * 0.3 +
    improvement.maintenanceEffort * 0.3
  );
}
```

## Key Improvement Focus Areas
- ✅ Consistent Dependency Management
- ✅ Security Hardening
- ✅ Performance Optimization
- ✅ Build Process Efficiency
- ✅ Monorepo Dependency Sharing

## Recommended Actions
1. Regularly run auto-improvement analysis
2. Review and apply suggested improvements
3. Validate changes through CI/CD pipelines
4. Document and track improvement history

## Monitoring and Reporting
```typescript
interface ImprovementReport {
  totalImprovements: number;
  highPriorityChanges: Improvement[];
  performanceMetrics: {
    buildTimeReduction: number;
    resourceEfficiency: number;
  };
}
```

## Error Recovery and Debugging
- Implement comprehensive logging
- Add detailed error tracking
- Create self-healing mechanisms for common infrastructure issues

## Docker Configuration Auto-Improvement

### Monitoring Patterns
```typescript
interface DockerPattern {
  type: 'copy' | 'user' | 'health' | 'build' | 'security';
  frequency: number;
  impact: 'low' | 'medium' | 'high';
  solution: {
    scriptPath: string;
    implementation: string;
  };
}

const dockerPatterns: DockerPattern[] = [
  {
    type: 'copy',
    frequency: 0,
    impact: 'high',
    solution: {
      scriptPath: 'scripts/docker-copy-configs.sh',
      implementation: 'Use centralized script for file copying'
    }
  },
  {
    type: 'health',
    frequency: 0,
    impact: 'high',
    solution: {
      scriptPath: 'scripts/docker-healthcheck.sh',
      implementation: 'Use standardized health check script'
    }
  }
];
```

### Auto-Detection Rules
```typescript
interface DockerImprovementRule {
  pattern: RegExp;
  check: (dockerfile: string) => boolean;
  fix: () => void;
}

const dockerImprovementRules: DockerImprovementRule[] = [
  {
    pattern: /COPY.*package\.json/,
    check: (dockerfile) => !dockerfile.includes('docker-copy-configs.sh'),
    fix: () => implementCopyConfigsScript()
  },
  {
    pattern: /HEALTHCHECK.*curl/,
    check: (dockerfile) => !dockerfile.includes('docker-healthcheck.sh'),
    fix: () => implementHealthCheckScript()
  }
];
```

### Implementation Strategy
1. **Script Generation**
   - Create reusable scripts for common patterns
   - Place in service-specific scripts directory
   - Update all related Dockerfiles

2. **Pattern Updates**
   - Monitor Dockerfile changes
   - Track script usage frequency
   - Update patterns based on effectiveness

3. **Validation Process**
   - Verify script permissions
   - Test in CI/CD pipeline
   - Monitor container health

## Lessons Learned

### Native Dependencies
- **Problem**: Native dependencies like `@nx/nx-darwin-x64` can fail to install or load properly
- **Impact**: Build tools and development workflows can break
- **Solution**: 
  - Always match native dependency versions with their parent package versions
  - Use resolutions in package.json to enforce consistent versions
  - Clean node_modules and reinstall when updating native dependencies
  - Consider platform-specific dependencies during version updates

### Version Synchronization
- **Problem**: Mismatched versions between related packages can cause runtime errors
- **Impact**: Build failures, runtime crashes, and development workflow disruptions
- **Solution**:
  - Keep related package versions in sync (e.g., nx and @nx/js)
  - Use package resolutions to enforce version consistency
  - Document version dependencies in package.json
  - Test after version updates across all platforms

### Error Recovery
- **Problem**: Build tool errors can be cryptic and hard to diagnose
- **Impact**: Development delays and workflow disruptions
- **Solution**:
  - Document common error patterns and their solutions
  - Maintain a troubleshooting guide
  - Keep track of platform-specific issues
  - Implement automated recovery procedures where possible


## Tasks

### Extracted Tasks

- [ ] `biome.md` - Code formatting and linting standards - M1
- [ ] `bun.md` - Bun runtime and package management - M2
- [ ] `crm-rule.md` - Customer relationship management patterns - M3
- [ ] `docker.md` - Containerization best practices - M4
- [ ] `playwright.md` - End-to-end testing automation - M5
- [ ] `react.md` - React development patterns - M6
- [ ] `react-query.md` - Server state management - M7
- [ ] `tailwind.md` - Utility-first CSS framework - M8
- [ ] `typescript.md` - Type-safe JavaScript development - M9
- [ ] `use-bun-instead-of-node-vite-npm-pnpm.md` - Runtime preference guidelines - M10
- [ ] `vite.md` - Build tool configuration - M11
- [ ] `vitest.md` - Unit testing framework - M12
- [ ] `zod.md` - Schema validation patterns - M13
- [ ] Missing dependencies or scripts - M14
- [ ] Environment configuration issues - M15
- [ ] Network connectivity problems - M16
- [ ] Database connection failures - M17
- [ ] Build process errors - M18
- [ ] Runtime crashes - M19
- [ ] Repeated corrections to generated code - M20
- [ ] Consistent modifications to suggested patterns - M21
- [ ] User explicitly mentions missing practices - M22
- [ ] Performance issues with current recommendations - M23
- [ ] Failed tests revealing rule gaps - M24
- [ ] Build failures due to outdated configurations - M25
- [ ] Deployment issues from insufficient guidelines - M26
- [ ] Security vulnerabilities in generated code - M27
- [ ] Integration test failures - M28
- [ ] End-to-end test failures - M29
- [ ] Increased error rates in specific areas - M30
- [ ] Performance degradation patterns - M31
- [ ] Accessibility issues in generated components - M32
- [ ] Bundle size optimization opportunities - M33
- [ ] Code duplication - M34
- [ ] Technical debt indicators - M35
- [ ] New framework versions with breaking changes - M36
- [ ] Deprecated API usage in existing rules - M37
- [ ] Emerging best practices in the ecosystem - M38
- [ ] Security updates requiring rule modifications - M39
- [ ] New tooling capabilities - M40
- [ ] Platform updates - M41
- [ ] Gather service logs - M42
- [ ] Collect error messages - M43
- [ ] Track stack traces - M44
- [ ] Record environment state - M45
- [ ] Compare with known issues - M46
- [ ] Identify root causes - M47
- [ ] Detect common patterns - M48
- [ ] Analyze frequency - M49
- [ ] Immediate fixes - M50
- [ ] Long-term improvements - M51
- [ ] Prevention strategies - M52
- [ ] Documentation updates - M53
- [ ] Rule file exists - M54
- [ ] Content is valid - M55
- [ ] No conflicts with other rules - M56
- [ ] Dependencies are available - M57
- [ ] Create backup - M58
- [ ] Apply changes - M59
- [ ] Format content - M60
- [ ] Update references - M61
- [ ] Syntax check - M62
- [ ] Integration test - M63
- [ ] Reference validation - M64
- [ ] User notification - M65
- [ ] CI/CD pipelines - M66
- [ ] Test results - M67
- [ ] Performance metrics - M68
- [ ] Error tracking - M69
- [ ] Code reviews - M70
- [ ] Issue reports - M71
- [ ] Feature requests - M72
- [ ] Documentation feedback - M73
- [ ] Usage patterns - M74
- [ ] Add new patterns - M75
- [ ] Update examples - M76
- [ ] Clarify instructions - M77
- [ ] Add error solutions - M78
- [ ] Consistent styling - M79
- [ ] Clear structure - M80
- [ ] Proper linking - M81
- [ ] Code highlighting - M82
- [ ] Track changes - M83
- [ ] Maintain history - M84
- [ ] Document reasons - M85
- [ ] Link to issues - M86
- [ ] Syntax checking - M87
- [ ] Reference validation - M88
- [ ] Pattern matching - M89
- [ ] Error handling - M90
- [ ] Cross-rule dependencies - M91
- [ ] Tool interactions - M92
- [ ] Environment compatibility - M93
- [ ] Error recovery - M94
- [ ] Build time impact - M95
- [ ] Resource usage - M96
- [ ] Scalability checks - M97
- [ ] Load testing - M98
- [ ] Version bump - M99
- [ ] Change documentation - M100
- [ ] Test execution - M101
- [ ] Backup creation - M102
- [ ] Rule updates - M103
- [ ] Reference updates - M104
- [ ] Cache clearing - M105
- [ ] Service restart - M106
- [ ] Health checks - M107
- [ ] Integration tests - M108
- [ ] Performance tests - M109
- [ ] User notification - M110
- [ ] Error rates - M111
- [ ] Performance impact - M112
- [ ] Resource usage - M113
- [ ] User feedback - M114
- [ ] Error spikes - M115
- [ ] Performance degradation - M116
- [ ] Resource exhaustion - M117
- [ ] Integration failures - M118
- [ ] **Bun Dependency Management** - M119
- [ ] **Dockerfile Best Practices** - M120
- [ ] ✅ Consistent Dependency Management - M121
- [ ] ✅ Security Hardening - M122
- [ ] ✅ Performance Optimization - M123
- [ ] ✅ Build Process Efficiency - M124
- [ ] ✅ Monorepo Dependency Sharing - M125
- [ ] Implement comprehensive logging - M126
- [ ] Add detailed error tracking - M127
- [ ] Create self-healing mechanisms for common infrastructure issues - M128
- [ ] Create reusable scripts for common patterns - M129
- [ ] Place in service-specific scripts directory - M130
- [ ] Update all related Dockerfiles - M131
- [ ] Monitor Dockerfile changes - M132
- [ ] Track script usage frequency - M133
- [ ] Update patterns based on effectiveness - M134
- [ ] Verify script permissions - M135
- [ ] Test in CI/CD pipeline - M136
- [ ] Monitor container health - M137
- [ ] **Problem**: Native dependencies like `@nx/nx-darwin-x64` can fail to install or load properly - M138
- [ ] **Impact**: Build tools and development workflows can break - M139
- [ ] **Solution**: - M140
- [ ] Always match native dependency versions with their parent package versions - M141
- [ ] Use resolutions in package.json to enforce consistent versions - M142
- [ ] Clean node_modules and reinstall when updating native dependencies - M143
- [ ] Consider platform-specific dependencies during version updates - M144
- [ ] **Problem**: Mismatched versions between related packages can cause runtime errors - M145
- [ ] **Impact**: Build failures, runtime crashes, and development workflow disruptions - M146
- [ ] **Solution**: - M147
- [ ] Keep related package versions in sync (e.g., nx and @nx/js) - M148
- [ ] Use package resolutions to enforce version consistency - M149
- [ ] Document version dependencies in package.json - M150
- [ ] Test after version updates across all platforms - M151
- [ ] **Problem**: Build tool errors can be cryptic and hard to diagnose - M152
- [ ] **Impact**: Development delays and workflow disruptions - M153
- [ ] **Solution**: - M154
- [ ] Document common error patterns and their solutions - M155
- [ ] Maintain a troubleshooting guide - M156
- [ ] Keep track of platform-specific issues - M157
- [ ] Implement automated recovery procedures where possible - M158

### Documentation Tasks

- [ ] Update content accuracy - M1
- [ ] Add code examples - M2
- [ ] Add diagrams/screenshots - M3
- [ ] Review and proofread - M4
- [ ] Add cross-references - M5

