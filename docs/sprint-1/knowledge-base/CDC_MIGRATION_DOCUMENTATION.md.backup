# Beauty CRM CDC Pipeline Migration Documentation

## Migration Overview
**Date**: 2025-07-03  
**Migration Type**: Debezi<PERSON> → Enhanced Debezium (Simplified Architecture)  
**Reason**: Estuary Flow is cloud-based and doesn't fit our self-hosted Docker infrastructure  

## Current State Documentation

### Pre-Migration CDC Architecture
```
PostgreSQL → Debezium → Kafka → NATS Connector → NATS JetStream
```

### Active Services (Before Migration)
- `debezium-connect`: Running on port 8083
- `kafka`: Running on port 9092  
- `zookeeper`: Supporting Kafka cluster
- `nats-connector`: Custom bridge service
- `debezium-ui`: Management interface
- `debezium-connector-setup`: Configuration service

### Data Backup Status
✅ **Appointment Outbox Data**: Backed up to `/tmp/appointment_outbox_backup_20250704_000056.sql`
- **Total Events**: 10 AppointmentCreated events
- **Source**: appointmentOutbox
- **Backup Method**: pg_dump with --data-only --inserts

✅ **Debezium Connector Config**: Backed up to `/tmp/debezium_connector_config_backup_*.json`
- **Connector Name**: beauty-crm-appointment-outbox-connector
- **Status**: RUNNING before migration

### Current Connector Configuration
- **Database**: beauty_crm_appointment
- **Table**: appointment_outbox
- **Replication Slot**: flow_slot
- **Publication**: flow_publication
- **Kafka Topic**: appointment.events
- **NATS Stream**: APPOINTMENT_EVENTS
- **Subject Pattern**: appointment.events.{eventType}

## Proposed Enhanced Architecture

### New Simplified Pipeline
```
PostgreSQL → Debezium → HTTP Sink → NATS Bridge → NATS JetStream
```

### Key Improvements
1. **Eliminate Kafka Dependency**: Direct HTTP-based communication
2. **Simplified Resource Management**: Fewer moving parts
3. **Enhanced Reliability**: Better error handling and recovery
4. **Improved Monitoring**: Comprehensive health checks
5. **Resource Optimization**: Lower memory and CPU usage

### Benefits Over Estuary Flow
- ✅ **Self-Hosted**: Maintains Docker infrastructure
- ✅ **Cost-Effective**: No cloud service fees
- ✅ **Data Sovereignty**: All data stays local
- ✅ **Custom Integration**: Direct NATS JetStream support
- ✅ **Operational Control**: Full control over deployment

## Rollback Procedures

### Emergency Rollback Steps
1. **Stop New CDC Services**:
   ```bash
   tilt down
   ```

2. **Restore Original Configuration**:
   ```bash
   # Restart with original Debezium setup
   tilt up --debezium --appointment
   ```

3. **Restore Appointment Outbox Data** (if needed):
   ```bash
   docker exec beauty_crm_postgres psql -U beauty_crm -d beauty_crm_appointment < /tmp/appointment_outbox_backup_20250704_000056.sql
   ```

4. **Verify Connector Status**:
   ```bash
   curl http://localhost:8083/connectors/beauty-crm-appointment-outbox-connector/status
   ```

### Data Integrity Verification
```bash
# Check appointment outbox count
docker exec beauty_crm_postgres psql -U beauty_crm -d beauty_crm_appointment -c "SELECT COUNT(*) FROM appointment_outbox;"

# Verify NATS stream
nats stream info APPOINTMENT_EVENTS
```

## Migration Risks and Mitigation

### Identified Risks
1. **Data Loss**: Mitigated by comprehensive backups
2. **Service Downtime**: Planned maintenance window
3. **Configuration Errors**: Rollback procedures documented
4. **Performance Impact**: Resource monitoring in place

### Success Criteria
- [ ] All 10 existing appointment events processed
- [ ] New appointment creation triggers CDC pipeline
- [ ] NATS JetStream receives events with correct subjects
- [ ] No data loss during migration
- [ ] Improved resource utilization
- [ ] Enhanced monitoring and alerting

## Next Steps
1. Implement enhanced Debezium architecture
2. Create new NATS bridge service
3. Configure HTTP sink connector
4. Test end-to-end pipeline
5. Verify performance improvements
6. Update monitoring and alerting

## Contact Information
**Migration Lead**: Senior Software Architect  
**Backup Contact**: DevOps Team  
**Emergency Escalation**: On-call Engineer  

---
*This document serves as the official record of the CDC pipeline migration for Beauty CRM.*
