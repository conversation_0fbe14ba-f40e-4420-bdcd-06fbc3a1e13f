---
description: 
globs: *.ts,*.tsx,*.css,*.less
alwaysApply: false
---
Use Tailwind CSS v4 Only (Not v3), Proper PostCSS Usage, and Merging Guidance

## Rule Name: tailwind_v4_only

## Description: Enforce the use of Tailwind CSS v4.x in all projects. Do not use Tailwind v3.x. Ensure correct PostCSS integration and follow v4 best practices for configuration and merging.

## Rule Content

Always use Tailwind CSS v4.x.

Do not use Tailwind CSS v3.x or earlier in any code, documentation, or configuration.

All code examples and configuration must follow Tailwind v4 syntax and features.

How to Use Tailwind CSS v4 Properly

## Configuration:

Use CSS-first configuration.

Configure themes and customizations directly in your CSS file with the @theme directive.

## Example:

css
@import "tailwindcss";
@theme {
  --font-display: "Satoshi", "sans-serif";
  --color-primary-500: oklch(0.84 0.18 117.33);
}
Do not use tailwind.config.js for new features unless specifically required. If you must import legacy config, use @config in your CSS:

css
@import "tailwindcss";
@config "../../tailwind.config.js";
Use @import "tailwindcss" instead of the old @tailwind base; @tailwind components; @tailwind utilities;.

## PostCSS Integration:

Use the official @tailwindcss/postcss plugin.

Do not use the old tailwindcss PostCSS plugin.

No need for postcss-import or autoprefixer—Tailwind v4 handles these internally.

Example postcss.config.js:

js
export default {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
  },
};


## Merging and Customization:

Use CSS @layer for custom components and utilities:

css
@layer components {
  .card {
    @apply m-10 rounded-lg bg-white;
  }
}
All design tokens (colors, fonts, spacing, etc.) are now available as CSS variables (e.g., var(--color-blue-500)).

Utilities like grid-cols-12, z-40, and opacity-70 work out of the box—no need for extra config.

## How to Use Tailwind v4 (Best Practices)

Use only v4 utilities, variants, and features.

Use new variants and features such as not-*, nth-*, and container queries.

Always prefer CSS variables and the @theme directive for custom design tokens.

Use the new utility and variant directives (@utility, @variant) for custom extensions.

Use the new CLI and Vite plugins: @tailwindcss/cli and @tailwindcss/vite.

## How Not to Use Tailwind v4

Do not use tailwind.config.js for new configuration (except for legacy support via @config).

Do not use v3 syntax, such as @tailwind base; or deprecated utilities (e.g., bg-opacity-*, text-opacity-*).

Do not use third-party PostCSS plugins like postcss-import or autoprefixer—these are not needed in v4.

Do not use old plugin or CLI packages (tailwindcss for PostCSS, old CLI).

Summary Table: Tailwind v4 vs v3 Usage
Feature/Practice	Use in v4?	Use in v3?	Notes
CSS-first config (@theme)	✔️	❌	v4 only
tailwind.config.js	⚠️ (legacy)	✔️	Use @config for legacy only in v4
@import "tailwindcss"	✔️	❌	v4 only
@tailwind base; etc.	❌	✔️	Deprecated in v4
PostCSS plugin	@tailwindcss/postcss	tailwindcss	v4 uses new plugin
Deprecated utilities	❌	✔️	Use new utility syntax in v4
CSS variables	✔️	❌	v4 exposes all tokens as CSS variables


## Migration & Compatibility
- Verify Tailwind v4 stable release before enforcing
- For existing v3 projects, create migration plan
- Check plugin compatibility before upgrading
- Document any v4-specific features used for team awareness


## Tasks

### Extracted Tasks

- [ ] Verify Tailwind v4 stable release before enforcing - M1
- [ ] For existing v3 projects, create migration plan - M2
- [ ] Check plugin compatibility before upgrading - M3
- [ ] Document any v4-specific features used for team awareness - M4

### Documentation Tasks

- [ ] Update content accuracy - M1
- [ ] Add code examples - M2
- [ ] Add diagrams/screenshots - M3
- [ ] Review and proofread - M4
- [ ] Add cross-references - M5

