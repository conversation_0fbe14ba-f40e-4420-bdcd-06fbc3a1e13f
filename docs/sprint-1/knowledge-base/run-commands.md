---
description:
globs:
alwaysApply: false
---
## Standard Run Commands

### Docker Commands

#### Build and Run
```bash
# ✅ DO: Use standardized build commands
docker compose build --no-cache service_name
docker compose up -d service_name

# ❌ DON'T: Use non-standard flags or direct docker build
docker build .
docker-compose up
```

#### Development Mode
```bash
# ✅ DO: Use development-specific commands
docker compose -f docker-compose.yml -f docker-compose.dev.yml up -d

# ❌ DON'T: Mix production and development configs
docker compose up -d
```

#### Health Checks
```bash
# ✅ DO: Use health check script
./scripts/docker-healthcheck.sh

# ❌ DON'T: Direct curl commands
curl http://localhost:port/health
```

### Bun Commands

#### Package Installation
```bash
# ✅ DO: Use frozen lockfile
bun install --frozen-lockfile

# ❌ DON'T: Install without lockfile
bun install
```

#### Development Server
```bash
# ✅ DO: Use bun flag
bun --bun run dev

# ❌ DON'T: Run without bun flag
bun run dev
```

#### Production Build
```bash
# ✅ DO: Use build script
bun run build

# ❌ DON'T: Use direct typescript commands
tsc -p tsconfig.json
```

### Standard Scripts in package.json
```json
{
  "scripts": {
    "dev": "bun --bun run src/index.ts",
    "build": "bun build ./src/index.ts --target node",
    "start": "bun --bun run dist/index.js",
    "test": "bun test",
    "docker:build": "docker compose build",
    "docker:up": "docker compose up -d",
    "docker:down": "docker compose down",
    "docker:logs": "docker compose logs -f"
  }
}

## Lessons Learned

### Command Execution Environment
- **Problem**: Commands may behave differently across platforms and environments
- **Impact**: Inconsistent behavior and failed commands
- **Solution**:
  - Always specify full paths in commands
  - Use cross-platform command syntax
  - Document environment requirements
  - Include error handling in scripts

### Package Manager Commands
- **Problem**: Mixed use of package managers can cause confusion
- **Impact**: Inconsistent dependency management and script execution
- **Solution**:
  - Standardize on Bun commands
  - Document command equivalents
  - Use consistent script naming
  - Implement command validation

### Build Tool Commands
- **Problem**: Build tools may require different command formats
- **Impact**: Failed builds and inconsistent outputs
- **Solution**:
  - Document platform-specific command requirements
  - Use cross-platform command syntax
  - Implement command validation
  - Maintain command compatibility documentation



## Tasks

### Extracted Tasks

- [ ] **Problem**: Commands may behave differently across platforms and environments - M1
- [ ] **Impact**: Inconsistent behavior and failed commands - M2
- [ ] **Solution**: - M3
- [ ] Always specify full paths in commands - M4
- [ ] Use cross-platform command syntax - M5
- [ ] Document environment requirements - M6
- [ ] Include error handling in scripts - M7
- [ ] **Problem**: Mixed use of package managers can cause confusion - M8
- [ ] **Impact**: Inconsistent dependency management and script execution - M9
- [ ] **Solution**: - M10
- [ ] Standardize on Bun commands - M11
- [ ] Document command equivalents - M12
- [ ] Use consistent script naming - M13
- [ ] Implement command validation - M14
- [ ] **Problem**: Build tools may require different command formats - M15
- [ ] **Impact**: Failed builds and inconsistent outputs - M16
- [ ] **Solution**: - M17
- [ ] Document platform-specific command requirements - M18
- [ ] Use cross-platform command syntax - M19
- [ ] Implement command validation - M20
- [ ] Maintain command compatibility documentation - M21

### Documentation Tasks

- [ ] Update content accuracy - M1
- [ ] Add code examples - M2
- [ ] Add diagrams/screenshots - M3
- [ ] Review and proofread - M4
- [ ] Add cross-references - M5

