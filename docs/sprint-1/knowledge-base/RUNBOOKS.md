# Beauty CRM Troubleshooting Runbook

## Golden Rule: Check Tilt Logs First

**Before attempting any action (e.g., sending a `curl` request, running a test script, or interacting with a frontend), ALWAYS check the Tilt UI at `http://tilt.localhost`.**

1.  **Verify Service Status:** Ensure the service(s) you are about to interact with are in a `Running` state and not `Error`, `Pending`, or `CrashLoopBackOff`.
2.  **Review Service Logs:** Check the logs for the relevant service(s) to confirm they have started successfully and are ready to accept requests. Look for messages indicating that the server is listening on a port and that database connections have been established.

This simple step will save you a significant amount of time and prevent you from troubleshooting issues that are simply caused by a service not being ready.

---

This document provides guidance for troubleshooting common issues encountered during the development and operation of the Beauty CRM system.

## General Troubleshooting Steps

Before diving into specific scenarios, follow these general steps:

1.  **Check Tilt UI:** Open your browser to `http://tilt.localhost` (or the configured Tilt port).
    *   Observe the status of all services. Are any services in a `Pending`, `Error`, or `CrashLoopBackOff` state?
    *   Review the logs for the affected service(s) for error messages or warnings.
2.  **Verify Docker Containers:**
    *   Run `docker ps` to see currently running containers. Ensure expected service containers are up.
    *   Run `docker ps -a` to see all containers (including exited ones). Check if any critical service has exited unexpectedly.
    *   Use `docker logs <container_id_or_name>` for detailed container logs.
3.  **Check Network Connectivity:**
    *   Use `curl` or `wget` to test health endpoints of services (e.g., `curl http://planner-api.localhost/health`).
    *   From within a running container, try to `ping` or `curl` other services to verify inter-container communication.

---

## Specific Failure Scenarios

### Scenario 1: `appointment-planner-backend` build failure (Prisma import error)

*   **Problem:** During `bun build` for `appointment-planner-backend`, you might see errors like `ReferenceError: prisma is not defined` in `src/infrastructure/outbox-worker.ts` or similar import-related issues.
*   **Cause:** The `outbox-worker.ts` file attempts to use the `prisma` client directly, but it's not properly initialized or exported in a way that the bundler (Bun) can resolve during the build process. This often happens when `prisma` is initialized in `index.ts` and then `outbox-worker.ts` is `require`d or imported without `prisma` being passed to it.
*   **Solution:**
    1.  **Refactor `outbox-worker.ts`:** Modify `services/appointment/appointment-planner-backend/src/infrastructure/outbox-worker.ts` to export a function (e.g., `startOutboxProcessing`) that accepts the `prisma` client and other necessary dependencies as arguments.
        ```typescript
        // services/appointment/appointment-planner-backend/src/infrastructure/outbox-worker.ts
        import type { OutboxManager, EventPublisher } from '@beauty-crm/platform-eventing';

        export function startOutboxProcessing(
          outboxManager: OutboxManager,
          publisher: EventPublisher,
        ) {
          // ... existing setInterval logic ...
        }
        ```
    2.  **Update `index.ts`:** In `services/appointment/appointment-planner-backend/src/index.ts`, ensure `prisma` is initialized first, then create `OutboxManager` and `EventPublisher` instances using this `prisma` client, and finally pass them to the `startOutboxProcessing` function.
        ```typescript
        // services/appointment/appointment-planner-backend/src/index.ts
        import { PrismaClient } from '@prisma/client';
        import { OutboxManager, createPrismaOutboxStorage, createPublisher } from '@beauty-crm/platform-eventing';
        import { startOutboxProcessing } from './infrastructure/outbox-worker'; // Import the new function

        // ... (PrismaClient initialization) ...
        const prisma = new PrismaClient({ /* ... */ });

        // ... (other service setups) ...

        async function startServer() {
          try {
            // ... (existing code) ...

            // Initialize event publisher
            await appointmentEventPublisher.connect();

            // Start the outbox worker
            const outboxStorage = createPrismaOutboxStorage(prisma, 'appointment-planner');
            const outboxPublisher = createPublisher({
              serviceName: 'appointment-planner',
              natsUrl: process.env.NATS_URL,
            });
            const outboxManager = new OutboxManager(outboxStorage, outboxPublisher);
            await outboxPublisher.connect(); // Ensure publisher is connected before starting outbox processing
            startOutboxProcessing(outboxManager, outboxPublisher);

            // ... (rest of startServer function) ...
          } catch (error) {
            console.error('❌ Failed to start server:', error);
            process.exit(1);
          }
        }
        ```
    3.  **Trigger Tilt Rebuild:** Run `tilt trigger <service_name>` (e.g., `tilt trigger appointment_appointment-planner-backend`) to apply the changes and rebuild the Docker image.

### Scenario 2: `appointment-planner-backend` 404 errors from `simulate_e2e_appointment.ts` or `curl`

*   **Problem:** API requests to `http://planner-api.localhost/api/v1/appointments` or `http://beauty-crm.localhost/api/planner/api/v1/appointments` return a 404 status.
*   **Cause:** This usually indicates a routing issue. Either Traefik is not correctly forwarding the request to the `appointment-planner-backend` service, or the backend service itself is not configured to handle the incoming path.
*   **Solution:**
    1.  **Verify Traefik Rules:**
        *   Check `services/appointment/docker-compose.planner.yml` for the `appointment-planner-backend` service's Traefik labels.
        *   Ensure the `traefik.http.routers.appointment-planner.rule` correctly matches the incoming hostname (e.g., `Host(\`planner-api.localhost\`)`) and that the `traefik.http.services.appointment-planner.loadbalancer.server.port` matches the `PORT` exposed by the backend (4000).
        *   If using `PathPrefix` (e.g., `PathPrefix(\`/api/planner\`)`), ensure the backend's Hono app is configured to handle the *full* incoming path, or add a `traefik.http.middlewares.<middleware_name>.stripprefix.prefixes=/api/planner` middleware to strip the prefix before forwarding.
    2.  **Verify Backend API Routes:**
        *   Examine `services/appointment/appointment-planner-backend/src/index.ts` and `services/appointment/appointment-planner-backend/src/presentation/routes/appointmentRoutes.ts`.
        *   Confirm that the Hono app's routes (e.g., `app.route('/api/v1', createAppointmentRoutes(appointmentController));`) correctly align with the expected API paths. If Traefik is *not* stripping the prefix, the backend needs to handle `/api/planner/api/v1/appointments`. If Traefik *is* stripping the prefix, the backend should expect `/api/v1/appointments`.
    3.  **Test Health Endpoint:** Use `curl http://planner-api.localhost/health` (or the appropriate hostname/port) to verify that the service is at least responding to basic requests. A 200 OK indicates the service is running and accessible via Traefik.
    4.  **Trigger Tilt Rebuild:** After any changes to `docker-compose.planner.yml` or backend code, run `tilt trigger appointment-planner-backend` to ensure the changes are applied.

### Scenario 3: `appointment-planner-backend` health check failure (PostgreSQL connection)

*   **Problem:** Tilt logs for `appointment-planner-backend` show `healthcheck: /run/postgresql:5432 - no response` or similar database connection errors.
*   **Cause:** The `appointment-planner-backend` container is attempting to connect to PostgreSQL before the PostgreSQL service is fully initialized and ready to accept connections. This can happen even with `depends_on` and `service_healthy` conditions if the application starts faster than the database.
*   **Solution:**
    1.  **Verify PostgreSQL Health:** Check the Tilt logs for the `postgres` service (`tilt logs postgres`). Ensure it reports "database system is ready to accept connections".
    2.  **Increase `start_period`:** In `services/appointment/docker-compose.planner.yml`, increase the `start_period` for the `appointment-planner-backend`'s health check. This gives the container more time to start up and connect to the database before Tilt marks it as unhealthy.
        ```yaml
        # services/appointment/docker-compose.planner.yml
        services:
          appointment-planner-backend:
            # ...
            healthcheck:
              test: [ "CMD", "wget", "--quiet", "--tries=1", "--spider", "http://127.0.0.1:4000/health" ]
              interval: 30s
              timeout: 10s
              retries: 3
              start_period: 60s # Increase this value, e.g., to 60s or 90s
            # ...
        ```
    3.  **Manual Database Connection Test (from inside container):**
        *   Get the container ID: `docker ps -q -f name=appointment_planner_backend`
        *   Execute a shell inside the container: `docker exec -it <container_id> /bin/bash`
        *   From within the container, try to connect to PostgreSQL using `psql` or `pg_isready`:
            ```bash
            psql -h beauty_crm_postgres -U beauty_crm -d beauty_crm_appointment
            # Or
            pg_isready -h beauty_crm_postgres -U beauty_crm -d beauty_crm_appointment
            ```
            This will provide more specific error messages if the connection fails.
    4.  **Trigger Tilt Rebuild:** Run `tilt trigger appointment-planner-backend` after modifying `docker-compose.planner.yml`.

### Scenario 4: Linting errors preventing builds

*   **Problem:** `biome` linting errors (e.g., `noUselessConstructor`, `noUnusedFunctionParameters`, `noUnusedImports`, `noExplicitAny`, `useNamingConvention`, `noInvalidUseBeforeDeclaration`, `noSvgWithoutTitle`) prevent Docker image builds or local development.
*   **Cause:** Code not adhering to the project's `biome` linting rules.
*   **Solution:**
    1.  **Run `bun run lint` locally:** Execute `bun run lint` in your terminal to get a detailed list of all linting errors and warnings.
    2.  **Address each error:**
        *   **`noUselessConstructor`:** If a constructor is only used for dependency injection, add a `// biome-ignore lint/complexity/noUselessConstructor: Constructor is used for dependency injection.` comment.
        *   **`noUnusedFunctionParameters`:** Prefix unused parameters with an underscore (e.g., `_paramName`).
        *   **`noUnusedImports`:** Remove the unused import statement.
        *   **`noExplicitAny`:** Replace `any` with a more specific type. If `any` is intentionally used (e.g., for mock data or external libraries), add a `// biome-ignore lint/suspicious/noExplicitAny: [Reason]` comment.
        *   **`useNamingConvention`:** Rename variables, properties, or functions to adhere to the specified naming convention (e.g., `snake_case` to `camelCase`).
        *   **`noInvalidUseBeforeDeclaration`:** Reorder variable declarations so they appear before their first use.
        *   **`noSvgWithoutTitle`:** For `<svg>` elements, add a `<title>` element inside the `<svg>` tag to provide an accessible name.
    3.  **Trigger Tilt Rebuild:** After fixing the code, Tilt should automatically detect changes and trigger a rebuild. If not, manually trigger with `tilt trigger <service_name>`.

---

## Implementation Plan (Appointment Creation Eventing)

To ensure the appointment creation eventing works fully, we need to verify the following:

1.  **Frontend (`appointment-planner-frontend`) publishes `appointment.created` event:**
    *   When a user submits the appointment form, the frontend should create a `UnifiedAppointment` object.
    *   This object should be wrapped in an `appointment.created` event and published to NATS.
2.  **Planner Backend (`appointment-planner-backend`) stores and relays events:**
    *   The planner backend should receive the `appointment.created` event.
    *   It should store this event in its outbox (Prisma `AppointmentOutbox` table).
    *   The `OutboxRelayer` should pick up this event from the outbox and publish it to NATS.
3.  **Management Backend (`appointment-management-backend`) subscribes and syncs:**
    *   The management backend should have a NATS subscriber listening for `appointment.created` events.
    *   Upon receiving the event, it should parse the `UnifiedAppointment` data.
    *   It should then sync this appointment data into its own database (Prisma `Appointment` table).

**Verification Steps:**

1.  **Run `bun run simulate_e2e_appointment.ts`:** This script simulates the entire flow and checks for database entries in both planner and management backends.
2.  **Monitor Tilt Logs:** Pay close attention to the logs of `appointment-planner-frontend`, `appointment-planner-backend`, and `appointment-management-backend` in the Tilt UI (`http://tilt.localhost`). Look for:
    *   Frontend: "Published event..."
    *   Planner Backend: "Outbox: Storing event...", "Published event...", "Outbox entry found..."
    *   Management Backend: "Received: appointment.created", "Appointment synced successfully (created in DB)"
3.  **Manual Database Check (if simulation fails):**
    *   Connect to the PostgreSQL database for `beauty_crm_appointment`.
    *   Check the `AppointmentOutbox` table in the planner backend's schema for unprocessed events.
    *   Check the `Appointment` table in the management backend's schema for the newly created appointment.

---



## Tasks

### Extracted Tasks

- [ ] Observe the status of all services. Are any services in a `Pending`, `Error`, or `CrashLoopBackOff` state? - M1
- [ ] Review the logs for the affected service(s) for error messages or warnings. - M2
- [ ] Run `docker ps` to see currently running containers. Ensure expected service containers are up. - M3
- [ ] Run `docker ps -a` to see all containers (including exited ones). Check if any critical service has exited unexpectedly. - M4
- [ ] Use `docker logs <container_id_or_name>` for detailed container logs. - M5
- [ ] Use `curl` or `wget` to test health endpoints of services (e.g., `curl http://planner-api.localhost/health`). - M6
- [ ] From within a running container, try to `ping` or `curl` other services to verify inter-container communication. - M7
- [ ] **Problem:** During `bun build` for `appointment-planner-backend`, you might see errors like `ReferenceError: prisma is not defined` in `src/infrastructure/outbox-worker.ts` or similar import-related issues. - M8
- [ ] **Cause:** The `outbox-worker.ts` file attempts to use the `prisma` client directly, but it's not properly initialized or exported in a way that the bundler (Bun) can resolve during the build process. This often happens when `prisma` is initialized in `index.ts` and then `outbox-worker.ts` is `require`d or imported without `prisma` being passed to it. - M9
- [ ] **Solution:** - M10
- [ ] **Problem:** API requests to `http://planner-api.localhost/api/v1/appointments` or `http://beauty-crm.localhost/api/planner/api/v1/appointments` return a 404 status. - M11
- [ ] **Cause:** This usually indicates a routing issue. Either Traefik is not correctly forwarding the request to the `appointment-planner-backend` service, or the backend service itself is not configured to handle the incoming path. - M12
- [ ] **Solution:** - M13
- [ ] Check `services/appointment/docker-compose.planner.yml` for the `appointment-planner-backend` service's Traefik labels. - M14
- [ ] Ensure the `traefik.http.routers.appointment-planner.rule` correctly matches the incoming hostname (e.g., `Host(\`planner-api.localhost\`)`) and that the `traefik.http.services.appointment-planner.loadbalancer.server.port` matches the `PORT` exposed by the backend (4000). - M15
- [ ] If using `PathPrefix` (e.g., `PathPrefix(\`/api/planner\`)`), ensure the backend's Hono app is configured to handle the *full* incoming path, or add a `traefik.http.middlewares.<middleware_name>.stripprefix.prefixes=/api/planner` middleware to strip the prefix before forwarding. - M16
- [ ] Examine `services/appointment/appointment-planner-backend/src/index.ts` and `services/appointment/appointment-planner-backend/src/presentation/routes/appointmentRoutes.ts`. - M17
- [ ] Confirm that the Hono app's routes (e.g., `app.route('/api/v1', createAppointmentRoutes(appointmentController));`) correctly align with the expected API paths. If Traefik is *not* stripping the prefix, the backend needs to handle `/api/planner/api/v1/appointments`. If Traefik *is* stripping the prefix, the backend should expect `/api/v1/appointments`. - M18
- [ ] **Problem:** Tilt logs for `appointment-planner-backend` show `healthcheck: /run/postgresql:5432 - no response` or similar database connection errors. - M19
- [ ] **Cause:** The `appointment-planner-backend` container is attempting to connect to PostgreSQL before the PostgreSQL service is fully initialized and ready to accept connections. This can happen even with `depends_on` and `service_healthy` conditions if the application starts faster than the database. - M20
- [ ] **Solution:** - M21
- [ ] Get the container ID: `docker ps -q -f name=appointment_planner_backend` - M22
- [ ] Execute a shell inside the container: `docker exec -it <container_id> /bin/bash` - M23
- [ ] From within the container, try to connect to PostgreSQL using `psql` or `pg_isready`: - M24
- [ ] **Problem:** `biome` linting errors (e.g., `noUselessConstructor`, `noUnusedFunctionParameters`, `noUnusedImports`, `noExplicitAny`, `useNamingConvention`, `noInvalidUseBeforeDeclaration`, `noSvgWithoutTitle`) prevent Docker image builds or local development. - M25
- [ ] **Cause:** Code not adhering to the project's `biome` linting rules. - M26
- [ ] **Solution:** - M27
- [ ] **`noUselessConstructor`:** If a constructor is only used for dependency injection, add a `// biome-ignore lint/complexity/noUselessConstructor: Constructor is used for dependency injection.` comment. - M28
- [ ] **`noUnusedFunctionParameters`:** Prefix unused parameters with an underscore (e.g., `_paramName`). - M29
- [ ] **`noUnusedImports`:** Remove the unused import statement. - M30
- [ ] **`noExplicitAny`:** Replace `any` with a more specific type. If `any` is intentionally used (e.g., for mock data or external libraries), add a `// biome-ignore lint/suspicious/noExplicitAny: [Reason]` comment. - M31
- [ ] **`useNamingConvention`:** Rename variables, properties, or functions to adhere to the specified naming convention (e.g., `snake_case` to `camelCase`). - M32
- [ ] **`noInvalidUseBeforeDeclaration`:** Reorder variable declarations so they appear before their first use. - M33
- [ ] **`noSvgWithoutTitle`:** For `<svg>` elements, add a `<title>` element inside the `<svg>` tag to provide an accessible name. - M34
- [ ] When a user submits the appointment form, the frontend should create a `UnifiedAppointment` object. - M35
- [ ] This object should be wrapped in an `appointment.created` event and published to NATS. - M36
- [ ] The planner backend should receive the `appointment.created` event. - M37
- [ ] It should store this event in its outbox (Prisma `AppointmentOutbox` table). - M38
- [ ] The `OutboxRelayer` should pick up this event from the outbox and publish it to NATS. - M39
- [ ] The management backend should have a NATS subscriber listening for `appointment.created` events. - M40
- [ ] Upon receiving the event, it should parse the `UnifiedAppointment` data. - M41
- [ ] It should then sync this appointment data into its own database (Prisma `Appointment` table). - M42
- [ ] Frontend: "Published event..." - M43
- [ ] Planner Backend: "Outbox: Storing event...", "Published event...", "Outbox entry found..." - M44
- [ ] Management Backend: "Received: appointment.created", "Appointment synced successfully (created in DB)" - M45
- [ ] Connect to the PostgreSQL database for `beauty_crm_appointment`. - M46
- [ ] Check the `AppointmentOutbox` table in the planner backend's schema for unprocessed events. - M47
- [ ] Check the `Appointment` table in the management backend's schema for the newly created appointment. - M48

### Documentation Tasks

- [ ] Update content accuracy - M1
- [ ] Add code examples - M2
- [ ] Add diagrams/screenshots - M3
- [ ] Review and proofread - M4
- [ ] Add cross-references - M5

