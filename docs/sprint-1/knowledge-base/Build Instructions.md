# Building and Running the Clipboard History App

## Prerequisites

- **macOS**: 12.0 or later (Monterey+)
- **Xcode**: 14.0 or later
- **Swift**: 5.7 or later

## Quick Start

### Option 1: Using Xcode (Recommended)

1. **Create a new macOS project:**
   - Open Xcode
   - File → New → Project
   - Choose "macOS" → "App"
   - Product Name: "ClipboardHistory"
   - Interface: SwiftUI
   - Language: Swift

2. **Replace the default code:**
   - Delete all files in the project except `Info.plist`
   - Add the `ClipboardHistory.swift` file to your project
   - Replace the contents with the provided code

3. **Configure app permissions:**
   - In your project settings, go to "Signing & Capabilities"
   - Add "App Sandbox" capability (if not already present)
   - Under App Sandbox, enable:
     - "Incoming Connections (Server)"
     - "Outgoing Connections (Client)"

4. **Build and run:**
   - Press `Cmd + R` or click the "Run" button
   - The app will appear in your menu bar as a clipboard icon

### Option 2: Command Line (Advanced)

1. **Create a Swift package:**
   ```bash
   mkdir ClipboardHistory
   cd ClipboardHistory
   swift package init --type executable
   ```

2. **Replace main.swift:**
   - Copy the `ClipboardHistory.swift` content to `Sources/ClipboardHistory/main.swift`

3. **Update Package.swift:**
   ```swift
   // swift-tools-version: 5.7
   import PackageDescription

   let package = Package(
       name: "ClipboardHistory",
       platforms: [.macOS(.v12)],
       targets: [
           .executableTarget(name: "ClipboardHistory")
       ]
   )
   ```

4. **Build and run:**
   ```bash
   swift run
   ```

## App Features

### Core Functionality
- **Real-time monitoring**: Automatically detects clipboard changes
- **Multiple data types**: Supports text, images, files, and URLs
- **Search capability**: Find items by content
- **Type filtering**: Filter by data type (text, image, file, URL)
- **Persistent storage**: Maintains history between app restarts

### User Interface
- **Menu bar integration**: Lives in your menu bar for easy access
- **Clean SwiftUI interface**: Modern, native macOS design
- **Hover interactions**: Action buttons appear on item hover
- **Keyboard shortcuts**: Navigate with arrow keys, copy with Enter

### Data Management
- **50-item limit**: Automatically manages storage by keeping recent items
- **Duplicate prevention**: Avoids storing identical consecutive items
- **Manual cleanup**: Clear individual items or entire history
- **Timestamp tracking**: Shows when each item was copied

## Usage Instructions

1. **Launch the app**: Click the clipboard icon in your menu bar
2. **Copy anything**: The app automatically captures clipboard content
3. **Browse history**: Scroll through your recent clipboard items
4. **Search items**: Use the search bar to find specific content
5. **Filter by type**: Click type buttons to show only specific content types
6. **Copy items**: Click any item or press Enter to copy it again
7. **Delete items**: Hover over items and click the trash icon
8. **Pause monitoring**: Click the pause button to temporarily stop monitoring

## Troubleshooting

### Common Issues

**App doesn't appear in menu bar:**
- Check that the app is running in Activity Monitor
- Restart the app
- Make sure you're looking in the right section of the menu bar

**Clipboard monitoring not working:**
- Ensure the app has necessary permissions
- Check if monitoring is paused (play/pause button in header)
- Restart the app

**Items not saving between sessions:**
- Check app permissions for file access
- Verify UserDefaults is accessible (not in strict sandbox mode)

### Performance Considerations

- The app polls the clipboard every 0.5 seconds
- Memory usage is minimal due to the 50-item limit
- CPU usage is negligible when no clipboard changes occur

## Customization

### Modifying the Item Limit
Change the `maxItems` property in `ClipboardManager`:
```swift
private let maxItems = 100  // Change from 50 to 100
```

### Adjusting Polling Interval
Modify the timer interval in `startMonitoring()`:
```swift
timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true)  // Change from 0.5 to 1.0 seconds
```

### Adding New Content Types
Extend the `ClipboardType` enum and update the content detection logic in `addCurrentClipboardContent()`.

## Security and Privacy

- **Local storage only**: All data stays on your device
- **No network access**: The app doesn't connect to the internet
- **Standard permissions**: Uses only standard macOS clipboard APIs
- **Sandbox compatible**: Can run in sandboxed mode for distribution

## Distribution

### For Personal Use
- Build and run directly from Xcode
- Copy the built app to your Applications folder

### For Distribution
- Enable hardened runtime
- Code sign with your developer certificate
- Notarize with Apple (for distribution outside App Store)
- Consider App Store distribution for wider reach

## Tasks

### Extracted Tasks

- [ ] **macOS**: 12.0 or later (Monterey+) - M1
- [ ] **Xcode**: 14.0 or later - M2
- [ ] **Swift**: 5.7 or later - M3
- [ ] File → New → Project - M4
- [ ] Choose "macOS" → "App" - M5
- [ ] Product Name: "ClipboardHistory" - M6
- [ ] Interface: SwiftUI - M7
- [ ] Language: Swift - M8
- [ ] Delete all files in the project except `Info.plist` - M9
- [ ] Add the `ClipboardHistory.swift` file to your project - M10
- [ ] Replace the contents with the provided code - M11
- [ ] In your project settings, go to "Signing & Capabilities" - M12
- [ ] Add "App Sandbox" capability (if not already present) - M13
- [ ] Under App Sandbox, enable: - M14
- [ ] "Incoming Connections (Server)" - M15
- [ ] "Outgoing Connections (Client)" - M16
- [ ] Press `Cmd + R` or click the "Run" button - M17
- [ ] The app will appear in your menu bar as a clipboard icon - M18
- [ ] Copy the `ClipboardHistory.swift` content to `Sources/ClipboardHistory/main.swift` - M19
- [ ] **Real-time monitoring**: Automatically detects clipboard changes - M20
- [ ] **Multiple data types**: Supports text, images, files, and URLs - M21
- [ ] **Search capability**: Find items by content - M22
- [ ] **Type filtering**: Filter by data type (text, image, file, URL) - M23
- [ ] **Persistent storage**: Maintains history between app restarts - M24
- [ ] **Menu bar integration**: Lives in your menu bar for easy access - M25
- [ ] **Clean SwiftUI interface**: Modern, native macOS design - M26
- [ ] **Hover interactions**: Action buttons appear on item hover - M27
- [ ] **Keyboard shortcuts**: Navigate with arrow keys, copy with Enter - M28
- [ ] **50-item limit**: Automatically manages storage by keeping recent items - M29
- [ ] **Duplicate prevention**: Avoids storing identical consecutive items - M30
- [ ] **Manual cleanup**: Clear individual items or entire history - M31
- [ ] **Timestamp tracking**: Shows when each item was copied - M32
- [ ] Check that the app is running in Activity Monitor - M33
- [ ] Restart the app - M34
- [ ] Make sure you're looking in the right section of the menu bar - M35
- [ ] Ensure the app has necessary permissions - M36
- [ ] Check if monitoring is paused (play/pause button in header) - M37
- [ ] Restart the app - M38
- [ ] Check app permissions for file access - M39
- [ ] Verify UserDefaults is accessible (not in strict sandbox mode) - M40
- [ ] The app polls the clipboard every 0.5 seconds - M41
- [ ] Memory usage is minimal due to the 50-item limit - M42
- [ ] CPU usage is negligible when no clipboard changes occur - M43
- [ ] **Local storage only**: All data stays on your device - M44
- [ ] **No network access**: The app doesn't connect to the internet - M45
- [ ] **Standard permissions**: Uses only standard macOS clipboard APIs - M46
- [ ] **Sandbox compatible**: Can run in sandboxed mode for distribution - M47
- [ ] Build and run directly from Xcode - M48
- [ ] Copy the built app to your Applications folder - M49
- [ ] Enable hardened runtime - M50
- [ ] Code sign with your developer certificate - M51
- [ ] Notarize with Apple (for distribution outside App Store) - M52
- [ ] Consider App Store distribution for wider reach - M53

### Frontend Tasks

- [ ] Implement UI components - M1
- [ ] Add form validation - M2
- [ ] Add error handling - M3
- [ ] Add loading states - M4
- [ ] Add unit tests - M5
- [ ] Add accessibility features - M6

