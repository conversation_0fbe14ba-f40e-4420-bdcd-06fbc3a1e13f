---
description: 
globs: *
alwaysApply: false
---
# Nx + Bun + Vite Monorepo - Roo Code IDE Rules

## Package Manager Rules

### ❌ NEVER Use
- `npm install` or `npm i`
- `npm run` or `npm start`
- `yarn install` or `yarn add`
- `yarn start` or `yarn build`
- Direct package.json modifications for dependencies

### ✅ ALWAYS Use
- `bun install` for installing dependencies
- `bun add <package>` for adding dependencies
- `bun remove <package>` for removing dependencies
- `bun run <script>` for running scripts
- `bun x <command>` for running executables

## Nx Generator Rules

### ❌ NEVER Manually Create
- package.json files for new projects
- tsconfig.json files
- Vite configuration files
- Build configuration files
- Project structure directories
- Workspace configuration files

### ✅ ALWAYS Use Nx Generators
```bash
# Generate new applications
bunx nx g @nx/vite:app <app-name>

# Generate new libraries
bunx nx g @nx/vite:lib <lib-name>

# Generate React components (if using React)
bunx nx g @nx/react:component <component-name>

# Generate Node applications
bunx nx g @nx/node:app <app-name>

# Generate configurations
bunx nx g @nx/vite:configuration <project-name>
```

## Project Structure Rules

### Application Generation
```bash
# Frontend applications
bunx nx g @nx/vite:app frontend-app --bundler=vite --e2eTestRunner=playwright

# React applications with Vite
bunx nx g @nx/react:app react-app --bundler=vite --e2eTestRunner=playwright

# Node.js applications
bunx nx g @nx/node:app backend-api --bundler=vite
```

### Library Generation
```bash
# Shared UI library
bunx nx g @nx/react:lib shared-ui --bundler=vite --buildable

# Utility library
bunx nx g @nx/js:lib shared-utils --bundler=vite --buildable

# Feature library
bunx nx g @nx/react:lib feature-auth --bundler=vite --buildable
```

## Development Workflow Rules

### Running Applications
```bash
# Serve applications
bunx nx serve <app-name>

# Build applications
bunx nx build <app-name>

# Test applications
bunx nx test <app-name>

# Lint applications
bunx nx lint <app-name>
```

### Dependency Management
```bash
# Add dependencies to specific project
bunx nx g @nx/js:library-dependency --project=<project-name> --dependency=<package-name>

# Add dev dependencies
bun add -d <package-name>

# Add dependencies to workspace root
bun add <package-name>
```

## Code Organization Rules

### Import Rules
- Use relative imports for same project files: `./components/Button`
- Use library imports for cross-project dependencies: `@myorg/shared-ui`
- Leverage Nx's path mapping in tsconfig.base.json

### Project Boundaries
- Enforce project boundaries using Nx's dependency rules
- Use `bunx nx g @nx/eslint:workspace-rule` for custom boundary rules
- Follow the principle: apps import from libs, libs don't import from apps

## Configuration Rules

### Vite Configuration
- Use Nx Vite plugin: `@nx/vite`
- Generate Vite configs via: `bunx nx g @nx/vite:configuration <project-name>`
- Extend base Vite configuration when needed

### TypeScript Configuration
- Use Nx-generated tsconfig files
- Extend from workspace tsconfig.base.json
- Generate project-specific configs via Nx generators

## Build and Deployment Rules

### Build Commands
```bash
# Build specific project
bunx nx build <project-name>

# Build all affected projects
bunx nx affected:build

# Build with production optimization
bunx nx build <project-name> --configuration=production
```

### Testing Rules
```bash
# Run tests for specific project
bunx nx test <project-name>

# Run all affected tests
bunx nx affected:test

# Run e2e tests
bunx nx e2e <app-name>-e2e
```

## Migration and Updates

### Nx Updates
```bash
# Update Nx workspace
bunx nx migrate latest

# Apply migrations
bunx nx migrate --run-migrations
```

### Plugin Management
```bash
# Add Nx plugins
bunx nx add @nx/vite
bunx nx add @nx/react
bunx nx add @nx/node
```

## Performance Rules

### Caching
- Leverage Nx's computation caching
- Use `bunx nx reset` to clear cache when needed
- Configure cacheable operations in nx.json

### Affected Commands
```bash
# Only run tasks on affected projects
bunx nx affected:build
bunx nx affected:test
bunx nx affected:lint
```

## Code Quality Rules

### Linting and Formatting
```bash
# Generate ESLint configuration
bunx nx g @nx/eslint:configuration

# Run linting
bunx nx lint <project-name>

# Run linting on affected projects
bunx nx affected:lint
```

## Environment Rules

### Environment Variables
- Use Nx's environment variable handling
- Configure via project.json or workspace.json
- Use Vite's env variable conventions (VITE_)

## Documentation and Resources

### Official Documentation
- **Nx Documentation**: https://nx.dev/getting-started/intro
- **Nx Vite Plugin**: https://nx.dev/packages/vite
- **Nx React Plugin**: https://nx.dev/packages/react
- **Nx Node Plugin**: https://nx.dev/packages/node

### Handbooks and Guides
- **Nx Workspace Setup**: https://nx.dev/getting-started/tutorials/react-monorepo-tutorial
- **Vite with Nx Guide**: https://nx.dev/recipes/vite
- **Monorepo Best Practices**: https://nx.dev/concepts/more-concepts/monorepo-nx-enterprise

### CLI References
- **Nx CLI Commands**: https://nx.dev/packages/nx/documents/nx
- **Nx Generators**: https://nx.dev/packages/nx/documents/generate
- **Nx Executors**: https://nx.dev/packages/nx/documents/run

### Cheatsheets
- **Nx Commands Cheatsheet**: https://nx.dev/recipes/nx-console/console-command-palette
- **Nx Migration Guide**: https://nx.dev/packages/nx/documents/migrate

## Quick Reference Commands

### Initial Setup
```bash
# Create new Nx workspace with Bun
bunx create-nx-workspace@latest myorg --preset=apps --bundler=vite --packageManager=bun

# Install dependencies
bun install
```

### Daily Development
```bash
# Generate new feature
bunx nx g @nx/react:lib feature-dashboard --bundler=vite

# Serve application
bunx nx serve my-app

# Run tests
bunx nx test my-app

# Build for production
bunx nx build my-app --configuration=production
```

### Maintenance
```bash
# Update workspace
bunx nx migrate latest && bunx nx migrate --run-migrations

# Clean and rebuild
bunx nx reset && bunx nx run-many --target=build

# Check project graph
bunx nx graph
```

## Anti-Patterns to Avoid

❌ **Don't**:
- Manually edit generated configuration files without using Nx generators
- Use npm/yarn commands in Bun projects
- Create cross-project dependencies without proper imports
- Bypass Nx's caching mechanisms
- Manually manage TypeScript paths in tsconfig files

✅ **Do**:
- Use Nx generators for all project scaffolding
- Leverage Bun for all package management operations
- Follow Nx's project boundary rules
- Use affected commands for CI/CD optimization
- Regularly update Nx and its plugins

## Lessons Learned

### Nx Native Dependencies
- **Problem**: Nx requires platform-specific native modules that can cause installation issues
- **Impact**: Build and development workflow disruptions
- **Solution**:
  - Use package.json resolutions to enforce correct native module versions
  - Match nx version with @nx/js and other nx packages
  - Document platform-specific requirements
  - Implement proper error handling for native module failures

### Dependency Management
- **Problem**: Complex dependency trees in monorepos can lead to version conflicts
- **Impact**: Build failures, runtime errors, and inconsistent behavior
- **Solution**:
  - Use workspace-level package management
  - Maintain consistent versions across all packages
  - Implement strict version control in package.json
  - Regular dependency audits and updates

### Build Tool Configuration
- **Problem**: Build tools may require different configurations per platform
- **Impact**: Inconsistent builds across development environments
- **Solution**:
  - Platform-specific build configurations
  - Clear documentation of build requirements
  - Automated build verification
  - Platform-specific troubleshooting guides

---

*Remember: Always use `bunx nx` prefix for Nx commands in Bun environments, and leverage Nx generators instead of manual configuration to maintain consistency and take advantage of Nx's powerful tooling.*


## Tasks

### Extracted Tasks

- [ ] `npm install` or `npm i` - M1
- [ ] `npm run` or `npm start` - M2
- [ ] `yarn install` or `yarn add` - M3
- [ ] `yarn start` or `yarn build` - M4
- [ ] Direct package.json modifications for dependencies - M5
- [ ] `bun install` for installing dependencies - M6
- [ ] `bun add <package>` for adding dependencies - M7
- [ ] `bun remove <package>` for removing dependencies - M8
- [ ] `bun run <script>` for running scripts - M9
- [ ] `bun x <command>` for running executables - M10
- [ ] package.json files for new projects - M11
- [ ] tsconfig.json files - M12
- [ ] Vite configuration files - M13
- [ ] Build configuration files - M14
- [ ] Project structure directories - M15
- [ ] Workspace configuration files - M16
- [ ] Use relative imports for same project files: `./components/Button` - M17
- [ ] Use library imports for cross-project dependencies: `@myorg/shared-ui` - M18
- [ ] Leverage Nx's path mapping in tsconfig.base.json - M19
- [ ] Enforce project boundaries using Nx's dependency rules - M20
- [ ] Use `bunx nx g @nx/eslint:workspace-rule` for custom boundary rules - M21
- [ ] Follow the principle: apps import from libs, libs don't import from apps - M22
- [ ] Use Nx Vite plugin: `@nx/vite` - M23
- [ ] Generate Vite configs via: `bunx nx g @nx/vite:configuration <project-name>` - M24
- [ ] Extend base Vite configuration when needed - M25
- [ ] Use Nx-generated tsconfig files - M26
- [ ] Extend from workspace tsconfig.base.json - M27
- [ ] Generate project-specific configs via Nx generators - M28
- [ ] Leverage Nx's computation caching - M29
- [ ] Use `bunx nx reset` to clear cache when needed - M30
- [ ] Configure cacheable operations in nx.json - M31
- [ ] Use Nx's environment variable handling - M32
- [ ] Configure via project.json or workspace.json - M33
- [ ] Use Vite's env variable conventions (VITE_) - M34
- [ ] **Nx Documentation**: https://nx.dev/getting-started/intro - M35
- [ ] **Nx Vite Plugin**: https://nx.dev/packages/vite - M36
- [ ] **Nx React Plugin**: https://nx.dev/packages/react - M37
- [ ] **Nx Node Plugin**: https://nx.dev/packages/node - M38
- [ ] **Nx Workspace Setup**: https://nx.dev/getting-started/tutorials/react-monorepo-tutorial - M39
- [ ] **Vite with Nx Guide**: https://nx.dev/recipes/vite - M40
- [ ] **Monorepo Best Practices**: https://nx.dev/concepts/more-concepts/monorepo-nx-enterprise - M41
- [ ] **Nx CLI Commands**: https://nx.dev/packages/nx/documents/nx - M42
- [ ] **Nx Generators**: https://nx.dev/packages/nx/documents/generate - M43
- [ ] **Nx Executors**: https://nx.dev/packages/nx/documents/run - M44
- [ ] **Nx Commands Cheatsheet**: https://nx.dev/recipes/nx-console/console-command-palette - M45
- [ ] **Nx Migration Guide**: https://nx.dev/packages/nx/documents/migrate - M46
- [ ] Manually edit generated configuration files without using Nx generators - M47
- [ ] Use npm/yarn commands in Bun projects - M48
- [ ] Create cross-project dependencies without proper imports - M49
- [ ] Bypass Nx's caching mechanisms - M50
- [ ] Manually manage TypeScript paths in tsconfig files - M51
- [ ] Use Nx generators for all project scaffolding - M52
- [ ] Leverage Bun for all package management operations - M53
- [ ] Follow Nx's project boundary rules - M54
- [ ] Use affected commands for CI/CD optimization - M55
- [ ] Regularly update Nx and its plugins - M56
- [ ] **Problem**: Nx requires platform-specific native modules that can cause installation issues - M57
- [ ] **Impact**: Build and development workflow disruptions - M58
- [ ] **Solution**: - M59
- [ ] Use package.json resolutions to enforce correct native module versions - M60
- [ ] Match nx version with @nx/js and other nx packages - M61
- [ ] Document platform-specific requirements - M62
- [ ] Implement proper error handling for native module failures - M63
- [ ] **Problem**: Complex dependency trees in monorepos can lead to version conflicts - M64
- [ ] **Impact**: Build failures, runtime errors, and inconsistent behavior - M65
- [ ] **Solution**: - M66
- [ ] Use workspace-level package management - M67
- [ ] Maintain consistent versions across all packages - M68
- [ ] Implement strict version control in package.json - M69
- [ ] Regular dependency audits and updates - M70
- [ ] **Problem**: Build tools may require different configurations per platform - M71
- [ ] **Impact**: Inconsistent builds across development environments - M72
- [ ] **Solution**: - M73
- [ ] Platform-specific build configurations - M74
- [ ] Clear documentation of build requirements - M75
- [ ] Automated build verification - M76
- [ ] Platform-specific troubleshooting guides - M77

### Documentation Tasks

- [ ] Update content accuracy - M1
- [ ] Add code examples - M2
- [ ] Add diagrams/screenshots - M3
- [ ] Review and proofread - M4
- [ ] Add cross-references - M5

