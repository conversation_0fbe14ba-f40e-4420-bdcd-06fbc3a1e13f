# Building and Running the Clipboard History App

## Prerequisites

- **macOS**: 12.0 or later (Monterey+)
- **Xcode**: 14.0 or later
- **Swift**: 5.7 or later

## Quick Start

### Option 1: Using Xcode (Recommended)

1. **Create a new macOS project:**
   - Open Xcode
   - File → New → Project
   - Choose "macOS" → "App"
   - Product Name: "ClipboardHistory"
   - Interface: SwiftUI
   - Language: Swift

2. **Replace the default code:**
   - Delete all files in the project except `Info.plist`
   - Add the `ClipboardHistory.swift` file to your project
   - Replace the contents with the provided code

3. **Configure app permissions:**
   - In your project settings, go to "Signing & Capabilities"
   - Add "App Sandbox" capability (if not already present)
   - Under App Sandbox, enable:
     - "Incoming Connections (Server)"
     - "Outgoing Connections (Client)"

4. **Build and run:**
   - Press `Cmd + R` or click the "Run" button
   - The app will appear in your menu bar as a clipboard icon

### Option 2: Command Line (Advanced)

1. **Create a Swift package:**
   ```bash
   mkdir ClipboardHistory
   cd ClipboardHistory
   swift package init --type executable
   ```

2. **Replace main.swift:**
   - Copy the `ClipboardHistory.swift` content to `Sources/ClipboardHistory/main.swift`

3. **Update Package.swift:**
   ```swift
   // swift-tools-version: 5.7
   import PackageDescription

   let package = Package(
       name: "ClipboardHistory",
       platforms: [.macOS(.v12)],
       targets: [
           .executableTarget(name: "ClipboardHistory")
       ]
   )
   ```

4. **Build and run:**
   ```bash
   swift run
   ```

## App Features

### Core Functionality
- **Real-time monitoring**: Automatically detects clipboard changes
- **Multiple data types**: Supports text, images, files, and URLs
- **Search capability**: Find items by content
- **Type filtering**: Filter by data type (text, image, file, URL)
- **Persistent storage**: Maintains history between app restarts

### User Interface
- **Menu bar integration**: Lives in your menu bar for easy access
- **Clean SwiftUI interface**: Modern, native macOS design
- **Hover interactions**: Action buttons appear on item hover
- **Keyboard shortcuts**: Navigate with arrow keys, copy with Enter

### Data Management
- **50-item limit**: Automatically manages storage by keeping recent items
- **Duplicate prevention**: Avoids storing identical consecutive items
- **Manual cleanup**: Clear individual items or entire history
- **Timestamp tracking**: Shows when each item was copied

## Usage Instructions

1. **Launch the app**: Click the clipboard icon in your menu bar
2. **Copy anything**: The app automatically captures clipboard content
3. **Browse history**: Scroll through your recent clipboard items
4. **Search items**: Use the search bar to find specific content
5. **Filter by type**: Click type buttons to show only specific content types
6. **Copy items**: Click any item or press Enter to copy it again
7. **Delete items**: Hover over items and click the trash icon
8. **Pause monitoring**: Click the pause button to temporarily stop monitoring

## Troubleshooting

### Common Issues

**App doesn't appear in menu bar:**
- Check that the app is running in Activity Monitor
- Restart the app
- Make sure you're looking in the right section of the menu bar

**Clipboard monitoring not working:**
- Ensure the app has necessary permissions
- Check if monitoring is paused (play/pause button in header)
- Restart the app

**Items not saving between sessions:**
- Check app permissions for file access
- Verify UserDefaults is accessible (not in strict sandbox mode)

### Performance Considerations

- The app polls the clipboard every 0.5 seconds
- Memory usage is minimal due to the 50-item limit
- CPU usage is negligible when no clipboard changes occur

## Customization

### Modifying the Item Limit
Change the `maxItems` property in `ClipboardManager`:
```swift
private let maxItems = 100  // Change from 50 to 100
```

### Adjusting Polling Interval
Modify the timer interval in `startMonitoring()`:
```swift
timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true)  // Change from 0.5 to 1.0 seconds
```

### Adding New Content Types
Extend the `ClipboardType` enum and update the content detection logic in `addCurrentClipboardContent()`.

## Security and Privacy

- **Local storage only**: All data stays on your device
- **No network access**: The app doesn't connect to the internet
- **Standard permissions**: Uses only standard macOS clipboard APIs
- **Sandbox compatible**: Can run in sandboxed mode for distribution

## Distribution

### For Personal Use
- Build and run directly from Xcode
- Copy the built app to your Applications folder

### For Distribution
- Enable hardened runtime
- Code sign with your developer certificate
- Notarize with Apple (for distribution outside App Store)
- Consider App Store distribution for wider reach