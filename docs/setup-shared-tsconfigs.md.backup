# Setup Shared TypeScript Configs

This script generates and manages TypeScript configuration files for the Beauty CRM project, specifically for shared packages and shared DDD layers.

## Overview

The script performs the following tasks:

- Writes a base TypeScript configuration file (`tsconfig.base.json`) for the project.
- Processes each package in the `shared-platform-engineering` directory to generate a `tsconfig.json` based on a default configuration and additional project references derived from the package's dependencies.
- Processes each directory in the `shared-product-engineering` (if available) to generate a `tsconfig.json` using a default configuration.
- Ensures that the `include` and `exclude` arrays in the generated configuration files are formatted on a single line for consistency and readability.

## File Structure 

- **scripts/setup-shared-tsconfigs.js**: Main script file that sets up TypeScript configuration files.
- **shared-platform-engineering/**: Directory containing shared platform engineering packages. Each package may have its own `package.json` and will receive a generated `tsconfig.json`.
- **shared-product-engineering/**: Directory containing shared product engineering packages. Each package may have its own `package.json` and will receive a generated `tsconfig.json`.
- **docs/setup-shared-tsconfigs.md**: This documentation file.

## Detailed Function Descriptions

### readJSON(filePath)

- **Purpose**: Reads a JSON file from disk and parses its content.
- **Behavior**:
  - Returns the parsed object if the file exists.
  - Returns an empty object if the file is not found (`ENOENT` error).
  - Throws an error for other file read issues.

### buildReferences(packageName, packageJson)

- **Purpose**: Builds TypeScript project reference paths based on a package's dependencies.
- **Behavior**:
  - Combines dependencies from `dependencies`, `devDependencies`, and `peerDependencies` in the package's `package.json`.
  - Filters dependencies that start with `@beauty-crm/`.
  - Checks if the referenced package exists in the `shared-platform-engineering` or `shared-product-engineering` directory.
  - Returns an array of references (e.g., `[ { path: "../otherPackage" } ]`). If the dependency does not exist, it logs a warning and skips it.

### createTSConfig(packageName)

- **Purpose**: Constructs a TypeScript configuration object for a given package.
- **Behavior**:
  - Reads the package's `package.json` file.
  - Generates project references using `buildReferences`.
  - Starts with a default configuration (`DEFAULT_TS_CONFIG`).
  - Attaches any valid references to the configuration if they exist.

### writeTsConfigFile(tsconfigPath, tsConfig)

- **Purpose**: Writes a tsconfig object to a file with pretty JSON formatting.
- **Behavior**:
  - Converts the configuration object to a formatted JSON string.
  - Uses regular expressions to modify the `include` and `exclude` arrays so they are output on a single line.
  - Writes the modified content to the provided file path.

### processPackages()

- **Purpose**: Processes all packages in the `shared-platform-engineering` directory to create individual tsconfig files.
- **Behavior**:
  - Iterates over each directory in `shared-platform-engineering` (skipping `tsconfig.base.json`).
  - Generates a tsconfig object for each package using `createTSConfig`.
  - Writes the tsconfig to the package directory and logs a confirmation message.

### processSharedDddLayers()

- **Purpose**: Processes shared DDD layers and creates a tsconfig file for each, if available.
- **Behavior**:
  - Checks for the presence of the `shared-ddd-layers` directory.
  - Iterates over each subdirectory (ignoring `tsconfig.base.json`).
  - Writes the default tsconfig (from `DEFAULT_TS_CONFIG`) to each directory and logs a confirmation message.
  - Logs a warning if the directory does not exist or an error occurs.

### setupTypeScriptConfigs()

- **Purpose**: Orchestrates the entire TypeScript configuration setup process.
- **Behavior**:
  - Writes the base tsconfig file using `BASE_TSCONFIG_CONTENT` in the `shared-platform-engineering` directory.
  - Invokes `processPackages()` and `processSharedDddLayers()` to generate configurations for packages and DDD layers respectively.
  - Logs completion messages or any encountered errors.

## Execution

To execute the script, run the following command from the project root:

  node scripts/setup-shared-tsconfigs.js

This command will:

- Create or overwrite the base tsconfig file at `shared-platform-engineering/tsconfig.base.json`.
- Generate `tsconfig.json` files for each package in the `shared-platform-engineering` directory.
- Generate `tsconfig.json` files for each product engineering package in the `shared-product-engineering` directory, if it exists.

## Additional Notes

- The formatting change for the `include` and `exclude` arrays ensures that these arrays are compact and readable, regardless of how many entries they have.
- The script logs warnings for any missing dependencies or issues encountered during the setup process.

## Conclusion

This script is a critical part of the Beauty CRM project's setup process, ensuring that TypeScript is properly configured across all shared modules. It simplifies configuration management and enforces consistent formatting across all generated tsconfig files. 