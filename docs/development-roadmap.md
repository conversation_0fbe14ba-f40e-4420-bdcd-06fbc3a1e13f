## Distributed Systems Reliability Patterns (Onboarding)

All engineers must review and understand the approved reliability patterns before contributing reliability-critical code.

- See [docs/reliability-patterns.md](./reliability-patterns.md) for approved patterns, templates, and code examples.
- External resources:
  - [Microsoft Cloud Design Patterns](https://learn.microsoft.com/en-us/azure/architecture/patterns/)
  - [AWS Well-Architected Framework](https://aws.amazon.com/architecture/well-architected/)
- Example PRs: (add links as they are created) 

## Tasks

### Extracted Tasks

- [ ] See [docs/reliability-patterns.md](./reliability-patterns.md) for approved patterns, templates, and code examples. - M1
- [ ] External resources: - M2
- [ ] [Microsoft Cloud Design Patterns](https://learn.microsoft.com/en-us/azure/architecture/patterns/) - M3
- [ ] [AWS Well-Architected Framework](https://aws.amazon.com/architecture/well-architected/) - M4
- [ ] Example PRs: (add links as they are created) - M5

### Documentation Tasks

- [ ] Update content accuracy - M1
- [ ] Add code examples - M2
- [ ] Add diagrams/screenshots - M3
- [ ] Review and proofread - M4
- [ ] Add cross-references - M5

