# TypeScript Configuration Generator Documentation

## Overview
The TypeScript Configuration Generator is a tool designed to automatically generate and maintain consistent TypeScript configurations across a monorepo with multiple packages. It supports a DDD (Domain-Driven Design) architecture with platform engineering, product engineering, and DDD layers.

## Architecture

### Directory Structure
```
root/
├── shared-platform-engineering/    # Platform-level packages
├── shared-product-engineering/     # Product-level packages
└── shared-ddd-layers/             # DDD architectural layers
```

### Core Components

1. **Generator**
   - Location: `shared-platform-engineering/shared-tsconfig-generator/`
   - Purpose: Generates TypeScript configurations for all packages
   - Entry Point: `bin/generate-tsconfig.ts`

2. **CLI Integration**
   - Package: `computing-lifecycle`
   - Command: `generate -t` or `generate --tsconfig`

## Implementation Guide

### 1. Package Structure

```typescript
// Package structure for shared-tsconfig-generator
{
  "name": "@beauty-crm/platform-tsconfig-generator",
  "version": "1.0.0",
  "main": "dist/index.js",
  "types": "dist/index.d.ts",
  "bin": {
    "generate-tsconfig": "./bin/generate-tsconfig.js"
  },
  "scripts": {
    "build": "tsc --build",
    "clean": "rimraf dist",
    "generate": "tsx ./bin/generate-tsconfig.ts"
  }
}
```

### 2. Core Types

```typescript
// Types for configuration
interface TSConfigReference {
  path: string;
}

interface TSConfigOptions {
  compilerOptions: {
    target: string;
    module: string;
    moduleResolution: string;
    esModuleInterop: boolean;
    strict: boolean;
    skipLibCheck: boolean;
    forceConsistentCasingInFileNames: boolean;
    declaration: boolean;
    outDir?: string;
    rootDir?: string;
    [key: string]: unknown;
  };
  include: string[];
  exclude: string[];
  references?: TSConfigReference[];
}

interface GeneratorConfig {
  baseConfig: TSConfigOptions;
  workspaceRoot: string;
  platformEngineeringDir: string;
  productEngineeringDir: string;
  dddLayersDir: string;
}
```

### 3. Base Configuration

```typescript
const BASE_CONFIG = {
  compilerOptions: {
    target: 'ES2020',
    module: 'NodeNext',
    moduleResolution: 'NodeNext',
    esModuleInterop: true,
    strict: true,
    skipLibCheck: true,
    forceConsistentCasingInFileNames: true,
    declaration: true,
  },
  include: ['src/**/*'],
  exclude: ['node_modules', 'dist', '__tests__/**/*'],
};
```

### 4. Generator Implementation Steps

1. **Project References Resolution**
   - Read package.json dependencies
   - Map dependencies to local packages
   - Create relative path references

2. **TSConfig Generation**
   - Create base configuration
   - Add project-specific settings
   - Include project references
   - Write configuration files

3. **Directory Processing**
   - Process platform engineering packages
   - Process product engineering packages
   - Process DDD layers
   - Skip non-existent directories

### 5. Usage

#### CLI Usage
```bash
# Using npm package directly
npx computing-lifecycle generate -t

# Using aliases
npx @lfc generate -t
npx @lxc generate -t
npx @lcx generate -t
```

#### Integration with Build Process
```json
{
  "scripts": {
    "setup:tsconfigs": "npm run build -w @beauty-crm/platform-tsconfig-generator && npx computing-lifecycle generate -t"
  }
}
```

## Generated Configuration Structure

### Base Configuration (tsconfig.base.json)
```json
{
  "compilerOptions": {
    "target": "ES2020",
    "module": "NodeNext",
    "moduleResolution": "NodeNext",
    "esModuleInterop": true,
    "strict": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "declaration": true
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist", "__tests__/**/*"]
}
```

### Package Configuration (package/tsconfig.json)
```json
{
  "compilerOptions": {
    "target": "ES2020",
    "module": "NodeNext",
    "moduleResolution": "NodeNext",
    "esModuleInterop": true,
    "strict": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "declaration": true,
    "outDir": "./dist",
    "rootDir": "./src"
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist"],
  "references": [
    { "path": "../dependent-package" }
  ]
}
```

## Key Features

1. **Automatic Project References**
   - Analyzes package dependencies
   - Creates TypeScript project references
   - Maintains correct build order

2. **Consistent Configuration**
   - Enforces standard compiler options
   - Maintains uniform include/exclude patterns
   - Supports modular project structure

3. **DDD Support**
   - Handles layered architecture
   - Supports cross-layer dependencies
   - Maintains architectural boundaries

4. **Monorepo Integration**
   - Works with workspace packages
   - Supports multiple package directories
   - Handles complex dependency graphs

## Error Handling

The generator implements robust error handling:
1. Validates directory existence
2. Checks package.json presence
3. Verifies JSON syntax
4. Reports detailed error messages

## Best Practices

1. **Project Structure**
   - Keep packages in designated directories
   - Maintain consistent naming conventions
   - Follow monorepo structure

2. **Dependencies**
   - Use workspace references
   - Maintain clean dependency graph
   - Avoid circular dependencies

3. **Configuration**
   - Don't modify generated files directly
   - Use base config for shared settings
   - Keep project-specific settings minimal

## Troubleshooting

Common issues and solutions:

1. **Missing References**
   - Verify package.json dependencies
   - Check directory structure
   - Ensure correct package names

2. **Build Errors**
   - Validate base configuration
   - Check project references
   - Verify compiler options

3. **Generator Failures**
   - Check file permissions
   - Verify directory access
   - Validate JSON syntax 